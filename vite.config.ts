import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import tailwindcss from "@tailwindcss/vite";
import vueDevTools from 'vite-plugin-vue-devtools'
import viteCompression from 'vite-plugin-compression';

import { assetpackPlugin } from "./scripts/assetpack-vite-plugin";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production' || mode === 'pre'

  return {
    base: './',
    plugins: [
      assetpackPlugin(),
      vueJsx(),
      vue(),
      tailwindcss(),
      vueDevTools(),
      viteCompression({
        algorithm: 'brotliCompress', // 使用 Brotli 压缩
        ext: '.br', // 压缩后的文件扩展名
        threshold: 1024, // 仅压缩大于 1KB 的文件
        filter: /\.(js|mjs|json|css|html)$/i, // 仅压缩这些类型的文件
        deleteOriginFile: false, // 压缩后保留原始文件
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ['vue', 'vue-i18n', 'pinia'], // 将 Vue 相关库打包成单独的 chunk
            spine: ['@esotericsoftware/spine-pixi-v8'], // 将 Spine 相关库打包成单独的 chunk
            pixi: ['pixi.js', '@pixi/sound'], // 将 PixiJS 相关库打包成单独的 chunk
          }
        }
      },
      minify: 'terser', // 确保使用 Terser 进行压缩
      terserOptions: {
        compress: {
          drop_console: isProduction, // 移除 console 语句
          drop_debugger: true, // 移除 debugger 语句
        },
        format: {
          comments: false, // 移除注释
        },
      },
    },
  }
})
