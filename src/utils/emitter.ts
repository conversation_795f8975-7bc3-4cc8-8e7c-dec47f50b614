import mitt, { type Emitter } from 'mitt';
import protoRoot from '@/proto/index';

export type GameEvent = {
  /** socket假死 */
  'socket-no-response': void;

  /** socket reconnect */
  'socket-reconnect': void;

  /** socket 重连失败 */
  'socket-reconnect-fail': void;

  /** 心跳 */
  'heartbeat': DecodedMessage<protoRoot.HeartbeatResponse>;

  /** 登录 req */
  'login-req': protoRoot.LoginRequest;
  /** token登录 req */
  'login-token-req': protoRoot.LoginTokenRequest;
  /** 登录 res */
  'login-res': DecodedMessage<protoRoot.LoginResponse>;

  /** 推送 被挤下线 */
  'push-conflict': DecodedMessage<protoRoot.loginConflictRequest>,

  /** 推送 被踢 */
  'push-kick-out': DecodedMessage<protoRoot.SessionTerminatedRequest>,

  /** 进入房间 req */
  'enter-room-req': void;
  /** 进入房间 res */
  'enter-room-res': DecodedMessage<protoRoot.EnterRoomResponse>;

  /** 退出房间 req */
  'exit-room-req': void;
  /** 退出房间 res */
  'exit-room-res': DecodedMessage<protoRoot.ExitRoomResponse>;

  /** 开始游戏、go req */
  'user-bet-req': EmitterCallbackData<protoRoot.StartGameRequest, number | undefined>;
  /** 开始游戏、go res */
  'user-bet-res': DecodedMessage<protoRoot.StartGameResponse>;

  /** full cash out req */
  'user-full-cashout-req': EmitterCallbackData<protoRoot.CashOutRequest, number | undefined>;
  /** full cash out res */
  'user-full-cashout-res': DecodedMessage<protoRoot.CashOutResponse>;

  /** part cashout req */
  'user-part-cashout-req': EmitterCallbackData<protoRoot.PartCashOutRequest, number | undefined>;
  /** part cashout res */
  'user-part-cashout-res': DecodedMessage<protoRoot.PartCashOutResponse>;

  /** 玩家投注历史记录 req */
  'user-bet-history-req': EmitterCallbackData<protoRoot.GetHistoryRequest, number | undefined>;
  /** 玩家投注历史记录 res */
  'user-bet-history-res': DecodedMessage<protoRoot.GetHistoryResponse>;

  /** 修改头像 req */
  'user-update-avatar-req': EmitterCallbackData<protoRoot.ChangeAvatarRequest, number | undefined>;
  /** 修改头像 res */
  'user-update-avatar-res': DecodedMessage<protoRoot.ChangeAvatarResponse>;

  /** game play */
  'handle-game-play': void;
  /** cashout */
  'handle-game-full-cashout': void;
  /** part cashout */
  'handle-game-part-cashout': void;
  /** bet amount error */
  'handle-bet-amount-error': void;
  /** notification */
  'handle-game-notification': string;
  /** 播放结算动画 */
  'play-award-animation': number;
};

const emitter: Emitter<GameEvent> = mitt<GameEvent>();

export default emitter;