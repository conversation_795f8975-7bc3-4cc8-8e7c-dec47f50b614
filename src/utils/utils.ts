export const glog = (...args: any[]) => {
  const [msg, ...attrs] = args;
  console.log(
      `%c[glog][${getTimeText()}] ===>%c${msg}`,
      'background-color: #254D70; color: #4CAF50; font-weight: bold; padding: 2px 4px; border-radius: 2px;',
      'background-color: #F2F2F2; color: #000000; font-weight: bold; padding: 2px 4px; border-radius: 2px;',
      ...attrs
  );
}

export const padStart = (maxlength: number, padString: string) => {
  return (num: number | string) => {
    const str = String(num);
    return str.padStart(maxlength, padString);
  }
}

export const getTimeText = (time?: number) => {
  const d =  time ? new Date(time) : new Date();
  const pad2 = padStart(2, '0');
  const pad3 = padStart(3, '0');
  const h = pad2(d.getHours());
  const m = pad2(d.getMinutes());
  const s = pad2(d.getSeconds());
  const ms = pad3(d.getMilliseconds());
  return `${h}:${m}:${s}.${ms}`;
}

export const getCountDownText = (ms: number) => {
  if (ms <= 0) return "00:00:00";

  const pad2 = padStart(2, '0');

  const m = Math.ceil(ms / 1000);
  const hours = Math.floor(m / 3600);
  const minutes = Math.floor(m % 3600 / 60);
  const seconds = Math.floor(m % 60);

  return `${ pad2(hours) }:${ pad2(minutes) }:${ pad2(seconds) }`;
}

export const formatAmountNoRounding = (amount: any, len = 2, isFixed = false) => {
  if (typeof amount !== 'number') {
    amount = Number(amount);
    if (isNaN(amount)) amount = 0;
  }

  // 将数字转换为字符串，保留所有小数位
  let str = amount.toString();
  // 分割整数和小数部分
  let [integerPart, decimalPart = ''] = str.split('.');

  // 截断小数位数
  let formattedDecimal = decimalPart.slice(0, len);

  if (isFixed) {
    // 处理小数部分
    formattedDecimal = formattedDecimal.padEnd(len, '0');
  } else {
    // 抹掉尾部的0
    formattedDecimal = formattedDecimal.replace(/0+$/, '');
  }

  // 返回格式化结果
  return formattedDecimal ? `${integerPart}.${formattedDecimal}` : integerPart;
}

export const formatAmount = (amount: any, len = 2, isFixed = false, isSymbol = false, split = true) => {
  const formatted = formatAmountNoRounding(amount, len, isFixed);

  const symbol = formatted >= 0 && isSymbol ? '+' : '';

  if (!split) return `${symbol}${formatted}`;

  // 拆分整数与小数部分
  const [integerPart, decimalPart] = formatted.split('.');
  // 为整数部分添加千分位空格
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g,' ')
  return decimalPart ? `${symbol}${formattedInteger}.${decimalPart}` : `${symbol}${formattedInteger}`;
}

export const amountProfitColor = (amount: number | string) => {
  if (Number(amount) >= 0) return '#00c82b';
  return '#ff003f';
}

export const roundNumber = (num: number) => {
  return Math.round(num * 1e4) / 1e4;
}

export const clamp = (value: number, min: number, max: number) => {
  return Math.max(min, Math.min(value, max));
}

export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

export const isMobileDevice = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobileUA = /mobile|android|iphone|ipod|blackberry|windows phone/i.test(userAgent);
  const isSmallScreen = window.innerWidth <= 768 || window.screen.width <= 768;
  return isMobileUA || isSmallScreen || isTouchDevice();
}

/**
 * 生成手动控制Promise
 */
export const createManualPromise = <T = void>() => {
  let resolve!: (value: T | PromiseLike<T>) => void;
  let reject!: (reason?: any) => void;

  const promise = new Promise<T>((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return { promise, resolve, reject };
}
