import { readonly } from "vue";

export enum TwistElement {
  WATER = 1,   // 水
  EARTH = 2,   // 土
  FIRE  = 3,   // 火
  AIR   = 4,   // 云
  DEATH = 5,   // 骷髅
}

export type CircleElement = TwistElement.FIRE | TwistElement.EARTH | TwistElement.WATER;

export type TCircleElement<T> = { [K in CircleElement]: T };

export interface ITwistSlotData {
  name: TwistElement
  posY: number
}

export const StepMap: CircleElement[] = [
  TwistElement.WATER,
  TwistElement.EARTH,
  TwistElement.FIRE
]

export const TwistSoundName: { [key in TwistElement]: string } = {
  [TwistElement.FIRE] : 'fire',
  [TwistElement.EARTH]: 'earth',
  [TwistElement.WATER]: 'water',
  [TwistElement.AIR]  : 'wind',
  [TwistElement.DEATH]: 'skull',
}

export const TwistElementName: { [key in TwistElement]: string } = {
  [TwistElement.FIRE] : 'fire',
  [TwistElement.EARTH]: 'earth',
  [TwistElement.WATER]: 'water',
  [TwistElement.AIR]  : 'air',
  [TwistElement.DEATH]: 'death',
};

export const TwistElemantAnim: { [key in TwistElement]: string } = {
  [TwistElement.FIRE]: 'fire',
  [TwistElement.EARTH]: 'earth',
  [TwistElement.WATER]: 'water',
  [TwistElement.AIR]: 'air',
  [TwistElement.DEATH]: 'losing',
}

// Progress coefficients
export const TwistCoeffs: TCircleElement<number[]> = {
  [TwistElement.FIRE]: [3.9, 12.5, 28, 52, 85, 133, 200, -1],
  [TwistElement.EARTH]: [2.5, 7.7, 16, 27.5, 44, 20.5],
  [TwistElement.WATER]: [1.55, 4.85, 10, 7]
};

// fire bonus coeffs
export const TwistFireBonus: number[] = [100, 200, 300, 400, 500];

// Radii for circular text and separators
export const TextRadii: TCircleElement<number> = {
  [TwistElement.FIRE]: 311,
  [TwistElement.EARTH]: 244,
  [TwistElement.WATER]: 177,
};

export const SeparatorRadii: TCircleElement<number> = {
  [TwistElement.FIRE]: 314,
  [TwistElement.EARTH]: 247,
  [TwistElement.WATER]: 179,
};

export const Colors: TCircleElement<string[]> = {
  [TwistElement.FIRE]: ['#E062FF', '#E465F0', '#EF6EC8', '#FB78A1', '#FF7B91', '#FB78A0', '#EF6EC8', '#E465F0', '#E062FF'],
  [TwistElement.EARTH]: ['#6FFE49', '#92FE5C', '#DCFF84', '#ECFF8D', '#DCFF85', '#98FE5F', '#6FFE49'],
  [TwistElement.WATER]: ['#69D2FF', '#A3E8FF', '#CFF8FF', '#A4E8FF', '#69D2FF'],
};

export const FilledColors: TCircleElement<string> = {
  [TwistElement.FIRE]: '#83275a',
  [TwistElement.EARTH]: '#366f25',
  [TwistElement.WATER]: '#245a81',
};

export const BonusColors: TCircleElement<string> = {
  [TwistElement.FIRE]: '#EF2C80',
  [TwistElement.EARTH]: '#F2EA37',
  [TwistElement.WATER]: '#32DAEE',
};

export const SeparatorColors: TCircleElement<string> = {
  [TwistElement.FIRE]: '#36434B',
  [TwistElement.EARTH]: '#366F25',
  [TwistElement.WATER]: '#36434B',
};

// 步长配置
export const StepConfigs: { threshold: number; step: number }[] = [
  { threshold: 1, step: 0.1 },
  { threshold: 5, step: 1 },
  { threshold: 25, step: 5 },
  { threshold: 100, step: 25 },
  { threshold: 500, step: 50 },
  { threshold: 1000, step: 100 },
  { threshold: 5000, step: 500 },
  { threshold: 20000, step: 1000 },
  { threshold: 100000, step: 5000 },
];

export const defaultGameData: GameData = readonly({
  outcome: TwistElement.FIRE,
  betAmount: 100,
  totalWinAmount: 0,
  lastWinAmount: 0,
  coeff: 0,
  bonusCoeff: 0,
  progressFire: 0,
  progressEarth: 0,
  progressWater: 0,
  isFinished: true,
  isCashout: false,
  isCanCashOut: false,
});