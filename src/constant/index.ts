export const BaseLayout = {
  mobileViewportX: 360,
  mobileViewportY: 595,
  desktopViewportX: 1500,
  desktopViewportY: 840,
  breakpointAspectRatio: .95,
  mobileHeaderHeight: 45,
  mobileHeaderMarginBottom: -1,
  desktopHeaderHeight: 49,
  desktopHeaderMarginBottom: -1,
}

export const GameLayout = {
  portraitConfig: {
    width: 780,
    height: 1200,
    centerYOffset: 100,
    balanceOffset: { x: 230, y: 100 },
  },
  landscapeConfig: {
    width: 1080,
    height: 1080,
    centerYOffset: 80,
    balanceOffset: { x: 100, y: 120 },
  },
  mobileViewportX: 360,
  mobileViewportY: 595,
  desktopViewportX: 1500,
  desktopViewportY: 840,
  breakpointAspectRatio: .95,
}

export enum SYSTEM_ERROR_CODE {
  /** 无错误 */
  NONE = 0,

  /** 系统异常，但仍可以操作，手动尝试重连 */
  // 网络错误
  NETWORK_ERROR = 1,
  // 异地登录被挤下线
  LOGOUT = 2,
  // 长时间不操作
  TIMEOUT = 3,

  /** 系统停止运行，无法继续操作 */
  // 校验游戏地址失败
  VALID_GAME_HREF_FAIL = 11,
  // 被踢出房间
  KICK_OUT = 12,
  // 系统维护
  MAINTAIN = 13,
}

export enum SocketEventCode {
  /** 推送 心跳 */
  HEARTBEAT = 0,

  /** 登录 Req */
  LOGIN_REQ = 101,
  /** token登录 Req */
  LOGIN_TOKEN_REQ = 103,
  /** 登录 Res */
  LOGIN_RES = 102,

  /** 推送 被挤下线 */
  PUSH_CONFLICT = 104,
  /** 推送 被踢 */
  PUSH_KICK_OUT = 105,

  /** 进入房间 Req */
  ENTER_ROOM_REQ = 21901,
  /** 进入房间 Res */
  ENTER_ROOM_RES = 21902,

  /** 退出房间 Req */
  EXIT_ROOM_REQ = 21903,
  /** 退出房间 Res */
  EXIT_ROOM_RES = 21904,

  /** bet Req */
  USER_BET_REQ = 21905,
  /** bet Res */
  USER_BET_RES = 21906,

  /** full cashout Req */
  USER_FULL_CASHOUT_REQ = 21907,
  /** full cashout Res */
  USER_FULL_CASHOUT_RES = 21908,

  /** cash out Req */
  USER_PART_CASHOUT_REQ = 21909,
  /** cash out Res */
  USER_PART_CASHOUT_RES = 21910,

  /** 玩家投注历史记录 Req */
  USER_BET_HISTORY_REQ = 21911,
  /** 玩家投注历史记录 Res */
  USER_BET_HISTORY_RES = 21912,

  /** 修改头像 Req */
  USER_UPDATE_NICKNAME_REQ = 21913,
  /** 修改头像 Res */
  USER_UPDATE_NICKNAME_RES = 21914,

  /** 修改头像 Req */
  USER_UPDATE_AVATAR_REQ = 21915,
  /** 修改头像 Res */
  USER_UPDATE_AVATAR_RES = 21916,
}

export const CURRENCY_SYMBOL_MAP: { [key: string]: string } = {
  "USD": "$",
  "UAH": "₴",
  "EUR": "€",
  "RUB": "₽",
  "INR": "₹",
  "BRL": "R$",
  "KZT": "₸",
  "VND": "₫",
  "UZS": "So'm",
  "IDR": "Rp",
  "AZN": "₼",
  "KGS": "с",
  "BDT": "৳",
  "PKR": "Rs",
  "PEN": "P$",
  "DEMO": "D",
  "SC": "SC",
  "GC": "GC",
  "CLP": "C$",
  "COP": "C$",
  "MXN": "M$",
  "CAD": "C$",
  "CZK": "Kč",
  "ZAR": "R",
  "BGN": "лв",
  "DKK": "kr",
  "RON": "lei",
  "AUD": "A$",
  "NZD": "N$",
  "CHF": "₣",
  "NOK": "kr",
  "HUF": "Ft",
  "TRY": "₺",
  "PLN": "zł",
  "GBP": "\xa3",
  "NGN": "₦",
  "ZMW": "ZK",
  "TZS": "TSh",
  "GHS": "GH\xa2",
  "XOF": "CFA",
  "VEF": "Bs",
  "VES": "VES",
  "SGD": "S$",
  "HRK": "kn",
  "KRW": "₩",
  "KES": "KES",
  "BAM": "BA",
  "RSD": "DIN",
  "AED": "د.إ",
  "AFN": "؋",
  "ALL": "L",
  "AMD": "֏",
  "ANG": "ƒ",
  "AOA": "Kz",
  "AWG": "AWG",
  "BBD": "Bds$",
  "BHD": "BD",
  "BIF": "BIF",
  "BMD": "$",
  "BND": "B$",
  "BOV": "BOV",
  "BSD": "B$",
  "BTN": "BTN",
  "BWP": "P",
  "BYN": "BYN",
  "BZD": "$",
  "CDF": "FC",
  "CHW": "CHW",
  "CLF": " CLF",
  "CUP": "CUP",
  "CVE": "CVE",
  "DJF": "Fdj",
  "DOP": "DOP",
  "DZD": "دج",
  "EGP": "E\xa3",
  "ERN": "Nkf",
  "ETB": "ETB",
  "FJD": "FJ$",
  "FKP": "FK\xa3",
  "GEL": "GEL",
  "GMD": "D",
  "GNF": "FG",
  "GYD": "G$",
  "HKD": "HK$",
  "HNL": "HNL",
  "HTG": "HTG",
  "ILS": "₪",
  "IQD": "ع.د",
  "IRR": "IRR",
  "ISK": "ISK",
  "JMD": "J$",
  "JOD": "JOD",
  "KHR": "KHR",
  "KMF": "CF",
  "KPW": "₩",
  "KWD": "د.ك",
  "KYD": "CI",
  "LAK": "₭",
  "LBP": "L\xa3",
  "LKR": "රා",
  "LRD": "L$",
  "LSL": "LSL",
  "LYD": "ل.د",
  "MAD": "د.م.",
  "MDL": "L",
  "MGA": "MGA",
  "MKD": "den",
  "MMK": "KHR",
  "MNT": "₮",
  "MOP": "$",
  "MRU": "UM",
  "MUR": "Re",
  "MVR": "Rf",
  "MWK": "MK",
  "MYR": "RM",
  "MZN": "MT",
  "NAD": "N$",
  "NPR": "NPR",
  "OMR": "OMR",
  "PGK": "K",
  "PHP": "₱",
  "QAR": "QAR",
  "CNY": "\xa5",
  "RWF": "RF",
  "SAR": "SAR",
  "SBD": "SI$",
  "SCR": "SCR",
  "SDG": "SDG",
  "SHP": "SHP",
  "SLE": "Le",
  "SLL": "Le",
  "SOS": "SOS",
  "SRD": "$",
  "SSP": "SSP",
  "SYP": "SYP",
  "SZL": "SZL",
  "GIP": "\xa3",
  "UGX": "UGX",
  "GMS": "GMS",
  "JPY": "\xa5",
}