<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useGameStore } from '@/stores';
import { formatAmount } from '@/utils/utils';

import ResizableBlock from '@/components/ResizableBlock/index.vue';
import CurrencySymbol from '../CurrencySymbol.vue';

const { gameData } = storeToRefs(useGameStore());
</script>

<template>
  <ResizableBlock
    :desktop="[0, 0]"
    :mobile="[336, 36]"
    :margin-mobile="[12, 0, 0, 0]"
    :full-width-mobile="true"
  >
    <div class="last-win-wrapper">
      <div>{{ $t('twist.LAST_WIN') }}:</div>
      <div class="last-win-amount">
        <span>{{ formatAmount(gameData.lastWinAmount, 2, false, false, false) }}</span>
        <CurrencySymbol class="fill-white size-[12px]" />
      </div>
    </div>
  </ResizableBlock>
</template>

<style lang="scss" scoped>
  .last-win-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: rgb(255, 255, 255);
    background: rgba(255, 255, 255, 0.05);
    gap: 4px;
    font-family: "Roboto Flex", sans-serif;
    font-size: 12px;
    font-weight: 500;
    line-height: 22px;
    text-transform: uppercase;
    transform: translateZ(0px);
  }

  .last-win-amount {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    opacity: 1;
    transform: translateY(0px);
    transition: opacity 200ms, transform 200ms;
  }
</style>