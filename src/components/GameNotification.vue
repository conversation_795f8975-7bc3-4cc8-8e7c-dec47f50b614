<script setup lang="ts">
import { onUnmounted, ref } from "vue";
import { useTimer } from "@/composables/useTimer.ts"
import emitter from "@/utils/emitter.ts";

const { start, clean  } = useTimer();

const show = ref(false);
const message = ref('');

const handleShow = (msg: string) => {
  message.value = msg;
  show.value = true;
  start(() => show.value = false, 3000);
}

emitter.on('handle-game-notification', handleShow);

onUnmounted(() => {
  clean();
  emitter.off('handle-game-notification', handleShow);
})
</script>

<template>
  <div v-if="show" class="notification w-[359px] h-[68px] absolute z-[999] top-[48px] left-1/2 transform-[translateX(-50%)] flex items-center gap-[16px] py-[8px] pr-[16px] pl-[8px] rounded-[10px] bg-[#1c1c21] leading-[1.15]">
    <div class="w-[4px] h-[52px] rounded-[10px] border-3 border-[#579dff]"></div>
    <div class="py-[4px] text-[16px] font-['Roboto_Flex',sans-serif] font-medium leading-[22px] text-white">
      {{ message }}
    </div>
  </div>
</template>

<style scoped lang="scss">
@keyframes show {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.notification {
  box-shadow: rgba(0, 0, 0, 0.03) 0 8px 10px 1px, rgba(0, 0, 0, 0.07) 0 3px 14px 2px, rgba(0, 0, 0, 0.06) 0 5px 5px -3px;
  animation: 0.3s ease-out 0s 1 normal none running show;
}
</style>