<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from "pinia";
import { useMainStore, useUserStore } from "@/stores";
import { useFullScreen } from "@/composables/useFullScreen.ts";
import { getAssetUrl } from "@/AssetsManager";
import protoRoot from "@/proto/index";

import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'

import GSwitch from '@/components/ui/GSwitch/index.vue';
import MyBetHistory from '@/components/modals/MyBetHistory.vue';
import HistoryRoundFairInfo from "@/components/modals/HistoryRoundFairInfo.vue";
import HowToPlay from '@/components/modals/HowToPlay.vue';
import GameRules from '@/components/modals/GameRules.vue';
import ProvablyFairSettings from '@/components/modals/ProvablyFairSettings.vue';
import ChooseGameAvatar from '@/components/modals/ChooseGameAvatar.vue';

import logo from '@/assets/image/logo.png';

const { toggleFullScreen } = useFullScreen();
const { avatar, nickname } = storeToRefs(useUserStore());
const { soundEnable, musicEnable, spaceEnable, isMaterial } = storeToRefs(useMainStore());

const showMyBetHistoryModal = ref(false);
const showRoundFairModal = ref(false);
const showHowToPlayModal = ref(false);
const showRulesModal = ref(false);
const showFairSettingsModal = ref(false);
const showAvatarModal = ref(false);

const roundInfo = ref<protoRoot.GameHistory>({} as protoRoot.GameHistory);

const handleFullScreen = () => {
  toggleFullScreen(document.body);
};
const handleShowMyBetHistoryModal = (close: () => void) => {
  showMyBetHistoryModal.value = true;
  close();
}
const handleShowHowToPlayModal = (close?: () => void) => {
  showHowToPlayModal.value = true;
  close?.();
}
const handleShowRulesModal = (close?: () => void) => {
  showHowToPlayModal.value = false;
  showRulesModal.value = true;
  close && close();
}
const handleShowFairSettingsModal = (close: () => void) => {
  showFairSettingsModal.value = true;
  close();
}
const handleShowAvatarModal = (close: () => void) => {
  showAvatarModal.value = true;
  close();
}

const handleShowRoundInfo = (info: protoRoot.GameHistory) => {
  roundInfo.value = info;
  showMyBetHistoryModal.value = false;
  showRoundFairModal.value = true;
}

const handleShowFairSettings = () => {
  showFairSettingsModal.value = true;
  showRoundFairModal.value = false;
}

const handleLogoClick = () => {
  window.open('https://inout.games/');
}
</script>

<template>
  <header
    class="
      flex items-center justify-between text-white z-[1]
      x:h-[49px] x:m-[24px] y:h-[45px] y:m-[16px] x-max-h500:h-[45px] x-max-h500:m-[16px]
    "
  >
    <div
      class="
        flex items-baseline justify-between gap-[6px] h-[27px] transition-all duration-200
        min-w1240:h-[34px] x-max-w1253:h-[24px]
      "
    >
      <img :src="logo" class="size-full y-max-w330:size-[95%] object-scale-down pointer-events-none mr-[100%]" />
    </div>

    <div class="relative flex items-center gap-[16px] max-w414:gap-[8px]">
      <Popover v-slot="{ open }" class="relative flex items-center">
        <PopoverButton class="outline-none" @keydown.prevent>
          <div
            class="
              group
              rounded-full p-[11px] size-[62px] cursor-pointer bg-black/40 hover:bg-white/20
              x-max-w1253:size-[48px] y:size-[36px]
            "
            :class="{ 'open-menu': open }"
          >
            <button
              class="
                relative p-0 m-[4px] border-none bg-none size-[32px] transition-all duration-500 cursor-pointer
                x-max-w1253:scale-[0.7] x-max-w1253:top-[-6px] x-max-w1253:left-[-7px]
                y:scale-[0.5] y:top-[-12px] y:left-[-13px]
              "
              data-testid="burger-menu"
            >
              <span class="menu-line top-[4px] left-0 rounded-l-[2px] group-[.open-menu]:rotate-45 group-[.open-menu]:left-[3px] group-[.open-menu]:top-[8px]"></span>
              <span class="menu-line top-[4px] right-0 rounded-r-[2px] group-[.open-menu]:-rotate-45 group-[.open-menu]:right-[3px] group-[.open-menu]:top-[8px]"></span>
              <span class="menu-line top-[calc(50%-1px)] left-0 rounded-l-[2px] group-[.open-menu]:-left-1/2 group-[.open-menu]:opacity-0"></span>
              <span class="menu-line top-[calc(50%-1px)] right-0 rounded-r-[2px] group-[.open-menu]:-right-1/2 group-[.open-menu]:opacity-0"></span>
              <span class="menu-line top-[26px] left-0 rounded-l-[2px] group-[.open-menu]:-rotate-45 group-[.open-menu]:left-[3px]  group-[.open-menu]:top-[18px]"></span>
              <span class="menu-line top-[26px] right-0 rounded-r-[2px] group-[.open-menu]:rotate-45 group-[.open-menu]:right-[3px] group-[.open-menu]:top-[18px]"></span>
            </button>
          </div>
        </PopoverButton>
        <transition
            enter-active-class="transition duration-200 ease-out"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
        >
          <PopoverPanel v-slot="{ close }" class="absolute right-0 top-[68px] y:top-[40px] x-max-h500:top-0 x-max-h500:right-[54px] x-max-h428:top-[-40px] x-max-h428:right-[38px] x-max-h428:scale-80 x-max-h344:top-[-80px] x-max-h344:right-[10px] x-max-h344:scale-60 z-10 w-[270px]">
            <div class="overflow-hidden rounded-[12px] bg-[#1b1d21] p-[16px] border border-[rgba(255,255,255,0.1)] shadow-[0_12px_12px_rgba(0,0,0,0.1)] tracking-[-0.3px]">
              <div class="flex items-center gap-[12px] mb-[16px] font-[Roboto_Flex]">
                <img v-if="avatar" :src="getAssetUrl(`av-${avatar}.png`)" alt="avatar" class="w-[24px]">
                <div v-else class="size-[24px] bg-[#D9D9D9] rounded-full shrink-0"></div>
                <div class="flex-1 text-sm text-white font-bold truncate leading-[17px]">{{ nickname }}</div>
                <div class="text-[#58c637] hover:text-[#9bc030] text-xs font-medium cursor-pointer leading-[15px]" @click="handleShowAvatarModal(close)">
                  {{ $t('global.menu.change_avatar') }}
                </div>
              </div>

              <div class="flex flex-col gap-[16px] text-[14px] leading-[14px]">
                <div class="panel-list-item cursor-pointer flex items-center" @click="handleFullScreen">
                  <svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[16px]">
                    <path d="M14 6L19 1M19 1H14M19 1V6M6 6L1 1M1 1L1 6M1 1L6 1M6 14L1 19M1 19H6M1 19L1 14M14 14L19 19M19 19V14M19 19H14" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.Play in fullscreen') }}</span>
                  </div>
                </div>

                <div class="w-full h-[1px] bg-[rgba(255,255,255,0.1)]"></div>

                <div class="flex items-center">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.6663 5.66651C11.555 6.85185 11.555 9.14785 10.6663 10.3332M12.6663 3.33318C15.325 5.87185 15.341 10.1445 12.6663 12.6665M1.33301 9.97251V6.02651C1.33301 5.64385 1.63167 5.33318 1.99967 5.33318H4.39034C4.47856 5.33287 4.56581 5.31469 4.64681 5.27974C4.72782 5.2448 4.80091 5.19381 4.86167 5.12985L6.86167 2.87118C7.28167 2.43385 7.99967 2.74385 7.99967 3.36185V12.6378C7.99967 13.2605 7.27301 13.5678 6.85567 13.1218L4.86234 10.8758C4.8014 10.8101 4.72757 10.7575 4.64545 10.7215C4.56333 10.6855 4.47468 10.6668 4.38501 10.6665H1.99967C1.63167 10.6665 1.33301 10.3558 1.33301 9.97251Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.menu.Sound') }}</span>
                  </div>
                  <GSwitch v-model="soundEnable" class="ml-auto" />
                </div>
                <div class="flex items-center">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.33301 11.9999V3.81054C5.33297 3.49201 5.44696 3.18399 5.65436 2.94223C5.86176 2.70048 6.14885 2.54095 6.46367 2.49254L12.4637 1.5692C12.6537 1.53998 12.8478 1.5522 13.0326 1.60501C13.2175 1.65782 13.3887 1.74998 13.5346 1.87517C13.6805 2.00036 13.7976 2.15562 13.8779 2.3303C13.9581 2.50498 13.9997 2.69496 13.9997 2.8872V10.6665" stroke="white" stroke-linecap="round" stroke-linejoin="round"></path><path d="M5.33301 5.99984L13.9997 4.6665" stroke="white"></path><path d="M5.33301 11.9997C5.33301 12.5301 5.12229 13.0388 4.74722 13.4139C4.37215 13.789 3.86344 13.9997 3.33301 13.9997C2.80257 13.9997 2.29387 13.789 1.91879 13.4139C1.54372 13.0388 1.33301 12.5301 1.33301 11.9997C1.33301 10.895 2.22834 10.6663 3.33301 10.6663C4.43767 10.6663 5.33301 10.895 5.33301 11.9997ZM13.9997 10.6663C13.9997 11.1968 13.789 11.7055 13.4139 12.0806C13.0388 12.4556 12.5301 12.6663 11.9997 12.6663C11.4692 12.6663 10.9605 12.4556 10.5855 12.0806C10.2104 11.7055 9.99967 11.1968 9.99967 10.6663C9.99967 9.56167 10.895 9.33301 11.9997 9.33301C13.1043 9.33301 13.9997 9.56167 13.9997 10.6663Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.menu.Music') }}</span>
                  </div>
                  <GSwitch v-model="musicEnable" class="ml-auto" />
                </div>
                <div class="y:hidden flex items-center">
                  <svg viewBox="0 0 16 7" fill="none" xmlns="http://www.w3.org/2000/svg" class="size-[16px]">
                    <path d="M3.21718 6.19824V1.40106C3.21718 1.24218 3.2803 1.08981 3.39264 0.977462C3.50499 0.865116 3.65736 0.802001 3.81624 0.802001H12.2031C12.362 0.802001 12.5143 0.865116 12.6267 0.977462C12.739 1.08981 12.8021 1.24218 12.8021 1.40106V6.19824M15.0361 6.19824H1.03613" stroke="white" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.menu.space_to_spin') }}</span>
                  </div>
                  <GSwitch v-model="spaceEnable" class="ml-auto" />
                </div>

                <div class="w-full h-[1px] bg-[rgba(255,255,255,0.1)]"></div>

                <div class="panel-list-item cursor-pointer flex items-center" @click="handleShowFairSettingsModal(close)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 8.295L5.705 7L5 7.705L7 9.705L11 5.705L10.295 5L7 8.295Z" fill="white"></path><path d="M8 15L4.912 13.3535C4.03167 12.8852 3.29549 12.1859 2.78246 11.3309C2.26944 10.4758 1.99894 9.49716 2 8.5V2C2.00027 1.73486 2.10571 1.48066 2.29319 1.29319C2.48067 1.10571 2.73487 1.00026 3 1H13C13.2651 1.00026 13.5193 1.10571 13.7068 1.29319C13.8943 1.48066 13.9997 1.73486 14 2V8.5C14.0011 9.49716 13.7306 10.4758 13.2175 11.3309C12.7045 12.1859 11.9683 12.8852 11.088 13.3535L8 15ZM3 2V8.5C2.99917 9.3159 3.22055 10.1166 3.64038 10.8162C4.06021 11.5158 4.66264 12.0879 5.383 12.471L8 13.8665L10.617 12.4715C11.3374 12.0883 11.9399 11.5162 12.3597 10.8165C12.7796 10.1168 13.0009 9.31599 13 8.5V2H3Z" fill="white"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.menu.fair_settings') }}</span>
                  </div>
                </div>
                <div class="panel-list-item cursor-pointer flex items-center " @click="handleShowRulesModal(close)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.707 14.9998H7.5V12.7928L10.017 10.2758C10.0058 10.1842 10.0001 10.092 10 9.99977C9.99965 9.49346 10.153 8.99897 10.4399 8.58175C10.7267 8.16453 11.1335 7.84423 11.6063 7.66324C12.0792 7.48225 12.5958 7.44909 13.088 7.56817C13.5801 7.68724 14.0244 7.95293 14.3622 8.33007C14.7 8.70722 14.9154 9.17804 14.9797 9.68024C15.0441 10.1824 14.9544 10.6924 14.7227 11.1425C14.4909 11.5926 14.1279 11.9618 13.6817 12.2011C13.2355 12.4404 12.7272 12.5387 12.224 12.4828L9.707 14.9998ZM8.5 13.9998H9.293L11.896 11.3968L12.166 11.4588C12.4874 11.5352 12.825 11.5047 13.1275 11.3719C13.43 11.239 13.6809 11.0111 13.842 10.7226C14.0032 10.4342 14.0658 10.1011 14.0204 9.77381C13.9749 9.44656 13.8239 9.14307 13.5903 8.90945C13.3567 8.67583 13.0532 8.52483 12.726 8.4794C12.3987 8.43397 12.0656 8.49658 11.7771 8.65773C11.4887 8.81887 11.2607 9.06975 11.1279 9.37225C10.995 9.67475 10.9645 10.0123 11.041 10.3338L11.103 10.6038L8.5 13.2068V13.9998Z" fill="white"></path><path d="M12.5 10.5C12.7761 10.5 13 10.2761 13 10C13 9.72386 12.7761 9.5 12.5 9.5C12.2239 9.5 12 9.72386 12 10C12 10.2761 12.2239 10.5 12.5 10.5Z" fill="white"></path><path d="M4 3H10V4H4V3ZM4 5H10V6H4V5ZM4 7H7V8H4V7ZM4 12H6V13H4V12Z" fill="white"></path><path d="M6 15H3C2.73486 14.9997 2.48066 14.8943 2.29319 14.7068C2.10571 14.5193 2.00026 14.2651 2 14V2C2.00026 1.73486 2.10571 1.48066 2.29319 1.29319C2.48066 1.10571 2.73486 1.00026 3 1H11C11.2651 1.00026 11.5193 1.10571 11.7068 1.29319C11.8943 1.48066 11.9997 1.73486 12 2V6.5H11V2H3V14H6V15Z" fill="white"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.game-rules.Game rules') }}</span>
                  </div>
                </div>
                <div class="panel-list-item cursor-pointer flex items-center" @click="handleShowMyBetHistoryModal(close)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.37691 4.26563H7.62535C7.5566 4.26563 7.50035 4.32188 7.50035 4.39063V8.69219C7.50035 8.73281 7.5191 8.77031 7.55191 8.79375L10.1347 10.6797C10.191 10.7203 10.2691 10.7094 10.3097 10.6531L10.7566 10.0438C10.7988 9.98594 10.7863 9.90781 10.73 9.86875L8.50191 8.25781V4.39063C8.50191 4.32188 8.44566 4.26563 8.37691 4.26563ZM11.8113 5.44063L14.2613 6.03906C14.3394 6.05781 14.416 5.99844 14.416 5.91875L14.4285 3.39531C14.4285 3.29063 14.3082 3.23125 14.2269 3.29688L11.7644 5.22031C11.7458 5.23471 11.7316 5.25407 11.7235 5.27616C11.7154 5.29825 11.7137 5.32218 11.7186 5.3452C11.7235 5.36821 11.7348 5.38938 11.7512 5.40626C11.7676 5.42314 11.7884 5.43505 11.8113 5.44063ZM14.4316 10.1453L13.5457 9.84063C13.5148 9.83004 13.481 9.83196 13.4515 9.84597C13.422 9.85999 13.3991 9.88499 13.3878 9.91563C13.3582 9.99531 13.3269 10.0734 13.2941 10.1516C13.016 10.8094 12.6175 11.4016 12.1082 11.9094C11.6044 12.4147 11.0075 12.8174 10.3503 13.0953C9.66961 13.3831 8.93787 13.5309 8.19878 13.5297C7.45191 13.5297 6.72847 13.3844 6.04722 13.0953C5.3901 12.8174 4.79313 12.4147 4.28941 11.9094C3.7816 11.4016 3.38316 10.8094 3.10347 10.1516C2.81725 9.47044 2.6706 8.73882 2.67222 8C2.67222 7.25313 2.81753 6.52813 3.1066 5.84688C3.38472 5.18906 3.78316 4.59688 4.29253 4.08906C4.79625 3.58378 5.39322 3.18103 6.05035 2.90313C6.72847 2.61406 7.45347 2.46875 8.20035 2.46875C8.94722 2.46875 9.67066 2.61406 10.3519 2.90313C11.009 3.18103 11.606 3.58378 12.1097 4.08906C12.2691 4.25 12.4191 4.41719 12.5566 4.59375L13.491 3.8625C12.2613 2.29063 10.3472 1.27969 8.19722 1.28125C4.45347 1.28281 1.44722 4.32344 1.48472 8.06875C1.52222 11.7484 4.51441 14.7188 8.20035 14.7188C11.0988 14.7188 13.5675 12.8813 14.5082 10.3078C14.5316 10.2422 14.4972 10.1688 14.4316 10.1453Z" fill="white"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.history.My bets history') }}</span>
                  </div>
                </div>
                <div class="panel-list-item cursor-pointer flex items-center" @click="handleShowHowToPlayModal(close)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.96834 1.3335C4.30967 1.3335 1.33301 4.32416 1.33301 8.00016C1.33301 11.6762 4.32367 14.6668 7.99967 14.6668C11.6757 14.6668 14.6663 11.6762 14.6663 8.00016C14.6663 4.32416 11.6617 1.3335 7.96834 1.3335ZM7.99967 13.3335C5.05901 13.3335 2.66634 10.9408 2.66634 8.00016C2.66634 5.0595 5.04434 2.66683 7.96834 2.66683C10.927 2.66683 13.333 5.0595 13.333 8.00016C13.333 10.9408 10.9403 13.3335 7.99967 13.3335Z" fill="white"></path>
                    <path d="M7.33301 4.6665H8.66634V9.33317H7.33301V4.6665ZM7.33301 9.99984H8.66634V11.3332H7.33301V9.99984Z" fill="white"></path>
                  </svg>
                  <div class="ml-[12px]">
                    <span>{{ $t('global.How to play') }}</span>
                  </div>
                </div>

                <div class="w-full h-[1px] bg-[rgba(255,255,255,0.1)]"></div>

                <div class="text-[#a1a1a1]">
                  <span>{{ $t('powered_by') }}</span>
                  <img v-if="isMaterial && getAssetUrl('brand-logo.png')" :src="getAssetUrl('brand-logo.png')" alt="init-scene" class="ml-[9px] inline-block w-62 cursor-pointer" @click="handleLogoClick" />
                  <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 20" fill="none" class="ml-[8px] inline-block w-[62px] cursor-pointer" @click="handleLogoClick">
                    <g clip-path="url(#clip0_769_1173)">
                      <path d="M16.4688 0.302734H17.2995L20.4415 2.27861H19.5654L16.4688 0.302734Z" fill="#FFA200"/>
                      <path d="M13.2212 19.6976H14.052L17.1939 17.7217H16.3178L13.2212 19.6976Z" fill="#FDE43B"/>
                      <path d="M10.9876 15.9584L12.9791 9.90918L14.7733 15.9584H19.146C19.146 15.9584 18.807 16.9032 18.5898 17.5087C18.1603 17.6094 17.9154 17.6529 17.4773 17.7063C17.0212 17.7619 16.303 17.7823 16.303 17.7823H3.97267C3.0456 17.7823 2.47389 17.7823 1.56225 17.4935C1.03421 17.3262 0.696972 17.0983 0.434298 16.7791C0.107631 16.3822 0.0791016 15.9584 0.0791016 15.9584H10.9876Z" fill="url(#paint0_linear_769_1173)"/>
                      <path d="M3.86452 3.47945C4.19614 3.19967 4.41257 3.07139 4.80706 2.88669C5.2085 2.69872 5.88867 2.53711 5.88867 2.53711L5.3633 4.10261L4.00358 9.19428L2.1494 15.9578H0.0791016C0.0791016 15.9578 0.0791115 14.6551 0.187068 14.1948C0.770348 11.7076 0.913312 11.4893 1.56225 9.08788C1.94096 7.68644 2.18033 6.89923 2.58204 5.53131C2.94661 4.28992 3.13867 4.09185 3.86452 3.47945Z" fill="url(#paint1_linear_769_1173)"/>
                      <path d="M18.6821 4.10259L17.0272 10.1214L14.8965 4.10259H5.34766L5.88846 2.53709C5.88846 2.53709 6.36646 2.43898 6.67648 2.4003C6.9827 2.36209 7.4645 2.33521 7.4645 2.33521L20.5056 2.27441C21.4326 2.27441 22.0043 2.27871 22.916 2.56749C23.444 2.73476 23.7813 2.96266 24.0439 3.28184C24.3706 3.67878 24.3684 4.10259 24.3684 4.10259H18.6821Z" fill="url(#paint2_linear_769_1173)"/>
                      <path d="M20.614 16.5507C20.2824 16.8304 20.0659 16.9587 19.6715 17.1434C19.27 17.3314 19.0241 17.4143 18.5898 17.5082C18.795 16.8909 19.1152 15.9275 19.1152 15.9275L20.4749 10.8358L22.3291 4.07227H24.3687C24.3687 4.07227 24.3653 4.99308 24.2914 5.57697C24.155 6.65567 22.303 13.6474 21.8965 14.4988C21.4899 15.3502 21.3398 15.9383 20.614 16.5507Z" fill="url(#paint3_linear_769_1173)"/>
                      <path d="M16.4687 0.302734L19.5738 2.28295L13.3018 2.3318L16.4687 0.302734Z" fill="url(#paint4_linear_769_1173)"/>
                      <path d="M13.2207 19.697L10.0566 17.668H16.3994L13.2207 19.697Z" fill="url(#paint5_linear_769_1173)"/>
                      <path d="M9.14676 4.10237H10.6169L7.4516 15.9576H5.98145L9.14676 4.10237Z" fill="url(#paint6_linear_769_1173)"/>
                      <path d="M3.47803 7.17285H8.79332V8.66235H3.47803V7.17285Z" fill="url(#paint7_linear_769_1173)"/>
                      <path d="M14.9427 15.9584L12.979 9.90918L14.1874 15.9584H14.9427Z" fill="#FFF348"/>
                      <path d="M19.0817 4.10237L17.0273 10.1212L18.3113 4.10237H19.0817Z" fill="#FFA200"/>
                      <path d="M23.9074 5.01409L23.9661 4.58748C24.0215 4.1848 23.9391 3.77509 23.7325 3.4257C23.483 3.00365 23.0722 2.70315 22.5962 2.59446L21.2119 2.27832L21.6931 2.27832C21.815 2.27833 21.9366 2.29145 22.0557 2.31746L23.042 2.53287C23.545 2.64274 23.9875 2.9414 24.2794 3.36807C24.5117 3.70761 24.6361 4.11014 24.6361 4.52237V4.81753C24.6361 5.07841 24.6021 5.33816 24.535 5.59016L22.0489 14.926C21.8465 15.6861 21.3871 16.3515 20.7499 16.8077C20.426 17.0395 20.0639 17.2118 19.6803 17.3164L19.4114 17.3898C18.7904 17.5592 18.1545 17.667 17.5127 17.7115L17.1636 17.7357L19.2901 16.967C19.5531 16.8719 19.8027 16.7429 20.0326 16.583C20.6617 16.1456 21.1157 15.4975 21.3136 14.7543L23.9074 5.01409Z" fill="#FFF348"/>
                      <path d="M2.48089 15.9576H1.74072L3.72337 8.66211H4.39928L2.48089 15.9576Z" fill="url(#paint8_linear_769_1173)"/>
                      <path d="M7.8886 15.9576H7.14844L10.2904 4.10237H10.9852L7.8886 15.9576Z" fill="#FFF349"/>
                      <path d="M7.8886 15.9576H7.14844L10.2904 4.10237H10.9852L7.8886 15.9576Z" fill="url(#paint9_linear_769_1173)"/>
                      <path d="M4.8224 7.17257H4.07568L4.90177 4.10237H5.57768L4.8224 7.17257Z" fill="#FFAA06"/>
                      <path d="M28.7735 4.15804C28.7735 4.15804 27.6084 4.33964 27.2747 4.47175C26.3996 4.82397 25.7432 5.5945 25.3767 6.70074C25.2673 7.03096 23.8561 12.32 23.6701 13.107C23.4732 13.9326 23.4732 14.56 23.6701 14.9728C23.8999 15.4406 24.2773 15.7048 24.9555 15.8644C25.2509 15.9359 25.8143 15.9579 28.1882 15.9745C31.8748 16.0075 32.4709 15.9469 33.4008 15.4571C34.0518 15.1104 34.637 14.3344 34.9597 13.3712C35.124 12.8924 36.6062 7.30065 36.7264 6.71725C37.0219 5.28629 36.6337 4.55981 35.3974 4.22957C35.0965 4.15254 34.6479 4.136 32.0171 4.1305C30.3489 4.125 28.7735 4.15804 28.7735 4.15804ZM32.9195 7.33366C32.8431 7.65837 31.5739 12.3971 31.5193 12.5622L31.4591 12.7438H29.4408C28.3304 12.7438 27.4224 12.7438 27.4224 12.7438C27.4224 12.7108 28.6805 7.97758 28.8227 7.47124L28.9047 7.18505H30.9341H32.9579L32.9195 7.33366Z" fill="white"/>
                      <path d="M38.6337 4.10254C38.5023 4.49881 36.2268 13.2387 36.1721 13.5524C35.9697 14.6751 36.2105 15.3521 36.9159 15.6603C37.5999 15.952 37.7256 15.963 41.1771 15.963C44.0596 15.963 44.426 15.952 44.8692 15.8639C46.1873 15.5942 46.893 14.9943 47.4181 13.7065C47.5383 13.4148 48.1126 11.3784 48.8292 8.68711C49.4966 6.19946 50.038 4.11905 50.038 4.10254C50.038 4.10254 49.2066 4.10254 48.1894 4.10254H46.3405L45.1973 8.3679C44.5683 10.7125 44.0157 12.7103 43.994 12.7433H42.0249H41.0267H40.0282L41.1826 8.47797C41.8116 6.1444 42.3258 4.14105 42.3258 4.10254C42.3258 4.10254 41.9209 4.10254 40.4932 4.10254C39.0711 4.10254 38.6337 4.10254 38.6337 4.10254Z" fill="white"/>
                      <path d="M50.9807 4.10254C50.9698 4.14105 50.7949 4.85653 50.5979 5.61606C50.4009 6.37005 50.1768 7.13508 50.1602 7.18459C50.1602 7.18459 50.2916 7.18459 51.9927 7.18459H53.8033L53.7649 7.30567C53.7429 7.37723 53.2232 9.31452 52.6109 11.6151C51.9982 13.9156 51.4073 15.941 51.3909 15.9905H53.3H55.1652C55.1815 15.9245 55.7395 13.8221 56.363 11.4775L57.5089 7.1956H59.3113H61.1273C61.1436 7.14608 61.7234 4.85653 61.9203 4.10254C61.9203 4.10254 60.8264 4.10254 56.4724 4.10254C52.1459 4.10254 50.9807 4.10254 50.9807 4.10254Z" fill="white"/>
                    </g>
                    <defs>
                      <linearGradient id="paint0_linear_769_1173" x1="10.5472" y1="22.9043" x2="14.3097" y2="1.63045" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint1_linear_769_1173" x1="10.5472" y1="22.9038" x2="14.3097" y2="1.6299" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint2_linear_769_1173" x1="10.5469" y1="22.9038" x2="14.3095" y2="1.62988" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint3_linear_769_1173" x1="10.5472" y1="22.9038" x2="14.3098" y2="1.62996" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint4_linear_769_1173" x1="10.5474" y1="22.9037" x2="14.3099" y2="1.62979" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint5_linear_769_1173" x1="10.5471" y1="22.904" x2="14.3096" y2="1.63015" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint6_linear_769_1173" x1="10.5472" y1="22.9035" x2="14.3098" y2="1.62967" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint7_linear_769_1173" x1="10.5469" y1="22.9038" x2="14.3095" y2="1.62995" gradientUnits="userSpaceOnUse">
                        <stop offset="0.380208" stop-color="#FFA200"/>
                        <stop offset="1" stop-color="#FDE43B"/>
                      </linearGradient>
                      <linearGradient id="paint8_linear_769_1173" x1="3.07" y1="8.66211" x2="1.72474" y2="15.9547" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFAA06"/>
                        <stop offset="1" stop-color="#FFF348"/>
                      </linearGradient>
                      <linearGradient id="paint9_linear_769_1173" x1="9.61062" y1="8.66207" x2="5.37819" y2="23.7868" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FFAB00"/>
                        <stop offset="1" stop-color="#FFE600" stop-opacity="0"/>
                      </linearGradient>
                      <clipPath id="clip0_769_1173">
                        <rect width="62" height="20" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            </div>
          </PopoverPanel>
        </transition>
      </Popover>
    </div>
  </header>
  <MyBetHistory v-model:show="showMyBetHistoryModal" @show-round-info="handleShowRoundInfo" />
  <HistoryRoundFairInfo v-model:show="showRoundFairModal" :round-info="roundInfo" @show-fair-settings="handleShowFairSettings" />
  <HowToPlay v-model:show="showHowToPlayModal" @link-click="handleShowRulesModal()" />
  <GameRules v-model:show="showRulesModal" />
  <ProvablyFairSettings v-model:show="showFairSettingsModal" />
  <ChooseGameAvatar v-model:show="showAvatarModal" />
</template>

<style lang="scss" scoped>
.open-menu {
  background: linear-gradient(0deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.2) 100%), rgba(0, 0, 0, 0.4);
  padding: 11px;
}

.menu-line {
  display: block;
  position: absolute;
  height: 2px;
  width: 50%;
  background: white;
  transition: all 0.3s ease-in-out;
}

.panel-list-item:hover {
  color: #9bc030;
  svg path {
    fill: #9bc030;
    stroke: #9bc030;
  }
}
</style>