<script setup lang="ts">
import { onMounted, ref, watchEffect } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores";
import { initAssets, getRemoteMaterialData } from "@/AssetsManager";

import GLoading from "@/components/ui/GLoading/index.vue"

const { isSocketOnline, isJoinRoom } = storeToRefs(useGameStore());
const { setIsInitLoaded } = useGameStore();

const assetsLoaded = ref(false);

watchEffect(() => {
  if (assetsLoaded.value && isSocketOnline.value && isJoinRoom.value) {
    setIsInitLoaded(true);
  };
});

onMounted(async () => {
  // 加载远端清单资源
  await getRemoteMaterialData();
  // 初始化游戏资源
  await initAssets();
  // 更新状态
  assetsLoaded.value = true;
});
</script>

<template>
  <div class="loading-wrapper absolute inset-0 size-full flex flex-col justify-center items-center">
    <div class="max-w-[500px] w-full mx-auto">
      <svg viewBox="0 0 501 157" fill="none" xmlns="http://www.w3.org/2000/svg" class="loading-bg w-full">
        <g clip-path="url(#clip0_1132_1181)">
          <path d="M132.776 0H139.507L164.96 15.9953H157.863L132.776 0Z" fill="#FFA200"/>
          <path d="M106.466 157H113.196L138.65 141.005H131.552L106.466 157Z" fill="#FDE43B"/>
          <path d="M88.3738 126.732L104.508 77.7617L119.042 126.732H154.468C154.468 126.732 151.721 134.381 149.961 139.282C146.482 140.097 144.498 140.449 140.949 140.882C137.253 141.332 131.435 141.497 131.435 141.497H31.5432C24.0326 141.497 19.401 141.497 12.0156 139.159C7.7377 137.805 5.00562 135.96 2.8776 133.376C0.231164 130.163 3.8147e-05 126.732 3.8147e-05 126.732H88.3738Z" fill="url(#paint0_linear_1132_1181)"/>
          <path d="M30.667 25.7154C33.3536 23.4506 35.107 22.4121 38.3029 20.9169C41.5551 19.3952 47.0654 18.0869 47.0654 18.0869L42.8092 30.7601L31.7936 71.9788L16.7723 126.732H3.8147e-05C3.8147e-05 126.732 0.000118382 116.186 0.874713 112.459C5.60006 92.3245 6.75826 90.5579 12.0156 71.1175C15.0836 59.7724 17.0228 53.3996 20.2772 42.3259C23.2307 32.2765 24.7867 30.673 30.667 25.7154Z" fill="url(#paint1_linear_1132_1181)"/>
          <path d="M150.711 30.7601L137.304 79.4842L120.042 30.7601H42.6841L47.0654 18.0869C47.0654 18.0869 50.9378 17.2927 53.4494 16.9795C55.9302 16.6702 59.8334 16.4526 59.8334 16.4526L165.483 15.9604C172.994 15.9604 177.625 15.9952 185.011 18.333C189.289 19.6871 192.021 21.532 194.149 24.1159C196.795 27.3292 196.778 30.7601 196.778 30.7601H150.711Z" fill="url(#paint2_linear_1132_1181)"/>
          <path d="M166.359 131.531C163.673 133.796 161.919 134.834 158.724 136.329C155.471 137.851 153.479 138.522 149.961 139.282C151.623 134.285 154.217 126.486 154.217 126.486L165.233 85.2673L180.254 30.5142H196.778C196.778 30.5142 196.75 37.9685 196.152 42.6952C195.047 51.4276 180.043 108.028 176.749 114.92C173.456 121.812 172.24 126.573 166.359 131.531Z" fill="url(#paint3_linear_1132_1181)"/>
          <path d="M132.776 0L157.931 16.0305L107.12 16.4259L132.776 0Z" fill="url(#paint4_linear_1132_1181)"/>
          <path d="M106.466 157L80.8321 140.574H132.217L106.466 157Z" fill="url(#paint5_linear_1132_1181)"/>
          <path d="M73.4595 30.7603H85.3697L59.7264 126.732H47.8163L73.4595 30.7603Z" fill="url(#paint6_linear_1132_1181)"/>
          <path d="M27.5376 55.6145H70.5987V67.6725H27.5376V55.6145Z" fill="url(#paint7_linear_1132_1181)"/>
          <path d="M120.416 126.732L104.508 77.7617L114.298 126.732H120.416Z" fill="#FFF348"/>
          <path d="M153.947 30.7603L137.304 79.4844L147.706 30.7603H153.947Z" fill="#FFA200"/>
          <path d="M193.039 38.1423L193.514 34.6887C193.963 31.4289 193.296 28.1122 191.623 25.2838C189.601 21.8671 186.273 19.4345 182.417 18.5546L171.202 15.9954L175.1 15.9954C176.088 15.9954 177.073 16.1016 178.038 16.3122L186.028 18.056C190.103 18.9454 193.689 21.3632 196.053 24.8172C197.935 27.5659 198.943 30.8245 198.943 34.1616V36.5511C198.943 38.663 198.667 40.7657 198.124 42.8057L177.983 118.382C176.343 124.536 172.622 129.922 167.459 133.615C164.835 135.492 161.902 136.886 158.794 137.733L156.615 138.327C151.585 139.699 146.433 140.571 141.234 140.932L138.405 141.128L155.633 134.905C157.763 134.135 159.785 133.09 161.648 131.796C166.744 128.255 170.423 123.008 172.026 116.992L193.039 38.1423Z" fill="#FFF348"/>
          <path d="M19.4575 126.732H13.4612L29.5233 67.6724H34.999L19.4575 126.732Z" fill="url(#paint8_linear_1132_1181)"/>
          <path d="M63.2675 126.732H57.2711L82.725 30.7603H88.3542L63.2675 126.732Z" fill="#FFF349"/>
          <path d="M63.2675 126.732H57.2711L82.725 30.7603H88.3542L63.2675 126.732Z" fill="url(#paint9_linear_1132_1181)"/>
          <path d="M38.4255 55.6145H32.3761L39.0685 30.7603H44.5442L38.4255 55.6145Z" fill="#FFAA06"/>
          <path d="M232.465 31.2057C232.465 31.2057 223.026 32.6758 220.323 33.7453C213.233 36.5966 207.916 42.8343 204.947 51.7897C204.06 54.4629 192.628 97.2794 191.121 103.651C189.526 110.334 189.526 115.413 191.121 118.754C192.982 122.542 196.04 124.68 201.535 125.972C203.928 126.551 208.492 126.73 227.723 126.863C257.59 127.131 262.419 126.641 269.953 122.675C275.227 119.868 279.968 113.586 282.582 105.789C283.913 101.913 295.92 56.6461 296.895 51.9233C299.288 40.3393 296.143 34.4582 286.128 31.7848C283.69 31.1612 280.056 31.0273 258.743 30.9828C245.228 30.9382 232.465 31.2057 232.465 31.2057ZM266.054 56.9134C265.435 59.542 255.153 97.9032 254.71 99.2396L254.223 100.71H237.871C228.876 100.71 221.52 100.71 221.52 100.71C221.52 100.443 231.712 62.1261 232.864 58.0271L233.528 55.7103H249.969H266.365L266.054 56.9134Z" fill="white"/>
          <path d="M312.345 30.7603C311.281 33.9682 292.846 104.72 292.403 107.26C290.764 116.349 292.714 121.829 298.429 124.324C303.97 126.685 304.988 126.774 332.951 126.774C356.302 126.774 359.271 126.685 362.862 125.972C373.54 123.789 379.257 118.933 383.511 108.507C384.485 106.146 389.138 89.6608 394.943 67.8738C400.35 47.7354 404.736 30.8939 404.736 30.7603C404.736 30.7603 398 30.7603 389.759 30.7603H374.781L365.52 65.2897C360.424 84.2697 355.947 100.443 355.771 100.71H339.818H331.732H323.643L332.995 66.1807C338.091 47.2898 342.256 31.072 342.256 30.7603C342.256 30.7603 338.977 30.7603 327.41 30.7603C315.889 30.7603 312.345 30.7603 312.345 30.7603Z" fill="white"/>
          <path d="M412.374 30.7603C412.286 31.072 410.869 36.8641 409.273 43.0127C407.677 49.1165 405.861 55.3096 405.727 55.7105C405.727 55.7105 406.791 55.7105 420.573 55.7105H435.241L434.93 56.6906C434.752 57.2699 430.542 72.9529 425.581 91.5766C420.617 110.2 415.83 126.596 415.698 126.997H431.163H446.274C446.406 126.462 450.927 109.443 455.979 90.4628L465.262 55.7995H479.864H494.575C494.708 55.3987 499.404 36.8641 501 30.7603C501 30.7603 492.138 30.7603 456.865 30.7603C421.814 30.7603 412.374 30.7603 412.374 30.7603Z" fill="white"/>
        </g>
        <defs>
          <linearGradient id="paint0_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.7351" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint1_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.735" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint2_linear_1132_1181" x1="84.8053" y1="182.961" x2="115.243" y2="10.735" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint3_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.7352" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint4_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.7351" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint5_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.735" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint6_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.7352" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint7_linear_1132_1181" x1="84.8053" y1="182.962" x2="115.243" y2="10.7352" gradientUnits="userSpaceOnUse">
            <stop offset="0.380208" stop-color="#FFA200"/>
            <stop offset="1" stop-color="#FDE43B"/>
          </linearGradient>
          <linearGradient id="paint8_linear_1132_1181" x1="24.2301" y1="67.6724" x2="13.3474" y2="126.711" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFAA06"/>
            <stop offset="1" stop-color="#FFF348"/>
          </linearGradient>
          <linearGradient id="paint9_linear_1132_1181" x1="77.2181" y1="67.6725" x2="42.977" y2="190.125" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFAB00"/>
            <stop offset="1" stop-color="#FFE600" stop-opacity="0"/>
          </linearGradient>
          <clipPath id="clip0_1132_1181">
            <rect width="501" height="157" fill="white"/>
          </clipPath>
        </defs>
      </svg>
    </div>
    <div class="flex items-center justify-center gap-[10px] mt-[7.4vh]">
      <GLoading />
      <!-- preload webfont Montserrat -->
      <span class="text-[#e9e8e8] text-[14px] font-[Montserrat]">{{ $t('global.loading') }}...</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@keyframes init-bg {
  0% {
    filter: drop-shadow(0px 0px 4rem rgba(255, 255, 255, 0.4));
  }
  50% {
      filter: drop-shadow(0px 0px 7rem rgba(255, 255, 255, 0.4));
  }
  100% {
     filter: drop-shadow(0px 0px 4rem rgba(255, 255, 255, 0.4));
   }
}

.loading-bg {
  animation: 1.5s linear 0s infinite normal none running init-bg;
}
</style>