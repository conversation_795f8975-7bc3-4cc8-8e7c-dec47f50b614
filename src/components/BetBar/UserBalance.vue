<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores';
import { formatAmount } from '@/utils/utils';

import CurrencySymbol from '@/components/CurrencySymbol.vue';

import balanceBg from '@/assets/image/balance.png';

const balanceBgCss = `url(${balanceBg}) center center / 100% no-repeat`;

const { balance } = storeToRefs(useUserStore());
</script>

<template>
  <div id="balance" class="block-info">
    <div class="opacity-60 font-bold text-[14px] y:text-[11px]">{{ $t('twist.BALANCE') }}</div>
    <div class="block-value">
      <CurrencySymbol class="size-[16px] y:size-[12px] fill-white" />
      <Vue3Odometer :value="Number.parseFloat(formatAmount(balance, 2, false, false, false))" format="( ddd).dd" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/assets/style/variables.scss';

.block-info {
  display: flex;
  color: white;
  text-transform: uppercase;

  width: 168px;
  height: 40px;
  padding: 12px 16px 12px 8px;
  align-items: center;
  justify-content: space-between;
  background: v-bind(balanceBgCss);
}

.block-value {
  font-size: 11px;
  letter-spacing: -0.5px;
  line-height: 18px;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}
</style>