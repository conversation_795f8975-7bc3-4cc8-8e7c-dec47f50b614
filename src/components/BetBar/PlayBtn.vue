<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useGameAutoPlayStore, useGameSlotAnimStore, useGameStore } from '@/stores';
import { getAudio } from '@/AudioManager';
import { useTimer } from '@/composables/useTimer';
import emitter from '@/utils/emitter';

import SpinnerIcon from "@/components/icons/SpinnerIcon.vue";
import PauseIcon from '@/components/icons/PauseIcon.vue';
import PlayIcon from '@/components/icons/PlayIcon.vue';

import playBtnBg from '@/assets/image/playBtn.png';
import playBtnEmptyBg from '@/assets/image/playBtnEmpty.png';
import playBtnIcon from '@/assets/image/playBtnIcon.png';

const playBtnBgCss = `url(${playBtnBg}) 0% 0% / 100% 100% no-repeat`;
const playBtnEmptyBgCss = `url(${playBtnEmptyBg}) 0% 0% / 100% 100% no-repeat`;

const { isSocketOnline, isFetchPending } = storeToRefs(useGameStore());
const { isRolling } = storeToRefs(useGameSlotAnimStore());
const { state, settings, start, stop, resume, pause } = useGameAutoPlayStore();

const { start: autoStart, clean: autoClean } = useTimer()

// 计算剩余轮数
const remainingRounds = computed(() => {
  if (state.processing) {
    return Math.max(settings.rounds - state.currentRound, 0);
  }
  return 0;
});

const btnDisabled = computed(() => {
  if (state.processing) return false;
  return isFetchPending.value || isRolling.value || !isSocketOnline.value;
});

const handleTouchDown = () => {
  if(btnDisabled.value) return;

  autoStart(start, 600);
}

const handleTouchUp = () => {
  autoClean();

  if (state.processing) return stop();

  if (!btnDisabled.value) {
    getAudio().sfx.play('sounds/tap.mp3');
    emitter.emit('handle-game-play');
  }
}

const handleTouchLeave = () => {
  autoClean();

  if (state.processing) stop();
}

const handlePlay = () => {
  if (state.processing) {
    state.paused ? resume() : pause();
  } else {
    emitter.emit('handle-game-play');
  }
}
</script>

<template>
  <div class="play-btn-wrapper">
    <button
      class="play-btn-inner btn-y group'"
      :style="{ background: state.processing ? playBtnEmptyBgCss : playBtnBgCss }"
      :disabled="btnDisabled"
      @pointerdown="handleTouchDown"
      @pointerup="handleTouchUp"
      @pointerleave="handleTouchLeave"
    >
      <SpinnerIcon v-if="state.processing" class="run-spin" />
      <img v-else :src="playBtnIcon" alt="playButton" />
    </button>

    <button
      class="play-btn-inner btn-x group"
      :style="{ background: playBtnEmptyBgCss }"
      :disabled="btnDisabled"
      @click="handlePlay"
    >
      <template v-if="state.processing">
        <PlayIcon v-if="state.paused" />
        <span class="group-hover:hidden absolute left-1/2 -translate-x-1/2">{{ remainingRounds }}</span>
        <PauseIcon v-if="!state.paused" class="hidden group-hover:block absolute left-1/2 -translate-x-1/2" />
      </template>
      <img v-else :src="playBtnIcon" alt="playButton" />
    </button>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/style/variables.scss";

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.run-spin {
  animation: spin 2s linear infinite;
}

.play-btn-wrapper {
  position: absolute;
  right: 112px;
  bottom: -5px;
  background: rgb(28, 28, 33);
  border-radius: 50%;
  padding: 6px;
  width: 118px;
  height: 118px;
  z-index: 1;

  @media #{variables.$y} {
    width: 81px;
    height: 81px;
    padding: 5px;
    bottom: 1px;
    transform: translateZ(0px);
    left: calc(50% - 40px);
  }
}

.play-btn-inner {
  @media #{variables.$x} {
    &.btn-y {
      display: none !important;
      opacity: 0 !important;
    }
  }

  @media #{variables.$y} {
    &.btn-x {
      display: none !important;
      opacity: 0 !important;
    }
  }

  &.btn-x {
    font-size: 36px;
    font-weight: 700;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
  }

  &.btn-y {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:active {
    opacity: 0.6;
  }

  &:hover:not(:disabled) {
    filter: brightness(110%);
  }

  &.btn-x img {
    margin: 3px 0 0 5px;
  }

  &.btn-y img {
    width: 64px;
    height: 68px;
    margin: 4px 0px 0px 3px;
  }

  img {
    transform: scale(0.5);
    pointer-events: none;
  }
}
</style>