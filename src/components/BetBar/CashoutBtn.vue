<script lang="ts" setup>
import Big from 'big.js';
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useGameAutoPlayStore, useGameSlotAnimStore, useGameStore } from '@/stores';
import { useI18n } from "vue-i18n";
import { TwistElement, type CircleElement } from '@/constant/game';

import partCashoutBtn from '@/assets/image/partCashoutBtn.png';
import fullCashoutBtn from '@/assets/image/fullCashoutBtn.png';
import partCashoutDisabledBtn from '@/assets/image/partCashoutDisabledBtn.png'
import fullCashoutDisabledBtn from '@/assets/image/fullCashoutDisabledBtn.png';
import CurrencySymbol from '../CurrencySymbol.vue';
import emitter from "@/utils/emitter.ts";

const props = defineProps<{
  mode: 'full' | 'part';
}>();

const partCashoutBtnCss = `url(${partCashoutBtn}) center center / 100% no-repeat`;
const fullCashoutBtnCss = `url(${fullCashoutBtn}) right center / 135px 100% no-repeat rgb(28, 28, 33)`;
const partCashoutDisabledBtnCss = `url(${partCashoutDisabledBtn}) right center / 100% no-repeat rgb(28, 28, 33)`;
const fullCashoutDisabledBtnCss = `url(${fullCashoutDisabledBtn}) right center / 135px 100% no-repeat rgb(28, 28, 33)`;

const { t } = useI18n();
const { isRolling } = storeToRefs(useGameSlotAnimStore());
const { state } = useGameAutoPlayStore();
const { gameData, twistCoeffs, circleSteps, isSocketOnline, isFetchPending } = storeToRefs(useGameStore());

const hasProgressLen = (el: CircleElement, minProgress: number = 1): boolean => {
  return (circleSteps.value[el] || 0) >= minProgress;
}

const getCoefficient = (item: CircleElement, progress: number, isPrev?: boolean): number => {
  const coeffs = twistCoeffs.value[item] || [];
  const step = isPrev ? progress - 1 : progress;

  if (!coeffs || coeffs.length === 0 || step <= 0) {
    return 0
  }

  // 如果progress等于odds长度，取倒数第二个元素，否则取step-1位置
  return progress === coeffs.length ? Number(coeffs[step - 2]) : Number(coeffs[step - 1])
}

// 计算 full cashout 金额
const calcFullCashoutAmount = () => {
  const { progressFire, progressEarth, progressWater } = gameData.value;

  const betAmount = new Big(gameData.value.betAmount);
  

  const fireCoeff = getCoefficient(TwistElement.FIRE, progressFire);
  const earthCoeff = getCoefficient(TwistElement.EARTH, progressEarth);
  const waterCoeff = getCoefficient(TwistElement.WATER, progressWater);

  return betAmount.times(new Big(fireCoeff).plus(earthCoeff).plus(waterCoeff)).toFixed(2, Big.roundDown);
}

// 计算 part cashout 金额
const calcPartCashoutAmount = () => {
  const { progressFire, progressEarth, progressWater } = gameData.value;

  if (progressFire < 2 && progressEarth < 2 && progressWater < 2) {
    return 0
  }

  const bet = new Big(gameData.value.betAmount || 0);

  const fireCoeff = getCoefficient(TwistElement.FIRE, progressFire);
  const earthCoeff = getCoefficient(TwistElement.EARTH, progressEarth);
  const waterCoeff = getCoefficient(TwistElement.WATER, progressWater);

  const fireCoeffPrev = getCoefficient(TwistElement.FIRE, progressFire, true);
  const earthCoeffPrev = getCoefficient(TwistElement.EARTH, progressEarth, true);
  const waterCoeffPrev = getCoefficient(TwistElement.WATER, progressWater, true);

  const totalCoeff = new Big(fireCoeff).plus(earthCoeff).plus(waterCoeff);
  const totalCoeffPrev = new Big(fireCoeffPrev).plus(earthCoeffPrev).plus(waterCoeffPrev);

  return bet.times(totalCoeff.minus(totalCoeffPrev)).toFixed(2, Big.roundDown);
}

const cashoutAmount = computed(() => {
  return props.mode === 'full' ? calcFullCashoutAmount() : calcPartCashoutAmount();
});

const isProcessing = computed(() => {
  return (state.processing && !state.paused) ||
    isRolling.value ||
    isFetchPending.value;
});

const isCashoutStep = computed(() => {
  return !hasProgressLen(TwistElement.FIRE, props.mode === 'full' ? 1 : 2) &&
         !hasProgressLen(TwistElement.EARTH, props.mode === 'full' ? 1 : 2) &&
         !hasProgressLen(TwistElement.WATER, props.mode === 'full' ? 1 : 2);
});

const isCashoutDisabled = computed(() => {
  if (
    isProcessing.value ||
    gameData.value.isFinished ||
    gameData.value.isCashout ||
    !isSocketOnline.value || 
    !gameData.value.isCanCashOut
  ) {
    return true
  }

  return isCashoutStep.value;
});

const handleCashout = () => {
  if (isCashoutDisabled.value) {
    if (props.mode === 'full') {
      return emitter.emit('handle-game-notification', t('twist.MAKE_ONE_SPIN_CASHOUT'));
    } else {
      const msg = isCashoutStep.value ? t('twist.FILL_AT_LEAST') : t('twist.MAKE_ONE_SPIN_PART_CASHOUT');
      return emitter.emit('handle-game-notification', msg);
    }
  }

  props.mode === 'full'
    ? emitter.emit('handle-game-full-cashout')
    : emitter.emit('handle-game-part-cashout');
}
</script>

<template>
  <div :style="{ cursor: isCashoutDisabled ? 'help' : 'auto' }" @click="handleCashout">
    <button :class="`bet-cashout-btn ${mode}`" :disabled="isCashoutDisabled">
      <span>{{ mode === 'full' ? $t('twist.CASHOUT') : $t('twist.PART_CASHOUT') }}</span>
      <div v-if="Number(cashoutAmount) && Number(cashoutAmount) > 0" class="flex items-center gap-[2px]">
        <span>{{ cashoutAmount }}</span>
        <CurrencySymbol class="currency-symbol" />
      </div>
    </button>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/style/variables.scss";

.bet-cashout-btn {
  &:disabled {
    &.part, &.full {
      opacity: 0.5;
      pointer-events: none;
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
      background: rgba(255, 255, 255, 0.05);
    }
  }

  &:hover:not(:disabled) {
    @media #{variables.$x} {
      &.part {
        color: rgb(16, 17, 20);
        border: none;
        background: rgb(255, 198, 0);
      }
      &.full {
        color: rgb(255, 198, 0);
        border: 1.5px solid rgb(255, 198, 0);
        background: linear-gradient(0deg, rgba(255, 198, 0, 0.2) 0%, rgba(255, 198, 0, 0.2) 100%), rgba(255, 255, 255, 0.05);
      }
    }

    @media #{variables.$y} {
      &.part {
        border: none;
        color: rgb(16, 17, 20);
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAVAAAABcBAMAAADKc/AlAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAAwUExURUdwTAYGBigoKAgICA4ODgQEBAMDAw4ODgQDAgUEAvG7APrCANGhAJVzAeWxAP/GAFcMt7MAAAAPdFJOUwAPAQoGFRwDJCu443NPk1FvcosAAAPxSURBVGje7Zs/a9tAFMCPWzoLof04AjcLCdK1kUGzBnvraOGhQ7gK7hsUmTadE1r3CyTgrVPBdOmQDN3TLt0KHbJ1iEPc906yYqf+05LQvKN6thXr3bvTj/fuTnexHmOVSCY5vDljguMJk5LBCRdMSC6lsCW2ENQcDaCES4G1BNiCvYATzgXowZCjCLTl2JAEva0kJDaBDWEdYRtkVeHK67MlQRSplJZWjFRSS60VfldGyVoUvKS10ZWl0fMiM6+L5baCtoVoqO2xaaU2Q5Wat1v9WXl9KW9YkdJsEL2uQJl7EL2tJXB7w2mKzpKk8LmlqqVIV+huK9IFdXVM0wWrtLNNFq6fpg0qR84koipx0YCqIolCj6pEcWrkHPTx8YyqTE+AVIvaoW9mhOWDv2v20aOqiMaUQWff4hR7KVdJSJpzdu13DMZeJQFt0NnHGEG5IQ/60081hj6kDnplQR8VUW9CG/Qy29U46EPqoNMsxtDTB51lEUz5ToD6GhaoDoD2oz3hBqifSldA3eijLoFKmPBbj7ag9EHHLeh9ghpXQHecAJVtH2092nq0DX0LekdQ5gYoY2znSRv6/zH0zniUu+JR6U7omWlvof/lVsSt1VPQDqZ77qPBhROh34lcAMV7feIGKP7E6IZHAfSzE6AqCV46Auo9nbgw6vH3m2fH9EHxUY0gL7+OyYcehr3XGwyPTifkQZ+HQS8vh6MftEGlKZIw6OaD8uh8Qtqj0nSi0OuCU8vRmDAoE8p0EiC1Tj0lDMo4ktZOHb4dkwStn8xUBsIfedhTh6Njsh5lnCFp1VMH5eiMLCiOKOvU0IOJajA8owtaxb9I6vBfkAOVC6Bzp3bz8mBM2KP4YLmxTgXS0YQyKEhD+o5u6CuBjtpB0uEXOpzTvm9uexRu/RpIg96AUDe9XAkK4cdFSm/wikw3vepGK0FxlRIG+eCcCuj7LNpjq0HBp9BNqcymn7JY81WkTJgiglX/AY3gX/c9fH6UrQ1+r3xNAvR73z46vI4UBlReUpijrl5kfrq/BpRJ2Jt6XQJz1PTksJ/FKwd9M6Cgm8JC+uhh5bDMu35HrwWtg4/7qIeVQd7N6qyW9cHHzWk/f2DpZv6u1myDCJxNYcv3sBJ4/maHok/tPopA4tVmh2Lwcc33DyT8XeXfpLKlZgsnuhS3fGsk2ZLgl3T+XJK5cdyo4rqBFDgl20oq75xNmd65he2YOKBsautN9qlWS5moujksWanqo629tdBisY4tVwttVGZWre0XreZNc/YXghm6rE7ktXm8VXKwENX/WfAtmLBHq0cjxupzzA6uzHijQWtMAa7LsTVbobJktQ/50lj/BQ412ypaxXc2AAAAAElFTkSuQmCC) center center / 100% no-repeat;
      }
      &.full {
        border: none;
        color: rgb(255, 198, 0);
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQ4AAAB4CAMAAAA0Y+z/AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAJcEhZcwAAFiUAABYlAUlSJPAAAABCUExURUdwTAQDAgUEAvzDAA0NDQQEBAMDAxoaGggICAYGBrCKAfW+ANimAO64AOSwAOayAExBGf/GAJ19DWxZFOazA8idB4Ceh4wAAAAQdFJOUwAkK+cGFRwDCg9IxW2jfY5eMKg4AAAD5ElEQVR42u2d6ZKrIBCFNQqto1naJO//qhdBM4C0yWSWulWn+09Sxi1fHZpF4VSVxpOgiijdQH5rFT6WL2E7EVVUOMOyD8UnTc5I+f7hbPm26Pt6a5SdkeKzU+lWNvdQuuftcfRuVL9+wG+FLAiillo5KPl4KejF3elLu5B0JIkXFC8gIJlZuOjAYgFSpOF+7tFiRZLjcDA+BkaLYRgdkVwhnsaJIaMZOw8kxuFKygfzdEGL63RjPnuBpOLoT3y/IMbEfOxTHE4c/RFQHEEhzKPn8WjeucyBi8Ppo7F9nD6cOMzIN1AclzufYhxz6jC2AZbHYKL0EXC4quWCmj2aBAd1M48BVh7MDkdHcUWLLA+Hw/RthqOGlQezdfL4HFRpO2h5BBwqjzIOn0wNrjy2OKDlkeP4zB43xRHJo+Gr4gjZo+9NfYbs5pdweHkcIJPpFkeFnExLOELHZUQsLQUcizwOiMm0iIN8aUEcFSvjWPq1d8UR1S0NK46Vh7GIo0AiDicPwAcuRRxLMgXs1go4vDosXlUrqcPzwKtqyzgWeeA1TCUcoMlDVkeHOOghqqOFfP4k4KhAc+k+DrhcKuKYc6mBG0Dew+HkgVa1SDiqMCSGVrXIOJaqRXF8vhcFV7U8UcdZcSDXtPuFxXwojrhqQWt4yIVFcWw7cWhPancKi8+liiPp4iuO7IXsq+JQHKWaVnGoOhSH4tB2xzdxkOLI1VErjhiHduEUxx4OHf6JcOjgYIIDdeiYyqNh+mAhnuajj53SVhgqDtI2+p46CLLZIaqDIOvZvfc7ACuWJ89oB31zMB38uSgO2A7cExwnfQk7WjHLDLCv6BdmcPRGJ3DEL7sALkuwN4HjqHPh4kbYgDeveGfyF+ISHjszJU86rTjGoXPw0cvKDo6jLlgRr4YEufhPoVUKOvIjqWNNpJPiwE6kpcJChNmZFdSxiuOqOB7iOIKulV7A4cUBucSetCIl+Gq2LSXDYCNo5hBWs21wbQWGLY4z7DL604qDwHv2IW482tiUZMZRM8OKo9lmUlgcsyHJFgdqYZmYz9bMOPJBUrx69jrdmY+1F0e7WS8M0+pqppH0Z8PDSHtu4FgMp0Mdisqmd29tXdcHqHB/2Hoam7GfeSkCDwQobICR0wg+eY6HsQ4JULh/vNCg3FPSO0oarAi2km1pPTlvN9p3aA6bXXDYpJIDa4fnwCpY9FJF9KZ17m/EH11WNixeNPIfGiz/gEWzuP0lS+vcmDscl5axh9V3ds7UYzsy4i6YbVNVsPKmx1HFE6+G3rQYhBMJRt6JjzglnuNPKXzRCv2tn+nnLk/f20GMf04Yqlq8qI8YAAAAAElFTkSuQmCC) right center / 135px 100% no-repeat rgb(28, 28, 33);
      }
    }
  }

  border: none;
  font-family: "Roboto Flex", sans-serif;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  cursor: pointer;
  outline: none;
  margin: 0px;
  padding: 0px;
  user-select: none;
  text-transform: uppercase;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 86px;
  border-radius: 12px;
  width: 164px;

  @media #{variables.$x} {
    &.part {
      color: rgb(255, 198, 0);
      border: 1.5px solid rgb(255, 198, 0);
      background: linear-gradient(0deg, rgba(255, 198, 0, 0.2) 0%, rgba(255, 198, 0, 0.2) 100%), rgba(255, 255, 255, 0.05);
    }
    &.full {
      color: rgb(16, 17, 20);
      border: none;
      background: rgb(255, 198, 0);
    }
  }

  @media #{variables.$y} {
    &.part:disabled {
      opacity: 0.5;
      color: rgb(255, 255, 255);
      pointer-events: none;
      border: none;
      background: v-bind(partCashoutDisabledBtnCss);
    }

    &.full:disabled {
      opacity: 0.5;
      color: rgb(255, 255, 255);
      pointer-events: none;
      border: none;
      background: v-bind(fullCashoutDisabledBtnCss);
    }

    &.part {
      width: 168px;
      height: 40px;
      padding: 12px;
      border: none;
      border-radius: unset;
      background: v-bind(partCashoutBtnCss);
      font-size: 11px;
      line-height: 14px;
      color: rgb(255, 198, 0);
      justify-content: center;
    }

    &.full {
      width: 142px;
      height: 60px;
      padding: 12px;
      border: none;
      border-radius: 0px 6px 6px 0px;
      background: v-bind(fullCashoutBtnCss);
      font-size: 14px;
      line-height: 14px;
      color: rgb(16, 17, 20);
      white-space: nowrap;
      justify-content: space-around;
    }

    .currency-symbol {
      width: 10px;
      height: 10px;
      margin-top: -1px;
    }
  }

  .currency-symbol {
    width: 16px;
    height: 16px;
    margin-bottom: 1px;
    fill: currentColor;
  }
}
</style>

