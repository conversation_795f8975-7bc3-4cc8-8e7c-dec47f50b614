<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useTimerFn } from '@/composables/useTimer';
import { useGameSlotAnimStore, useGameStore } from '@/stores';
import { StepConfigs } from '@/constant/game';

import CurrencySymbol from '../CurrencySymbol.vue';

import betPreset from '@/assets/image/betPresets.png';
import betMob from '@/assets/image/betMob.png';
import emitter from '@/utils/emitter';

const betPresetCss = `url(${betPreset}) left center / cover no-repeat`;
const betMobCss = `url(${betMob}) left center / 136px 100% no-repeat rgb(28, 28, 33)`;

const { isTwistRolling, isBonusRolling, isBonusSlot } = storeToRefs(useGameSlotAnimStore());
const { gameData, gameMaxBet, gameMinBet, isSocketOnline, isFetchPending } = storeToRefs(useGameStore());

const isError = ref(false);

const inputValue = ref(gameData.value.betAmount.toString());

const errorTimer = useTimerFn(() => isError.value = false, { delay: 1000, immediate: false });

const getStepValue = (value: number, isIncrement: boolean) => {
  for (const { threshold, step } of StepConfigs) {
    if (isIncrement ? value < threshold : value <= threshold) {
      return step
    }
  }
  return 10000;
}

const clamp = (value: number, min: number, max: number) => {
  return Math.max(min, Math.min(value, max))
}

const roundToTwoDecimals = (value: number): number => {
  return Math.round(value * 100) / 100
}

const handleIncrement = () => {
  if (isBetDisabled.value) return;

  const stepValue = getStepValue(gameData.value.betAmount, true);
  const newBetValue = clamp(roundToTwoDecimals(gameData.value.betAmount + stepValue), gameMinBet.value, gameMaxBet.value);

  inputValue.value = String(newBetValue);
}

const handleDecrement = () => {
  if (isBetDisabled.value) return;

  const stepValue = getStepValue(gameData.value.betAmount, false);
  const newBetValue = clamp(roundToTwoDecimals(gameData.value.betAmount - stepValue), gameMinBet.value, gameMaxBet.value);

  inputValue.value = String(newBetValue);
}

const showError = () => {
  isError.value = true;
  errorTimer.start();
}

const isBetDisabled = computed(() => {
  return isFetchPending.value ||
    isTwistRolling.value ||
    isBonusRolling.value ||
    isBonusSlot.value ||
    !isSocketOnline.value ||
    !gameData.value.isFinished;
});

const handleBetInput = () => {
  const trimStr = inputValue.value.replace(',', '.').trim();
  const isEndWithDot = trimStr.endsWith('.');
  const [_, decimal] = trimStr.split('.');
  const decimalLen = Math.min(decimal?.length || 0, 2);
  const floatN = Number.parseFloat(trimStr);

  if (Number.isNaN(floatN)) {
    inputValue.value = '';
    return;
  }

  const absFloatN = Math.abs(floatN);

  let displayValue = String(absFloatN);

  if (absFloatN > gameMaxBet.value) {
    displayValue = gameMaxBet.value.toString();

    if (decimalLen > 0 && !displayValue.includes('.')) {
      displayValue += `.${'0'.repeat(decimalLen)}`;
    }
  } else if (decimalLen) {
    displayValue = `${Math.floor(absFloatN)}.${decimal.substring(0, decimalLen)}`;
  }

  if (isEndWithDot && !displayValue.includes('.')) {
    displayValue += '.';
  }
  
  inputValue.value = displayValue;
}

watch(inputValue, (newVal: string) => {
  gameData.value.betAmount = newVal ? Number.parseFloat(newVal) : 0;
});

onMounted(() => {
  emitter.on('handle-bet-amount-error', showError);
});

onUnmounted(() => {
  emitter.off('handle-bet-amount-error', showError);
  errorTimer.clean();
});
</script>

<template>
  <div class="bet-input" :class="{ 'bet-input-disabled': isBetDisabled, 'has-error': isError }">
    <div class="input-wrapper">
      <label class="input-label">{{ $t('twist.BET') }}</label>
      <div class="input-container">
        <CurrencySymbol class="input-symbol" />
        <input
          v-model="inputValue"
          type="text"
          inputmode="decimal"
          class="input-el"
          placeholder="0"
          :maxlength="10"
          :disabled="isBetDisabled"
          @input="handleBetInput"
        />
      </div>
    </div>

    <div class="btns-wrapper">
      <button class="btn-item" :disabled="isBetDisabled || gameData.betAmount >= gameMaxBet" @click="handleIncrement">
        <svg width="46" height="37" viewBox="0 0 46 37" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M45.3849 4.26279C46.26 2.25 44.7848 0 42.59 0H6.09543C2.72913 0 0.00018692 2.72893 0.00018692 6.09524V30.9048C0.00018692 34.2711 2.72912 37 6.09542 37H35.2769C36.8679 37 38.1912 35.7762 38.3153 34.1901L39.0616 24.6515C39.4308 19.9326 40.5826 15.308 42.4699 10.9672L45.3849 4.26279Z" fill="white" fill-opacity="0.05"></path>
          <path d="M42.5898 0.457031H6.0957C2.98187 0.457031 0.457031 2.98187 0.457031 6.0957V30.9043C0.457031 34.0181 2.98187 36.543 6.0957 36.543H35.2773C36.545 36.5428 37.6128 35.6284 37.8281 34.4033L37.8594 34.1543L38.6055 24.6162C38.9553 20.1449 40.0012 15.7573 41.7021 11.6113L42.0508 10.7852L44.9658 4.08008C45.7093 2.36932 44.4553 0.457031 42.5898 0.457031Z" stroke="white" stroke-opacity="0.1" stroke-width="0.914286"></path>
          <path d="M27.0004 21.0007L21.0004 15.0007L15.0004 21.0007" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </button>

      <button class="btn-item" :disabled="isBetDisabled || gameData.betAmount <= gameMinBet" @click="handleDecrement">
        <svg width="48" height="37" viewBox="0 0 48 37" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M46.9376 32.4555C48.0831 34.4871 46.6152 37 44.2829 37L6.09543 37C2.72912 37 0.00018692 34.2711 0.00018692 30.9048V6.09523C0.00018692 2.72893 2.72912 -3.8147e-06 6.09542 -3.8147e-06H35.2769C36.8679 -3.8147e-06 38.1912 1.22379 38.3153 2.8099L38.8832 10.0683C39.368 16.2654 41.2011 22.2816 44.254 27.6962L46.9376 32.4555Z" fill="white" fill-opacity="0.05"></path>
          <path d="M44.2832 36.543H6.0957C2.98187 36.543 0.457031 34.0181 0.457031 30.9043V6.0957C0.457031 2.98187 2.98187 0.457031 6.0957 0.457031H35.2773C36.545 0.457229 37.6128 1.37158 37.8281 2.59668L37.8594 2.8457L38.4277 10.1035C38.9025 16.1713 40.6557 22.0682 43.5703 27.4062L43.8555 27.9209L46.5391 32.6797C47.4823 34.3525 46.3413 36.4101 44.4668 36.5371L44.2832 36.543Z" stroke="white" stroke-opacity="0.1" stroke-width="0.914286"></path>
          <path d="M28.0004 17.0007L22.0004 23.0007L16.0004 17.0007" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/assets/style/variables.scss';

.bet-input {
  position: relative;
  display: flex;
  justify-content: center;
  color: rgb(255, 255, 255);

  &:hover:not(:disabled) {
    border-radius: 12px 4px 4px 12px;
    background-color: rgba(255, 255, 255, 0.02);
  }

  &.bet-input-disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  &.has-error .input-container {
    color: #ff4444;
  }

  @media #{variables.$x} {
    width: 221px;
    height: 88px;

    &::before {
      position: absolute;
      content: "";
      height: 89px;
      width: 230px;
      z-index: -1;
      cursor: pointer;
      background: v-bind(betPresetCss);
    }
  }

  @media #{variables.$y} {
    width: 144px;
    height: 60px;
    border-radius: 6px 0px 0px 6px;
    margin-left: 0px;
    background: v-bind(betMobCss);
  }
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;

  @media #{variables.$x} {
    padding: 0px 14px;
    justify-content: center;
  }

  @media #{variables.$y} {
    padding: 12px 0px 12px 12px;
    justify-content: space-around;
  }

  .input-label {
    font-weight: 700;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 2px;
    text-transform: uppercase;
    line-height: 24px;

    @media #{variables.$x} {
      font-size: 16px;
      line-height: 24px;
    }

    @media #{variables.$y} {
      font-size: 12px;
      line-height: 14px;
    }
  }

  .input-container {
    display: flex;
    align-items: center;
    margin-top: 2px;
    font-weight: 500;
    gap: 4px;
    color: white;
    transition: color 0.2s;

    @media #{variables.$x} {
      font-size: 20px;
      line-height: 24px;
    }

    @media #{variables.$y} {
      font-size: 14px;
      line-height: 14px;
    }
  }

  .input-symbol {
    width: 18px;
    height: 18px;
    margin-bottom: -1px;
    fill: currentColor;

    @media #{variables.$x} {
      min-height: 16px;
      min-width: 16px;
    }

    @media #{variables.$y} {
      min-height: 12px;
      min-width: 12px;
    }
  }

  .input-el {
    border: 0px;
    outline: 0px;
    margin: 0px;
    display: block;
    padding: 1px 0px 0px;
    width: 100%;
    background: none;
    font-weight: inherit;
    font-size: inherit;
    line-height: inherit;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.btns-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 11px;

  @media #{variables.$x} {
    padding-right: 11px;
    margin-top: -1px;
    gap: 3px;
  }

  @media #{variables.$y} {
    padding-right: 14px;
    margin-top: -1px;
    gap: 2px;
  }

  .btn-item {
    cursor: pointer;
    border: none;
    background: none;
    outline: none;
    margin: 0px;
    padding: 0px;
    display: flex;
    user-select: none;

    &:active {
      opacity: 0.6;
    }

    &:disabled {
      pointer-events: none;
      opacity: 0.5;
    }

    &:hover svg path{
      fill-opacity: 0.2;
    }
    
    @media #{variables.$y} {
      svg {
        width: 34px;
        height: 22px;
      }
    }
  }
}
</style>