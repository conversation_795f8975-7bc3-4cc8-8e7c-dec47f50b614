<script lang="ts" setup>
import { formatAmount } from '@/utils/utils';
import CurrencySymbol from '@/components/CurrencySymbol.vue';

defineProps<{
  title: string;
  value: number;
}>()

</script>

<template>
  <div id="balance" class="block-info">
    <div class="opacity-60 font-bold text-[14px] y:text-[11px]">{{ title }}</div>
    <div class="block-value">
      <CurrencySymbol class="size-[16px] y:size-[12px] fill-white" />
      <Vue3Odometer :value="Number.parseFloat(formatAmount(value, 2, false, false, false))" format="( ddd).dd" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '@/assets/style/variables.scss';

.block-info {
  display: flex;
  color: white;
  text-transform: uppercase;

  width: 164px;
  height: 100%;
  padding-inline: 16px;
  padding-block: 8px;
  flex-direction: column;
  justify-content: space-around;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.05);
}

.block-value {
  font-size: 18px;
  letter-spacing: -0.5px;
  line-height: 18px;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
}
</style>