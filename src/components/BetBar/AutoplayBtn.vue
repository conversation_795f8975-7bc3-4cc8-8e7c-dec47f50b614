<script setup lang="ts">
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { useGameAutoPlayStore, useGameStore } from "@/stores";

import AutoplaySettings from "@/components/modals/AutoplaySettings.vue"
import SpinnerIcon from "@/components/icons/SpinnerIcon.vue";
import CloseIcon from "@/components/icons/CloseIcon.vue";

import autoplayBtn from "@/assets/image/autoplayBtn.png";

const autoplayBtnCss = `url(${autoplayBtn}) right center / cover no-repeat`;

const { isSocketOnline } = storeToRefs(useGameStore());
const { state, stop } = useGameAutoPlayStore();

const isBtnDisabled = computed(() => {
  return !isSocketOnline.value;
});

const showAutoplayModal = ref(false);

const handleAutoplayBtnClick = () => {
  if (state.processing) {
    stop();
  } else {
    showAutoplayModal.value = true;
  }
}
</script>

<template>
  <div class="autoplay-btn-wrapper">
    <button class="autoplay-btn-inner" :disabled="isBtnDisabled" @click="handleAutoplayBtnClick">
      <CloseIcon v-if="state.processing" />
      <SpinnerIcon v-else />
    </button>
    <AutoplaySettings v-model:show="showAutoplayModal" />
  </div>
</template>

<style lang="scss" scoped>
.autoplay-btn-inner {
  cursor: pointer;
  border: none;
  background: none;
  outline: none;
  margin: 0px;
  padding: 0px;
  width: 127px;
  height: calc(100% - 16px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &::before {
    position: absolute;
    content: "";
    background:  v-bind(autoplayBtnCss);
    right: -6px;
    height: 89px;
    width: 136px;
    z-index: -1;
  }

  &:active {
    opacity: 0.6;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:hover:not(:disabled) {
    filter: brightness(110%);
  }
}
</style>