<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useGameStore, useMainStore, useUserStore } from '@/stores';

import ResizableBlock from '@/components/ResizableBlock/index.vue';

import BlockInfo from './BlockInfo.vue';
import UserBalance from './UserBalance.vue';
import CashoutBtn from './CashoutBtn.vue';
import BetInput from './BetInput.vue';
import PlayBtn from './PlayBtn.vue';
import AutoplayBtn from './AutoplayBtn.vue';

const { balance } = storeToRefs(useUserStore());
const { gameData } = storeToRefs(useGameStore());
const { isHorizontal, isVertical } = storeToRefs(useMainStore());
</script>

<template>
  <ResizableBlock
    :desktop="[1221, 102]"
    :mobile="[336, 112]"
    :margin-desktop="[0, 0, 20, 0]"
    :margin-mobile="[0, 12, 0, 12]"
  >
    <div v-if="isHorizontal" class="betbar-container-x">
      <div class="h-full flex gap-[12px]">
        <BlockInfo :title="$t('twist.BALANCE')" :value="balance" />
        <BlockInfo :title="$t('twist.WIN')" :value="gameData.totalWinAmount" />
      </div>
      
      <div class="flex items-center gap-[8px] flex-wrap">
        <CashoutBtn mode="part" />
        <CashoutBtn mode="full" />
        <div class="bet-panel-container">
          <BetInput />
          <PlayBtn />
          <AutoplayBtn />
        </div>
      </div>
    </div>

    <div v-if="isVertical" class="betbar-container-y">
      <UserBalance />
      <CashoutBtn mode="part" />
      <BetInput />
      <PlayBtn />
      <CashoutBtn mode="full" />
    </div>
  </ResizableBlock>
</template>

<style lang="scss" scoped>
@use "@/assets/style/variables.scss";

.betbar-container-x {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 8px;
  color: white;
  border-radius: 16px;
  background-color: rgba(28,28,33,0.6);
}

.betbar-container-y {
  display: flex;
  justify-content: space-between;
  width: 100%;
  color: rgb(255, 255, 255);
  border-radius: 16px;
  flex-flow: wrap;
  height: 102px;
  padding: 0;
  margin: 0;
  align-items: flex-start;
}

.bet-panel-container {
  position: relative;
  width: 439px;
  height: 104px;
  filter: drop-shadow(rgba(0, 0, 0, 0.5) 0px 3.405px 7.32px);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>