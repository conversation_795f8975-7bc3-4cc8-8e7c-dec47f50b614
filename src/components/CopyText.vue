<script setup lang="ts">
import copyText from "copy-to-clipboard";
import { useI18n } from "vue-i18n";

import { openMessage } from "@/components/ui/GMessage/index.ts";

defineModel();

const { t } = useI18n();

const onCopyClick = (text: string) => {
  copyText(text);
  openMessage({ message: t('global.Copied') });
}
</script>

<template>
  <div class="rounded-[6px] py-[10px] pr-[36px] pl-[16px] bg-[rgba(255,255,255,.1)] text-white text-[16px] leading-[150%] h-[43px] relative overflow-hidden">
    <slot>
      <div :title="modelValue" class="overflow-auto scrollbar-hidden select-text">{{ modelValue || '' }}</div>
    </slot>
    <svg viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[14px] absolute top-1/2 right-[18px] translate-y-[-50%] cursor-pointer" @click="onCopyClick(modelValue)">
      <path d="M4.33594 2.66683V10.6668C4.33594 11.0205 4.47641 11.3596 4.72646 11.6096C4.97651 11.8597 5.31565 12.0002 5.66927 12.0002H11.0026C11.3562 12.0002 11.6954 11.8597 11.9454 11.6096C12.1955 11.3596 12.3359 11.0205 12.3359 10.6668V4.82816C12.3359 4.65054 12.3004 4.4747 12.2315 4.31099C12.1626 4.14728 12.0616 3.99899 11.9346 3.87483L9.7246 1.7135C9.47551 1.46993 9.14099 1.33354 8.7926 1.3335H5.66927C5.31565 1.3335 4.97651 1.47397 4.72646 1.72402C4.47641 1.97407 4.33594 2.31321 4.33594 2.66683V2.66683Z" stroke="#ffffff"></path>
      <path d="M9.66797 12.0003V13.3337C9.66797 13.6873 9.52749 14.0264 9.27744 14.2765C9.0274 14.5265 8.68826 14.667 8.33464 14.667H3.0013C2.64768 14.667 2.30854 14.5265 2.05849 14.2765C1.80844 14.0264 1.66797 13.6873 1.66797 13.3337V6.00033C1.66797 5.6467 1.80844 5.30756 2.05849 5.05752C2.30854 4.80747 2.64768 4.66699 3.0013 4.66699H4.33464" stroke="#ffffff"></path>
    </svg>
  </div>
</template>

<style scoped lang="scss">

</style>