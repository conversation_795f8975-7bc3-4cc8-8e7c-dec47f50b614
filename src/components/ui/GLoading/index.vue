<template>
  <div class="g-loading"></div>
</template>

<style scoped lang="scss">
  @keyframes loader {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .g-loading {
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 3px solid rgba(255, 255, 255, .2);
    border-right: 3px solid rgba(255, 255, 255, .2);
    border-bottom: 3px solid rgba(255, 255, 255, .2);
    border-left: 3px solid white;
    transform: translateZ(0);
    animation: loader 1.1s linear infinite;
    border-radius: 50%;
    width: 27px;
    height: 27px;
  }
</style>