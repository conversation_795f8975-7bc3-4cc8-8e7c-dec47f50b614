<script setup lang="ts">
  import { Dialog, DialogPanel, DialogTitle, DialogDescription, TransitionRoot, TransitionChild } from '@headlessui/vue'
  import { watchEffect } from "vue";
  import { useMainStore } from "@/stores";

  defineProps<{
    title?: string;
    maxWidth?: number;
  }>()

  const show = defineModel<boolean>('show');

  const { setModalVisible } = useMainStore();

  function setShow(value: boolean) {
    show.value = value
  }

  watchEffect(() => {
    setModalVisible(!!show.value);
  })
</script>

<template>
  <TransitionRoot appear :show="show" as="template">
    <Dialog class="relative z-50">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-[rgba(27,29,36,0.8)] backdrop-blur-[5px]" aria-hidden="true" />
      </TransitionChild>

      <div class="fixed inset-0 w-screen">
        <div class="flex h-full overflow-y-auto scrollbar-hidden">
          <div class="px-[12px] w-full m-auto max-w-[600px] y:max-w-[450px]" :style="{ 'max-width': maxWidth ? maxWidth + 'px' : '' }">
            <TransitionChild
              as="template"
              enter="duration-0 ease-out"
              enter-from="opacity-0"
              enter-to="opacity-100 translate-y-0"
              leave="duration-200 ease-in"
              leave-from="opacity-100 translate-y-0"
              leave-to="opacity-0"
            >
              <div class="relative w-full z-[100] y:my-[12px]">
                <DialogPanel v-bind="$attrs" class="g-modal-container">
                  <div class="flex items-center justify-between text-white font-bold">
                    <slot name="title">
                      <DialogTitle class="text-[18px] min-w1600:text-[22px] leading-[150%] truncate">{{ title }}</DialogTitle>
                    </slot>
                    <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" class="cursor-pointer w-[23px] shrink-0 fill-white hover:fill-[#feb62a]" @click="setShow(false)">
                      <path d="M4.14979 3.20716C4.02405 3.08572 3.85565 3.01853 3.68085 3.02004C3.50606 3.02156 3.33885 3.09168 3.21524 3.21528C3.09164 3.33889 3.02152 3.5061 3.02001 3.68089C3.01849 3.85569 3.08568 4.02409 3.20712 4.14983L7.05712 7.99983L3.20645 11.8498C3.14278 11.9113 3.09199 11.9849 3.05705 12.0662C3.02211 12.1476 3.00372 12.235 3.00295 12.3236C3.00219 12.4121 3.01905 12.4999 3.05257 12.5818C3.08609 12.6637 3.1356 12.7382 3.19819 12.8008C3.26079 12.8634 3.33522 12.9129 3.41715 12.9464C3.49908 12.9799 3.58687 12.9968 3.67539 12.996C3.76391 12.9952 3.85139 12.9768 3.93272 12.9419C4.01406 12.907 4.08762 12.8562 4.14912 12.7925L7.99979 8.94249L11.8498 12.7925C11.9755 12.9139 12.1439 12.9811 12.3187 12.9796C12.4935 12.9781 12.6607 12.908 12.7843 12.7844C12.9079 12.6608 12.9781 12.4936 12.9796 12.3188C12.9811 12.144 12.9139 11.9756 12.7925 11.8498L8.94245 7.99983L12.7925 4.14983C12.9139 4.02409 12.9811 3.85569 12.9796 3.68089C12.9781 3.5061 12.9079 3.33889 12.7843 3.21528C12.6607 3.09168 12.4935 3.02156 12.3187 3.02004C12.1439 3.01853 11.9755 3.08572 11.8498 3.20716L7.99979 7.05716L4.14979 3.20649V3.20716Z"></path>
                    </svg>
                  </div>

                  <DialogDescription>
                    <slot />
                  </DialogDescription>
                </DialogPanel>
              </div>
            </TransitionChild>
          </div>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<style scoped lang="scss">
.g-modal-container {
  color: white;
  font-family: "Roboto Flex", sans-serif;
  font-weight: 500;

  padding: 32px;
  background: rgb(27, 29, 33);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: 0.3s;
  display: block;
  height: max-content;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 2.99px 3.987px 0px;
}
</style>