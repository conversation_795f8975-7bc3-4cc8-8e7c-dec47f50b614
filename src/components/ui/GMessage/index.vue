<template>
  <div ref="messageWrapperRef" class="g-message-inner" @click.stop>
    <div class="text-white text-[14px] flex items-center">
      <svg v-if="type === 'error'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[24px] shrink-0">
        <mask id="mask0_720_27882" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24" style="mask-type: alpha;">
          <rect width="24" height="24" fill="#D9D9D9"></rect>
        </mask>
        <g mask="url(#mask0_720_27882)">
          <path d="M1 21L12 2L23 21H1ZM4.45 19H19.55L12 6L4.45 19ZM12 18C12.2833 18 12.521 17.904 12.713 17.712C12.9043 17.5207 13 17.2833 13 17C13 16.7167 12.9043 16.4793 12.713 16.288C12.521 16.096 12.2833 16 12 16C11.7167 16 11.4793 16.096 11.288 16.288C11.096 16.4793 11 16.7167 11 17C11 17.2833 11.096 17.5207 11.288 17.712C11.4793 17.904 11.7167 18 12 18ZM11 15H13V10H11V15Z" fill="white"></path>
        </g>
      </svg>
      <svg v-else viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[16px] shrink-0">
        <path d="M5.99971 10.7998L3.66638 8.46651C3.60536 8.40475 3.5327 8.35571 3.45259 8.32225C3.37248 8.28878 3.28653 8.27155 3.19971 8.27155C3.1129 8.27155 3.02694 8.28878 2.94684 8.32225C2.86673 8.35571 2.79406 8.40475 2.73305 8.46651C2.67129 8.52752 2.62225 8.60019 2.58879 8.6803C2.55532 8.76041 2.53809 8.84636 2.53809 8.93318C2.53809 9.01999 2.55532 9.10595 2.58879 9.18605C2.62225 9.26616 2.67129 9.33883 2.73305 9.39984L5.52638 12.1932C5.78638 12.4532 6.20638 12.4532 6.46638 12.1932L13.533 5.13318C13.5948 5.07216 13.6438 4.99949 13.6773 4.91939C13.7108 4.83928 13.728 4.75333 13.728 4.66651C13.728 4.57969 13.7108 4.49374 13.6773 4.41363C13.6438 4.33353 13.5948 4.26086 13.533 4.19984C13.472 4.13808 13.3994 4.08905 13.3193 4.05558C13.2391 4.02212 13.1532 4.00488 13.0664 4.00488C12.9796 4.00488 12.8936 4.02212 12.8135 4.05558C12.7334 4.08905 12.6607 4.13808 12.5997 4.19984L5.99971 10.7998Z" fill="white"></path>
      </svg>
      <div v-if="typeof props.message === 'string'" class="px-[8px]">{{ props.message }}</div>
      <component v-else :is="props.message"/>
      <svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-[12px] w-[20px] cursor-pointer shrink-0" @click="handleClose">
        <mask id="mask0_720_27886" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20" style="mask-type: alpha;">
          <rect width="20" height="20" fill="#D9D9D9"></rect>
        </mask>
        <g mask="url(#mask0_720_27886)">
          <path d="M5.33366 15.8332L4.16699 14.6665L8.83366 9.99984L4.16699 5.33317L5.33366 4.1665L10.0003 8.83317L14.667 4.1665L15.8337 5.33317L11.167 9.99984L15.8337 14.6665L14.667 15.8332L10.0003 11.1665L5.33366 15.8332Z" fill="white"></path>
        </g>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, useTemplateRef } from "vue";
import type {MessageOptions} from "@/components/ui/GMessage/types.ts";

const props = withDefaults(defineProps<MessageOptions>(), {
  type: 'success',
  duration: 3000,
});

const emits = defineEmits<{
  (e: 'close'): void
}>();

let timer: any = null;

const messageWrapperRef = useTemplateRef('messageWrapperRef');

const bgColor = computed(() => ({
  success: '#52b466',
  error: '#ea4248',
  warning: '',
  info: ''
}[props.type || 'success']));

const handleClose = () => {
  timer && clearTimeout(timer);
  messageWrapperRef.value?.classList.add('hide');
  messageWrapperRef.value?.addEventListener('animationend', () => {
    emits('close');
  });
}

onMounted(() => {
  timer = setTimeout(handleClose, props.duration);
});

onUnmounted(() => {
  timer && clearTimeout(timer);
});
</script>

<style lang="scss" scoped>
@keyframes message-inner-show {
  0% {
    transform: translateX(50px);
  }
  25% {
    transform: translateX(-24px);
  }
  50% {
    transform: translateX(12px);
  }
  75% {
    transform: translateX(-5px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes message-inner-hide {
  0% {
    transform: translateX(0);
  }
  10% {
    transform: translateX(-20px);
  }
  30% {
    transform: translateX(-20px);
  }
  70% {
    transform: translateX(calc(100% + 50px));
  }
  100% {
    transform: translateX(calc(100% + 50px));
  }
}

.g-message-inner {
  animation: message-inner-show 0.5s cubic-bezier(0, 0.1, 0.4, 1) forwards;
}

.g-message-inner.hide {
  animation: message-inner-hide 0.3s ease-in forwards;
}

.g-message-inner {
  position: relative;
  max-width: 336px;
  min-height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 18px 30px;
  border-radius: 6px;
  background-color: v-bind(bgColor);
}
</style>


