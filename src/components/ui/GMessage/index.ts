import {createApp, h} from "vue";
import i18n from "@/i18n";

import type { MessageOptions } from "./types";
import GMessage from '@/components/ui/GMessage/index.vue';

const MAX_MESSAGE_COUNT = 3;

/**
 * 创建消息容器
 */
const createMessageWrapper = (): Element => {
  const root = document.body.querySelector('.view-box') || document.body;

  let messageWrapper = root.querySelector('.g-message-root');
  if (messageWrapper) return messageWrapper;

  messageWrapper = document.createElement('div');
  messageWrapper.classList.value = "g-message-root z-9999 fixed top-[95px] right-0 flex flex-col items-center py-[24px] pl-[50px] pr-[24px] gap-[8px] max-h-full overflow-y-auto scrollbar-hidden font-[Roboto_Flex] font-semibold";
  root.appendChild(messageWrapper);

  return messageWrapper;
}

export function openMessage (props: MessageOptions = {}) {
  const messageWrapper = createMessageWrapper();

  const msgContainer = document.createElement('div');

  const msg = createApp({
    render() {
      return h(GMessage, {
        ...props,
        onClose: close
      });
    },
  });
  msg.use(i18n);

  const close = () => {
    msg.unmount();
    msgContainer.remove();
    if (messageWrapper.childNodes.length === 0) {
      messageWrapper.remove();
    }
  }

  msg.mount(msgContainer);
  messageWrapper.insertBefore(msgContainer, messageWrapper.firstChild);
  // 超出最大展示个数，则清理旧消息
  if (messageWrapper.childNodes.length > MAX_MESSAGE_COUNT) messageWrapper.lastChild?.remove();
  messageWrapper.scrollTop = 0;
  return { close }
}