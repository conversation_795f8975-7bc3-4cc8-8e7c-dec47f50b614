<script setup lang="ts">
import { Switch } from '@headlessui/vue'

const enabled = defineModel<boolean>('modelValue');

withDefaults(defineProps<{
  activeColor?: string;
  inactiveColor?: string;
}>(), {
  activeColor: '#1bcd49',
  inactiveColor: 'rgba(255, 255, 255, 0.1)',
});
</script>

<template>
  <Switch
    v-model="enabled"
    :style="{ '--bg-color': enabled ? activeColor : inactiveColor }"
    class="relative inline-flex items-center w-[28px] h-[17px] bg-(--bg-color) cursor-pointer rounded-full transition-colors duration-200 ease-in-out outline-0"
    @keyup.prevent
  >
    <span
      aria-hidden="true"
      :class="enabled ? 'translate-x-[11px]' : 'translate-x-[1px]'"
      class="pointer-events-none inline-block size-[16px] transform rounded-full bg-white  shadow-lg ring-0 transition duration-200 ease-in-out"
    />
  </Switch>
</template>

<style scoped lang="scss">

</style>