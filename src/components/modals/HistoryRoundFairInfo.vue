<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useUserStore } from "@/stores";
import { getAssetUrl } from "@/AssetsManager";
import { formatAmountNoRounding, formatAmount } from "@/utils/utils.ts";

import GModal from '@/components/ui/GModal/index.vue';
import CopyText from "@/components/CopyText.vue";
import CurrencySymbol from "@/components/CurrencySymbol.vue";

import protoRoot from "@/proto/index";

defineProps<{
  roundInfo: protoRoot.GameHistory;
}>();
const show = defineModel<boolean>('show');
const emits = defineEmits(["show-fair-settings"]);

const { nickname, avatar } = storeToRefs(useUserStore());

const handleFairLinkClick = () => {
  emits('show-fair-settings');
}
</script>

<template>
  <GModal v-model:show="show">
    <template #title>
      <div class="text-[18px] min-w1600:text-[22px] leading-[150%] max-sm:truncate capitalize">{{ $t('global.fairness.Bet ID') }}&nbsp;#{{ roundInfo.id }}</div>
      <div class="flex justify-center items-center bg-[#ffffcf] rounded-[8px] w-[60px] h-[32px] y:h-[30px] text-[12px] text-[#3b322b] mr-[10px]">
        x{{ formatAmountNoRounding(roundInfo.multiple) }}
      </div>
    </template>
    <div class="flex flex-col mt-[24px] gap-[16px] min-w1600:gap-[32px]">
      <div>
        <div class="text-[16px] mb-[4px]">{{ $t('global.fairness.Server Seed') }}</div>
        <div class="text-[12px] mb-[12px] text-[#a1a1a1]">{{ $t('global.fairness.Generated on our side') }}</div>
        <CopyText v-model="roundInfo.serverSeed" />
      </div>

      <div>
        <div class="text-[16px] mb-[4px]">{{ $t('global.fairness.Client Seed') }}</div>
        <div class="text-[12px] mb-[12px] text-[#a1a1a1]">{{ $t('global.fairness.Generated on player side') }}</div>
        <div class="flex text-[14px] leading-[150%] font-medium min-w1600:text-[16px] gap-[8px] max-w768:flex-col">
          <div class="w-[35%] max-w768:w-full flex items-center gap-[12px]">
            <span>{{ $t('global.fairness.Player') }}:</span>
            <img :src="getAssetUrl(`av-${avatar}.png`)" alt="avatar" class="w-[24px]" />
            <div class="text-ellipsis overflow-hidden">
              {{ nickname }}
            </div>
          </div>
          <div class="flex-1 flex items-center gap-[12px] min-w-0">
            <span>{{ $t('global.fairness.Seed') }}:</span>
            <CopyText v-model="roundInfo.userSeed" class="flex-1 min-w-0" />
          </div>
        </div>
      </div>

      <div>
        <div class="text-[16px] mb-[4px]">{{ $t('global.fairness.Combined SHA512 Hash') }}</div>
        <div class="text-[12px] mb-[12px] text-[#a1a1a1]">{{ $t('global.fairness.Above seeds combined and converted to SHA512 Hash') }}</div>
        <CopyText v-model="roundInfo.hashCode" />
      </div>

      <div>
        <div class="text-[16px] mb-[12px]">{{ $t('global.fairness.Hex') }}</div>
        <CopyText v-model="roundInfo.hexCode" />
      </div>

      <div>
        <div class="text-[16px] mb-[12px]">{{ $t('global.fairness.Decimal') }}</div>
        <CopyText :model-value="Number(roundInfo.decimalCode)" />
      </div>

      <div class="flex gap-[12px] justify-between font-semibold">
        <div class="flex-1 flex flex-col gap-[4px] p-[12px] rounded-[6px] bg-white/10">
          <div class="text-[14px] leading-[18px] text-[#c2c2c2]">{{ $t('global.fairness.Result') }}</div>
          <div class="text-[16px] leading-[22px]">x{{ formatAmountNoRounding(roundInfo.multiple) }}</div>
        </div>
        <div class="flex-1 flex flex-col gap-[4px] p-[12px] rounded-[6px] bg-white/10">
          <div class="text-[14px] leading-[18px] text-[#c2c2c2]">{{ $t('global.history.Bet') }}</div>
          <div class="flex items-center gap-[4px] text-[16px] leading-[22px]">
            <CurrencySymbol />{{ formatAmount(roundInfo.betAmount) }}
          </div>
        </div>
        <div class="flex-1 flex flex-col gap-[4px] p-[12px] rounded-[6px] bg-white/10">
          <div class="text-[14px] leading-[18px] text-[#c2c2c2]">{{ $t('global.history.Win') }}</div>
          <div class="flex items-center gap-[4px] text-[16px] leading-[22px]">
            <template v-if="roundInfo.multiple">
              <CurrencySymbol/>{{ formatAmount(roundInfo.winAmount) }}
            </template>
            <template v-else>–</template>
          </div>
        </div>
      </div>

      <div class="text-[12px] leading-[15px] md:text-[14px] md:leading-[17px] text-center">
        <span>{{ $t('global.fairness.To learn more, check') }}&nbsp;</span>
        <span class="text-[#58c637] cursor-pointer" @click="handleFairLinkClick">{{ $t('global.fairness.What is Provably fair settings') }}</span>
      </div>
    </div>
  </GModal>
</template>

<style lang="scss">
</style>