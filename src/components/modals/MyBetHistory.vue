<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, nextTick, ref, watch } from 'vue';
import { useSocketFetch } from "@/composables/useSocketFetch.ts";
import { formatAmount, formatAmountNoRounding } from "@/utils/utils.ts";
import protoRoot from "@/proto/index";

import GModal from '@/components/ui/GModal/index.vue';
import CurrencySymbol from '@/components/CurrencySymbol.vue';

const show = defineModel<boolean>('show');
const emits = defineEmits(["show-round-info"]);

// 页面显示的每页数据量
const PAGE_SIZE = 7;
// 每次请求的数据量
const REQUEST_PAGE_SIZE = PAGE_SIZE * 5;
// 最大显示数据量
const MAX_DATA_COUNT = 300;

const isRequesting = ref(false);
const betRecords = ref(<protoRoot.GameHistory[]> []);
const pageNum = ref(1);
const requestPageNum = ref(1);
let total = 0;

// 展示的记录数据
const displayRecords = computed(() => betRecords.value.slice(0, pageNum.value * PAGE_SIZE));
const isLoadDisabled = computed(() => isRequesting.value || displayRecords.value.length >= Math.min(MAX_DATA_COUNT, total));

/**
 * 获取投注历史记录
 */
const fetchHistory = async () => {
  if (isRequesting.value) return;

  isRequesting.value = true;
  const { promise } = useSocketFetch('user-bet-history-req', 'user-bet-history-res', {
    page: requestPageNum.value,
    pageSize: REQUEST_PAGE_SIZE
  });
  promise.finally(() => isRequesting.value = false);

  const { data }: DecodedMessage<protoRoot.GetHistoryResponse> = await promise;
  const histories = Array.isArray(data?.histories) ? data?.histories as protoRoot.GameHistory[] : [];
  betRecords.value.push(...histories);
  total = data?.totalCount || 0;
};

/**
 * 切换页数
 */
const handlePageChange = () => {
  if (isLoadDisabled.value) return;

  isRequesting.value = true;
  pageNum.value++;
  const dataCount = pageNum.value * PAGE_SIZE;

  // 在当前已请求数据范围内，无需请求
  if (dataCount > (requestPageNum.value - 1) * REQUEST_PAGE_SIZE && dataCount <= requestPageNum.value * REQUEST_PAGE_SIZE) {
    nextTick(() => isRequesting.value = false);

    return;
  }

  isRequesting.value = false;
  requestPageNum.value = Math.ceil(dataCount / REQUEST_PAGE_SIZE);
  fetchHistory();
};

const handleRecordClick = (record: protoRoot.GameHistory) => {
  emits('show-round-info', record);
};

watch(show, (val) => {
  if (!val) return;
  pageNum.value = 1;
  requestPageNum.value = 1;
  total = 0;
  betRecords.value = [];
  fetchHistory();
});
</script>

<template>
  <GModal v-model:show="show" :title="$t('global.history.My bets history')">
    <div class="my-[24px] max-w768:my-[12px]">
      <div class="table-header">
        <div>{{ $t('global.history.Date') }}</div>
        <div>{{ $t('global.history.Bet') }}</div>
        <div>{{ $t('global.history.Mult') }}</div>
        <div>{{ $t('global.history.Win') }}</div>
      </div>

      <div class="relative" style="height: 220px; margin-right: -12px;">
        <div class="h-full overflow-hidden">
          <div class="scroll-container h-full not-hover:scrollbar-hidden overflow-y-scroll group">
            <div class="flex flex-col gap-[4px] mr-[12px] group-hover:mr-[4px]">
              <div v-for="record of displayRecords" class="table-row">
                <div class="flex whitespace-nowrap text-[14px] leading-[24px]">
                  <span>{{ dayjs(record.time).format('HH:mm') }} &nbsp;</span>
                  <span class="y:hidden">{{ dayjs(record.time).format('DD-MM-YY') }}</span>
                </div>

                <div class="flex items-center gap-[4px] cursor-pointer text-nowrap font-semibold text-[12px] min-w400:text-[14px]">
                  <CurrencySymbol class="size-[18px]" />{{ formatAmount(record.betAmount) }}
                </div>

                <div class="flex justify-center items-center rounded-[8px] w-[60px] min-w-[60px] h-[32px] y:h-[30px] mr-[10px] font-semibold text-[12px] text-[#3b322b] bg-[#ffc27d]">
                  x{{ formatAmountNoRounding(record.multiple) }}
                </div>

                <div class="flex items-center gap-[4px] font-semibold text-[14px] y:text-[12px]">
                  <template v-if="record.multiple">
                    <CurrencySymbol class="size-[18px]" />{{ formatAmount(record.winAmount) }}
                  </template>
                  <template v-else>–</template>
                </div>

                <div :data-fairness="$t('check_fairness')" class="check-icon relative" @click="handleRecordClick(record)">
                  <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[16px] cursor-pointer text-[#1bcd49] hover:text-[#feb62a]">
                    <path d="M7 8.295L5.705 7L5 7.705L7 9.705L11 5.705L10.295 5L7 8.295Z" fill="currentColor"></path>
                    <path d="M8 15L4.912 13.3535C4.03167 12.8852 3.29549 12.1859 2.78246 11.3309C2.26944 10.4758 1.99894 9.49716 2 8.5V2C2.00027 1.73486 2.10571 1.48066 2.29319 1.29319C2.48067 1.10571 2.73487 1.00026 3 1H13C13.2651 1.00026 13.5193 1.10571 13.7068 1.29319C13.8943 1.48066 13.9997 1.73486 14 2V8.5C14.0011 9.49716 13.7306 10.4758 13.2175 11.3309C12.7045 12.1859 11.9683 12.8852 11.088 13.3535L8 15ZM3 2V8.5C2.99917 9.3159 3.22055 10.1166 3.64038 10.8162C4.06021 11.5158 4.66264 12.0879 5.383 12.471L8 13.8665L10.617 12.4715C11.3374 12.0883 11.9399 11.5162 12.3597 10.8165C12.7796 10.1168 13.0009 9.31599 13 8.5V2H3Z" fill="currentColor"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <button
      :class="{
        'w-full h-[50px] rounded-[12px] shadow bg-[#05b763]': true,
        'tracking-[1px] font-bold text-[17px] uppercase cursor-pointer hover:brightness-110': true,
        'opacity-30 pointer-events-none': isLoadDisabled
      }"
      @click="handlePageChange"
    >
      {{ $t('global.history.Load more') }}
    </button>
  </GModal>
</template>

<style scoped lang="scss">
@use '@/assets/style/variables.scss';

.scroll-container {
  --scrollbar-thumb: #feb62a;
  --scrollbar-track: transparent;
  --scrollbar-width: 8px;

  &::-webkit-scrollbar {
    width: var(--scrollbar-width);
    height: var(--scrollbar-width);
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.check-icon:hover {
  &:after {
    content: attr(data-fairness);
    display: block;
    padding: 10px;
    border-radius: 6px;
    position: absolute;
    top: -12px;
    left: -120px;
    background: #1c222a;
    font-size: 12px;
  }
}

.table-header {
  padding: 8px;
  gap: 4px;
  display: grid;
  grid-template-columns: 64px 1fr 60px 1fr 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 150%;
  color: rgb(194, 194, 194);
  letter-spacing: -0.3px;

  @media #{variables.$x} {
    grid-template-columns: 144px 87px 78px 1fr 16px;
  }
}

.table-row {
  align-items: center;
  display: grid;
  grid-template-columns: 64px 1fr 60px 1fr 16px;
  padding: 8px;
  gap: 4px;
  border-radius: 6px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: rgb(255, 255, 255);
  background: rgba(255, 255, 255, 0.1);

  @media #{variables.$x} {
    grid-template-columns: 144px 87px 78px 1fr 16px;
  }
}
</style>