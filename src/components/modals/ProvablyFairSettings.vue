<script setup lang="ts">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/stores";

import GModal from '@/components/ui/GModal/index.vue';
import CopyText from "@/components/CopyText.vue";

const show = defineModel<boolean>('show');

const { clientSeed, serverSeed } = storeToRefs(useUserStore());

</script>

<template>
  <GModal v-model:show="show" :title="$t('global.menu.fair_settings')">
    <div class="mt-[4px] flex flex-col gap-[32px] y:gap-16 text-white">
      <div class="pt-[5px] pb-[12px] text-[12px] leading-[15px] text-[#a1a1a1]">{{ $t('global.provably_fair.title') }}</div>
      <div>
        <div class="text-[14px] leading-[17px]">{{ $t('global.provably_fair.seed') }}:</div>
        <div class="pt-[5px] pb-[12px] text-[12px] leading-[15px] text-[#a1a1a1]">{{ $t('global.provably_fair.seed_description') }}</div>
        <div class="text-[14px] leading-[17px] mb-[12px]">{{ $t('global.provably_fair.random') }}:</div>
        <CopyText v-model="clientSeed">
          <div class="flex flex-row-reverse justify-end items-center">
            <span :title="clientSeed" class="ml-[4px] text-[16px] leading-[150%] truncate">{{ clientSeed }}</span>
            <span class="text-[14px] leading-[17px] text-[#a1a1a1]">{{ $t('global.provably_fair.Current') }}</span>
          </div>
        </CopyText>
      </div>
      <div>
        <div class="text-[14px] leading-[17px] mb-[12px]">{{ $t('global.provably_fair.next_seed') }}:</div>
        <CopyText v-model="serverSeed" />
      </div>
      <div class="my-[14px] font-medium text-center text-[14px] leading-[17px] text-[#a1a1a1]">{{ $t('global.provably_fair.check_history') }}</div>
    </div>
  </GModal>
</template>

<style lang="scss">
</style>