<script setup lang="ts">
import Big from 'big.js';
import { computed, nextTick, ref, useTemplateRef, watch } from 'vue';
import { storeToRefs } from "pinia";
import { useGameAutoPlayStore, useGameStore } from "@/stores";
import emitter from '@/utils/emitter';

import GModal from '@/components/ui/GModal/index.vue';
import GSwitch from '@/components/ui/GSwitch/index.vue';

const show = defineModel<boolean>('show')

// 默认值
const DEFAULT_SETTINGS = {
  autoSpin: 5,
  cashDecreasesBy: 0,
  coeffIncreasesBy: 0
};

const MIN_AUTO_SPIN = 5;
const MAX_AUTO_SPIN = 100;

const SPINS_STEP = [5, 10, 15, 20, 25, 50, 75, 100];

const { gameData } = storeToRefs(useGameStore());
const { start: startAutoplay } = useGameAutoPlayStore();

const autoSpin = ref(DEFAULT_SETTINGS.autoSpin);

const cashRef = useTemplateRef('cashRef');
const cashDecreasesBy = ref(DEFAULT_SETTINGS.cashDecreasesBy);
const cashIsEndWithDot = ref(false);
const cashDisplay = computed({
  get: () => {
    return `${cashDecreasesBy.value}${cashIsEndWithDot.value ? '.' : ''}`;
  },
  set: (val: string) => {
    const str = val.replace(',', '.').trim();

    cashIsEndWithDot.value = str.endsWith('.');
    cashDecreasesBy.value = str ? parseInputNumber(Number.parseFloat(str), 0) : 0;

    nextTick(() => {
      if (cashRef.value) {
        cashRef.value.value = cashDisplay.value;
      }
    });
  }
});

const coeffRef = useTemplateRef('coeffRef');
const coeffIncreasesBy = ref(DEFAULT_SETTINGS.coeffIncreasesBy);
const coeffIsEndWithDot = ref(false);
const coeffDisplay = computed({
  get: () => {
    return `${coeffIncreasesBy.value}${coeffIsEndWithDot.value ? '.' : ''}`;
  },
  set: (val: string) => {
    const str = val.replace(',', '.').trim();

    coeffIsEndWithDot.value = str.endsWith('.');
    coeffIncreasesBy.value = str ? parseInputNumber(Number.parseFloat(str), gameData.value.coeff) : gameData.value.coeff;

    nextTick(() => {
      if (coeffRef.value) {
        coeffRef.value.value = coeffDisplay.value;
      }
    });
  }
});

const enableCashDecreasesBy = ref(false);
const enableCoeffIncreasesBy = ref(false);

function parseInputNumber(val: number, min: number): number {
  if (Number.isNaN(val)) return min;

  const [_, d] = val.toString().split('.');

  const absN = Math.abs(val);

  if (absN <= min) return min;

  if (d && d.length) {
    return Number.parseFloat(`${Math.floor(absN)}.${d.slice(0, 2)}`);
  }

  return absN;
}

const percent = computed(() => (autoSpin.value - MIN_AUTO_SPIN) / (MAX_AUTO_SPIN - MIN_AUTO_SPIN) * 100 + '%');

const disabledResetBtn = computed(() =>
  autoSpin.value === DEFAULT_SETTINGS.autoSpin &&
  !enableCashDecreasesBy.value &&
  !enableCoeffIncreasesBy.value
);

const disabledStartBtn = computed(() =>
  (enableCashDecreasesBy.value && cashDecreasesBy.value <= 0) ||
  (enableCoeffIncreasesBy.value && (coeffIncreasesBy.value <= 0 || coeffIncreasesBy.value < gameData.value.coeff))
);

const onSlidebarChange = (e: Event) => {
  const el = e.target as HTMLInputElement;
  if (!el) return;

  autoSpin.value = Number(el.value);
};

const handleDecreaseCash = () => {
  cashDecreasesBy.value = Math.max(0, new Big(cashDecreasesBy.value).minus(1).toNumber());
}

const handleIncreaseCash = () => {
  cashDecreasesBy.value = Math.min(9999999999, new Big(cashDecreasesBy.value).plus(1).toNumber());
}

const handleDecreaseCoeff = () => {
  coeffIncreasesBy.value = Math.max(gameData.value.coeff || 0, new Big(coeffIncreasesBy.value).minus(1).toNumber());
}

const handleIncreaseCoeff = () => {
  coeffIncreasesBy.value = Math.min(999999999, new Big(coeffIncreasesBy.value).plus(1).toNumber());
}

/**
 * 重置
 */
const handleReset = () => {
  autoSpin.value = DEFAULT_SETTINGS.autoSpin;
  enableCashDecreasesBy.value = false;
  enableCoeffIncreasesBy.value = false;
  cashDecreasesBy.value = DEFAULT_SETTINGS.cashDecreasesBy;
  coeffIncreasesBy.value = gameData.value.coeff;
}

/**
 * 开始自动投注
 */
const handleStart = () => {
  if (disabledStartBtn.value) return;

  startAutoplay({
    rounds: autoSpin.value,
    cashDecreasesBy: enableCashDecreasesBy.value ? cashDecreasesBy.value : 0,
    coeffIncreasesBy: enableCoeffIncreasesBy.value ? coeffIncreasesBy.value : 0
  });

  emitter.emit('handle-game-play');

  show.value = false;
}

watch(show, (val) => {
  if (!val) return;
  handleReset();
});
</script>

<template>
  <GModal v-model:show="show" :title="$t('twist.AUTOPLAY_SETTINGS')" class="border-none! bg-[#191e22]! p-[16px]! min-w-[460px] max-w-[536px] w-[460px] mx-auto">
    <template #title>
      <span class="text-[18px] font-medium">{{ $t('twist.AUTOPLAY_SETTINGS') }}</span>
    </template>
    <div class="mt-[32px] flex flex-col gap-[32px]">
      <div class="flex flex-col gap-[16px]">
        <span class="text-[16px]">{{ $t('twist.NUMBERS_OF_AUTOSPINS') }}</span>
        <div class="grid grid-cols-[repeat(4,1fr)] gap-[12px]">
          <button
            v-for="num of SPINS_STEP"
            :class="{
              'max-w-full h-auto px-[12px] py-[8px] rounded-[8px] border outline-none cursor-pointer': true,
              'text-[14px] leading-[110%] font-bold text-center': true,
              'bg-[rgba(255,255,255,0.05)] hover:bg-[#61799f] hover:shadow-[0_0_0_2px_inset_#45618e] transition-[background] duration-200': true,
              'text-[#05b763] border-[#05b763]': autoSpin === num,
              'text-white border-transparent': autoSpin !== num,
            }"
            @click="autoSpin = num"
          >
            {{ num }}
          </button>
        </div>
      </div>
      <div class="flex items-center">
        <div class="flex-1 relative">
          <input
              type="range"
              step="1"
              min="5"
              max="100"
              :value="autoSpin"
              class="slidebar w-full h-[4px] rounded-[4px] appearance-none outline-none cursor-pointer"
              :style="{ background: `linear-gradient(to right, rgb(5, 183, 99) 0%, rgb(5, 183, 99) ${ percent }, rgba(255, 255, 255, 0.05) ${ percent }, rgba(255, 255, 255, 0.05) 100%)` }"
              @input="onSlidebarChange">
          <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              class="w-[24px] absolute top-1/2 transform-[translateY(-50%)] pointer-events-none"
              :style="{
                left: percent,
                transform: `translate( ${ '-' + percent }, -50%)`
              }">
            <rect width="24" height="24" rx="4" fill="#05B763"/>
            <rect x="0.5" y="0.5" width="23" height="23" rx="3.5" stroke="white" stroke-opacity="0.1"/>
            <rect x="7" y="5" width="2" height="14" rx="1" fill="white"/>
            <rect x="15" y="5" width="2" height="14" rx="1" fill="white"/>
            <rect x="11" y="5" width="2" height="14" rx="1" fill="white"/>
          </svg>
        </div>
        <div class="w-[140px] flex justify-end items-center gap-[5px] text-[20px] font-bold leading-[110%] text-right">
          {{ autoSpin }}
        </div>
      </div>
      <div class="flex flex-col gap-[16px] text-[16px]">
        <span>{{ $t('twist.SETTINGS') }}</span>
        <div class="flex flex-col gap-[16px] text-[14px] font-medium space-y-[12px]">
          <div class="flex justify-between items-center gap-[12px]">
            <div class="flex items-center gap-[17px] leading-[150%]">
              <GSwitch v-model="enableCashDecreasesBy" class="transform-[scale(1.18)] origin-left" />
              {{ $t('twist.AUTOPLAY_RULES_1') }}
            </div>
            {{ cashDisplay }}
            <div :class="[enableCashDecreasesBy ? 'opacity-100' : 'opacity-60 pointer-events-none']" class="flex w-[156px] rounded-[8px] bg-[rgba(255,255,255,0.1)] px-[12px] py-[8px] transition-[opacity] duration-200">
              <button data-testid="minus-btn" class="w-[24px] h-[24px] p-[4px] rounded-[6px] cursor-pointer outline-none" @click="handleDecreaseCash">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.8332 8.5H3.1665" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                </svg>
              </button>
              <input
                ref="cashRef"
                v-model="cashDisplay"
                :maxlength="10"
                type="text"
                class="w-full text-center text-white text-[16px] outline-none"
                @blur="cashIsEndWithDot = false"
              >
              <button data-testid="plus-btn" class="w-[24px] h-[24px] p-[4px] rounded-[6px] cursor-pointer outline-none" @click="handleIncreaseCash">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8.422 2.50467L8.5 2.5C8.66329 2.50002 8.82089 2.55997 8.94291 2.66848C9.06494 2.77698 9.1429 2.9265 9.162 3.08867L9.16667 3.16667V7.83333H13.8333C13.9966 7.83335 14.1542 7.8933 14.2762 8.00181C14.3983 8.11032 14.4762 8.25983 14.4953 8.422L14.5 8.5C14.5 8.66329 14.44 8.82089 14.3315 8.94291C14.223 9.06494 14.0735 9.1429 13.9113 9.162L13.8333 9.16667H9.16667V13.8333C9.16665 13.9966 9.1067 14.1542 8.99819 14.2762C8.88968 14.3983 8.74017 14.4762 8.578 14.4953L8.5 14.5C8.33671 14.5 8.17911 14.44 8.05709 14.3315C7.93506 14.223 7.8571 14.0735 7.838 13.9113L7.83333 13.8333V9.16667H3.16667C3.00338 9.16665 2.84578 9.1067 2.72375 8.99819C2.60173 8.88968 2.52377 8.74017 2.50467 8.578L2.5 8.5C2.50002 8.33671 2.55997 8.17911 2.66848 8.05709C2.77698 7.93506 2.9265 7.8571 3.08867 7.838L3.16667 7.83333H7.83333V3.16667C7.83335 3.00338 7.8933 2.84578 8.00181 2.72375C8.11032 2.60173 8.25983 2.52377 8.422 2.50467L8.5 2.5L8.422 2.50467Z" fill="white"></path>
                </svg>
              </button>
            </div>
          </div>

          <div class="flex justify-between items-center gap-[12px]">
            <div class="flex items-center gap-[17px] leading-[150%]">
              <GSwitch v-model="enableCoeffIncreasesBy" class="transform-[scale(1.18)] origin-left" />
              {{ $t('twist.AUTOPLAY_RULES_2') }}
            </div>
            <div :class="[enableCoeffIncreasesBy ? 'opacity-100' : 'opacity-60 pointer-events-none']" class="flex w-[156px] rounded-[8px] bg-[rgba(255,255,255,0.1)] px-[12px] py-[8px] transition-[opacity] duration-200">
              <button data-testid="minus-btn" class="w-[24px] h-[24px] p-[4px] rounded-[6px] cursor-pointer outline-none" @click="handleDecreaseCoeff">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.8332 8.5H3.1665" stroke="white" stroke-width="2" stroke-linecap="round"></path>
                </svg>
              </button>
              <input
                ref="coeffRef"
                v-model="coeffDisplay"
                :maxlength="9"
                type="text"
                class="w-full text-center text-white text-[16px] outline-none"
                @blur="coeffIsEndWithDot = false"
              >
              <button data-testid="plus-btn" class="w-[24px] h-[24px] p-[4px] rounded-[6px] cursor-pointer outline-none" @click="handleIncreaseCoeff">
                <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8.422 2.50467L8.5 2.5C8.66329 2.50002 8.82089 2.55997 8.94291 2.66848C9.06494 2.77698 9.1429 2.9265 9.162 3.08867L9.16667 3.16667V7.83333H13.8333C13.9966 7.83335 14.1542 7.8933 14.2762 8.00181C14.3983 8.11032 14.4762 8.25983 14.4953 8.422L14.5 8.5C14.5 8.66329 14.44 8.82089 14.3315 8.94291C14.223 9.06494 14.0735 9.1429 13.9113 9.162L13.8333 9.16667H9.16667V13.8333C9.16665 13.9966 9.1067 14.1542 8.99819 14.2762C8.88968 14.3983 8.74017 14.4762 8.578 14.4953L8.5 14.5C8.33671 14.5 8.17911 14.44 8.05709 14.3315C7.93506 14.223 7.8571 14.0735 7.838 13.9113L7.83333 13.8333V9.16667H3.16667C3.00338 9.16665 2.84578 9.1067 2.72375 8.99819C2.60173 8.88968 2.52377 8.74017 2.50467 8.578L2.5 8.5C2.50002 8.33671 2.55997 8.17911 2.66848 8.05709C2.77698 7.93506 2.9265 7.8571 3.08867 7.838L3.16667 7.83333H7.83333V3.16667C7.83335 3.00338 7.8933 2.84578 8.00181 2.72375C8.11032 2.60173 8.25983 2.52377 8.422 2.50467L8.5 2.5L8.422 2.50467Z" fill="white"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-between gap-[16px] text-[14px] font-bold">
        <button
            :class="[disabledResetBtn ? 'opacity-60 cursor-not-allowed' : 'opacity-100']"
            class="outline-none cursor-pointer uppercase bg-[rgba(255,255,255,0.05)] hover:bg-[rgba(255,255,255,0.1)] active:opacity-60 border border-[rgba(255,255,255,0.2)] flex-1 rounded-[8px] p-[12px] transition-[background] duration-300 ease-in-out"
            @click="handleReset">
          {{ $t('twist.RESET') }}
        </button>
        <button
            :class="[disabledStartBtn ? 'opacity-60 cursor-not-allowed' : 'opacity-100']"
            class="outline-none cursor-pointer uppercase bg-[#05b763] hover:bg-linear-[0deg,rgba(255,255,255,0.15)_0%,rgba(255,255,255,0.15)_100%,rgb(5,183,99)] active:opacity-60 border border-[rgba(255,255,255,0.2)] flex-1 rounded-[8px] p-[12px] transition-[background] duration-300 ease-in-out"
            @click="handleStart">
          {{ $t('twist.START') }}&nbsp;({{ autoSpin }})
        </button>
      </div>
    </div>
  </GModal>
</template>

<style lang="scss">

</style>