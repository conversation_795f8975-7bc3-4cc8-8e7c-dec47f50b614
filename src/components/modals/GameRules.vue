<script setup lang="ts">
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { useGameStore, useUserStore } from "@/stores";
import { formatAmount } from "@/utils/utils.ts";

import GModal from '@/components/ui/GModal/index.vue';

const show = defineModel<boolean>('show');

const { t } = useI18n();

const { currency } = storeToRefs(useUserStore());
const { gameMinBet, gameMaxBet, gameMaxWin } = storeToRefs(useGameStore());

const rules = ref([
  { title: t('global.game-rules.Min bet'), value: gameMinBet.value },
  { title: t('global.game-rules.Max bet'), value: gameMaxBet.value },
  { title: t('global.game-rules.Max win'), value: gameMaxWin.value }
]);
</script>

<template>
  <GModal v-model:show="show" :title="$t('global.game-rules.Game rules')">
    <div class="mt-[4px] text-[12px] text-[#a1a1a1] leading-[15px]">{{ $t('global.game-rules.Bet limits are presented below') }}</div>
    <div v-for="rule of rules" class="flex justify-between items-center text-[16px] p-[10px_36px_9px_16px] bg-white/10 rounded-[8px] mt-[24px] y:mt-[16px]">
      <span class="text-white/50 leading-[16px] font-semibold shrink-0">{{ rule.title }}</span>
      <span class="text-white leading-[150%] font-bold">{{ formatAmount(rule.value) }}&nbsp;{{ currency }}</span>
    </div>
  </GModal>
</template>

<style scoped lang="scss">
</style>