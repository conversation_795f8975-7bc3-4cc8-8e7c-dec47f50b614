<script setup lang="ts">
import { ref, watch } from "vue";
import { useUserStore } from "@/stores";
import { storeToRefs } from "pinia";
import { useSocketFetch } from "@/composables/useSocketFetch.ts";
import { getAssetUrl } from "@/AssetsManager";

import GModal from '@/components/ui/GModal/index.vue';

const show = defineModel<boolean>('show');

const { setAvatar } = useUserStore();
const { avatar } = storeToRefs(useUserStore());

const tempAvatar = ref(avatar.value);
const isRequesting = ref(false);

const onAvatarClick = (n: number) => {
  tempAvatar.value = n.toString();
}

const save = async () => {
  if (isRequesting.value) return;
  isRequesting.value = true;
  const { promise } = useSocketFetch('user-update-avatar-req', 'user-update-avatar-res', {
    avatar: tempAvatar.value
  });
  promise.finally(() => (isRequesting.value = false));
  await promise;
  setAvatar(tempAvatar.value);
  show.value = false;
}

watch(show, val => {
  if (!val) return;

  tempAvatar.value = avatar.value;
});
</script>

<template>
  <GModal v-model:show="show" :title="$t('global.menu.change_avatar')">
    <div class="flex flex-wrap justify-center min-w1200:my-[24px] min-w1200:gap-y-[24px] my-[16px] gap-y-[16px]">
      <div v-for="n of 12" class="min-w480:w-1/4 w-1/3 flex justify-center">
        <div :class="[tempAvatar === n.toString() ? 'border-[#feb62a]' : 'border-transparent']" class="border-4 h-fit max-w-[78px] flex rounded-full relative">
          <img :src="getAssetUrl(`av-${n}.png`)" class="size-[calc(100%+2px)] m-[-1px] cursor-pointer" :alt="'img_avatar_' + n" draggable="false" @click="onAvatarClick(n)" />
          <div v-if="tempAvatar === n.toString()" class="size-[24px] rounded-full absolute top-[-4px] right-[-4px] bg-[#feb62a] flex justify-center items-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 15" fill="none" class="w-[16px]">
              <path d="M5.99996 9.79212L3.66662 7.67639C3.60561 7.62039 3.53294 7.57593 3.45283 7.54559C3.37273 7.51524 3.28677 7.49962 3.19996 7.49962C3.11314 7.49962 3.02719 7.51524 2.94708 7.54559C2.86697 7.57593 2.7943 7.62039 2.73329 7.67639C2.67153 7.73172 2.6225 7.79761 2.58903 7.87025C2.55556 7.94288 2.53833 8.02082 2.53833 8.09954C2.53833 8.17826 2.55556 8.2562 2.58903 8.32883C2.6225 8.40147 2.67153 8.46736 2.73329 8.52268L5.52662 11.0555C5.78662 11.2913 6.20662 11.2913 6.46662 11.0555L13.5333 4.65393C13.595 4.5986 13.6441 4.53271 13.6776 4.46008C13.711 4.38744 13.7282 4.3095 13.7282 4.23078C13.7282 4.15206 13.711 4.07412 13.6776 4.00149C13.6441 3.92885 13.595 3.86296 13.5333 3.80764C13.4723 3.75164 13.3996 3.70718 13.3195 3.67683C13.2394 3.64648 13.1534 3.63086 13.0666 3.63086C12.9798 3.63086 12.8939 3.64648 12.8137 3.67683C12.7336 3.70718 12.661 3.75164 12.6 3.80764L5.99996 9.79212Z" fill="#12151E"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
    <div :class="[isRequesting ? 'opacity-50 pointer-events-none' : '']"
         class="h-[50px] bg-[#05b763] hover:brightness-110 flex justify-center items-center cursor-pointer text-[16px] leading-[1.15] font-bold tracking-[1px] uppercase rounded-[12px] drop-shadow-[0_4px_4px_rgba(0,0,0,0.25)]"
         @click="save">
      <span class="truncate">{{ $t('global.menu.save_&_close') }}</span>
    </div>
  </GModal>
</template>

<style lang="scss">
</style>