<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from "vue-i18n";

import GModal from '@/components/ui/GModal/index.vue';

const show = defineModel<boolean>('show');

const { t } = useI18n();

const steps = ref([
  t('twist.HOW_TO_PLAY_1'),
  t('twist.HOW_TO_PLAY_2'),
  t('twist.HOW_TO_PLAY_3'),
  t('twist.HOW_TO_PLAY_4'),
]);
</script>

<template>
  <GModal v-model:show="show" :title="$t('global.How to play')">
    <ol class="pl-[15px] pt-[14px] list-decimal text-white text-[14px] leading-[17px] font-semibold whitespace-pre-wrap">
      <li v-for="step of steps" class="mt-[16px] pl-[21px]">
        {{ step }}
      </li>
    </ol>
  </GModal>
</template>

<style lang="scss">
</style>