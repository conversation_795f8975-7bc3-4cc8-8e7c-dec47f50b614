<script setup lang="ts">
import { useTemplateRef } from 'vue';
import { useResize } from '@/composables/useResize';
import { BaseLayout } from '@/constant';

type Size = [number, number];
type Margin = [number, number, number, number];

const props = withDefaults(defineProps<{
  desktop: Size;
  mobile: Size;
  preventScale?: boolean;
  marginDesktop?: Margin;
  marginMobile?: Margin;
  contextOnly?: boolean;
  maxScale?: number;
  fullWidthMobile?: boolean;
  fullWidthDesktop?: boolean;
}>(), {
  preventScale: false,
  contextOnly: false,
  maxScale: 999,
  fullWidthDesktop: false,
  fullWidthMobile: false,
});

const wrapperRef = useTemplateRef('wrapperRef');
const contentRef = useTemplateRef('contentRef');

const getWidthInfo = () => {
  const ww = window.innerWidth;
  const wh = window.innerHeight;

  const desktopOffset = BaseLayout.desktopHeaderHeight + BaseLayout.desktopHeaderMarginBottom;
  const mobileOffset = BaseLayout.mobileHeaderHeight + BaseLayout.mobileHeaderMarginBottom;

  const isMobileByWidth = ww / wh <= BaseLayout.breakpointAspectRatio;
  const isMobileByAdjustedHeight = ww / (wh - (isMobileByWidth ? mobileOffset : desktopOffset)) <= BaseLayout.breakpointAspectRatio;
  
  const edgeChanged = isMobileByWidth !== isMobileByAdjustedHeight;

  return {
    offset: isMobileByAdjustedHeight || edgeChanged ? mobileOffset : desktopOffset,
    isOnEdge: edgeChanged
  }
}

const updateLayout = () => {
  const { offset, isOnEdge } = getWidthInfo();
  const ww = window.innerWidth;
  const wh = window.innerHeight - offset;

  const isMobile = ww / wh <= BaseLayout.breakpointAspectRatio || isOnEdge;
  const padding = isMobile ? props.marginMobile : props.marginDesktop;
  const [width, height] = isMobile ? props.mobile : props.desktop;

  const scale = Math.min(
    ww / (isMobile ? BaseLayout.mobileViewportX : BaseLayout.desktopViewportX),
    wh / (isMobile ? BaseLayout.mobileViewportY : BaseLayout.desktopViewportY),
    props.maxScale
  );

  if (props.preventScale) {
    contentRef.value!.style.width = `${width * scale}px`;
    contentRef.value!.style.height = `${height * scale}px`
  } else if (!props.contextOnly) {
    const adjustedWidth = padding ? ww - padding[1] * scale - padding[3] * scale : ww;
    const mobileFull = isMobile && props.fullWidthMobile;
    const desktopFull = !isMobile && props.fullWidthDesktop;
    const finalWidth = mobileFull || desktopFull ? adjustedWidth : width * scale;

    wrapperRef.value!.style.width = `${finalWidth}px`;
    wrapperRef.value!.style.height = `${height * scale}px`;

    contentRef.value!.style.transform = `scale(${scale})`;
    contentRef.value!.style.width = `${finalWidth / scale}px`;
    contentRef.value!.style.height = `${height}px`;
  }

  if (padding) {
    const [top, right, bottom, left] = padding;
    wrapperRef.value!.style.paddingTop = `${top * scale}px`;
    wrapperRef.value!.style.paddingRight = `${right * scale}px`;
    wrapperRef.value!.style.paddingBottom = `${bottom * scale}px`;
    wrapperRef.value!.style.paddingLeft = `${left * scale}px`;
  }
}

useResize(document.documentElement, () => updateLayout());
</script>

<template>
  <div ref="wrapperRef" class="box-content">
    <div ref="contentRef" class="relative origin-top-left">
      <slot />
    </div>
  </div>
</template>