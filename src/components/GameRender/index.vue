<script setup lang="ts">
import { Application } from 'pixi.js';
import { initDevtools } from '@pixi/devtools';
import { onMounted, useTemplateRef, onUnmounted, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { useMainStore } from '@/stores';
import { createAudio } from '@/AudioManager';
import { getResolution } from '@/GameManager/engine';
import { emitTick } from '@/GameManager/GlobalTick';
import { MainScreen } from '@/GameManager/MainScreen';
import { useResize } from '@/composables/useResize';

const { musicEnable, soundEnable } = storeToRefs(useMainStore());

const root = useTemplateRef('rootRef');

const audio = createAudio();

const app = new Application();

const { manual } = useResize(root, (entry: ResizeObserverEntry) => {
  const { width, height } = entry.contentRect;
  app.renderer.resize(width, height, getResolution());
  app.render();
}, {
  isManual: true,
  immediate: true,
});

onMounted(async () => {
  if (!root.value) throw new Error('Root element not found');
  
  audio.sfx.setVolume(0.5);
  audio.bgm.setVolume(0.1);
  audio.bgm.play('sounds/music.mp3');
  watchEffect(() => musicEnable.value ? audio.bgm.unmute() : audio.bgm.mute());
  watchEffect(() => soundEnable.value ? audio.sfx.unmute() : audio.sfx.mute());

  await app.init({
    // resizeTo: root.value,
    autoDensity: true,
    // antialias: true,
    // roundPixels: true,
    resolution: getResolution(),
    backgroundAlpha: 0,
    powerPreference: 'high-performance',
  });

  app.stage.addChild(new MainScreen(app));

  app.ticker.add(emitTick)

  root.value.appendChild(app.canvas);

  manual();

  initDevtools({ app });

  window.__PIXI_APP__ = app;
});

onUnmounted(() => {
  app.destroy(false, { children: true });
});
</script>

<template>
  <div ref="rootRef" class="absolute overflow-hidden size-full"></div>
</template>

<style lang="scss" scoped>

</style>