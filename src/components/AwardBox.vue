<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { useMainStore } from "@/stores";
import { useTimer } from "@/composables/useTimer.ts";
import { formatAmount } from "@/utils/utils.ts";
import emitter from "@/utils/emitter.ts";

import CurrencySymbol from "@/components/CurrencySymbol.vue";
import winNotification from "@/assets/image/win-notification.png"
import winNotificationMobile from "@/assets/image/win-notification-mobile.png"

const { isHorizontal } = storeToRefs(useMainStore());
const { start: startHidden, clean: cleanHidden } = useTimer();

const showAwardBox = ref(false);
const gameAwardAmount = ref(0);

/**
 * 动画播放完毕
 */
const handleAnimationEnd = () => {
  showAwardBox.value = false;
};

/**
 * 播放结算动画
 * @param amount
 */
const handlePlayAnimation = (amount: number) => {
  gameAwardAmount.value = amount;
  showAwardBox.value = true;
  startHidden(handleAnimationEnd, 1.5e3);
};

onMounted(() => {
  emitter.on('play-award-animation', handlePlayAnimation);
});

onUnmounted(() => {
  emitter.off('play-award-animation', handlePlayAnimation);
  cleanHidden();
});
</script>

<template>
  <div
    v-if="showAwardBox"
      class="award-box absolute left-1/2 top-0 z-[999] -translate-x-1/2 flex w-[298px] h-[127px] y:w-[260px] y:h-[98px] flex-col items-center bg-no-repeat bg-size-[100%] leading-[150%] font-black font-['Roboto_Flex',sans-serif]"
      :style="{
        'background-image': `url(${isHorizontal ? winNotification : winNotificationMobile})`
      }"
    >
    <div class="text-[#342d24] text-[20px] y:text-[12px] uppercase mt-[33px] y:mt-[27px]">
      {{ $t('global.fairness.Win') }}
    </div>
    <div class="mt-[25px] y:mt-[15px] text-[30px] y:text-[18px] flex items-center gap-[4px] text-shadow-[rgb(20,27,21)_0_1.5_0]">
      <span>{{ formatAmount(gameAwardAmount, 2, false, false, false) }}</span>
      <CurrencySymbol class="size-[22px] y:size-[14px]"/>
    </div>
  </div>
</template>

<style scoped lang="scss">
@keyframes shake {
  0% {
    transform: scale(0.8);
    opacity: 0;
    filter: brightness(1);
  }
  20% {
    transform: scale(1.1);
    opacity: 1;
    filter: brightness(1.1);
  }
  40% {
    transform: scale(1);
    opacity: 1;
    filter: brightness(1.3);
  }
  60% {
    transform: scale(1.05);
    opacity: 1;
    filter: brightness(1.2);
  }
  80% {
    transform: scale(1);
    opacity: 1;
    filter: brightness(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
    filter: brightness(1);
  }
}

.award-box {
  animation: 1.5s ease-out shake;
}
</style>