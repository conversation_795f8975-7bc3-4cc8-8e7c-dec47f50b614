/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from "protobufjs/minimal";

// Common aliases
const $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
const $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

/**
 * ServerType enum.
 * @exports ServerType
 * @enum {number}
 * @property {number} NoN=0 NoN value
 * @property {number} Crash=1000 Crash value
 * @property {number} FortuneGems=1001 FortuneGems value
 * @property {number} FortuneGems1=1002 FortuneGems1 value
 * @property {number} FortuneGems2=1003 FortuneGems2 value
 * @property {number} SuperAce=1004 SuperAce value
 * @property {number} CrazySeven=1005 CrazySeven value
 * @property {number} LegacyOfEgyptSeven=1007 LegacyOfEgyptSeven value
 * @property {number} MoneyPot=1008 MoneyPot value
 * @property {number} FruityWheel=1009 FruityWheel value
 * @property {number} Aviator=1010 Aviator value
 * @property {number} MoneyComing=1011 MoneyComing value
 * @property {number} mahjong=1012 mahjong value
 * @property {number} BoxingKing=1013 BoxingKing value
 * @property {number} WinGo=1014 WinGo value
 * @property {number} SevenSevenSeven=1015 SevenSevenSeven value
 * @property {number} ChargeBuffalo3=1016 ChargeBuffalo3 value
 * @property {number} THREE_COIN_TREASURES=1017 THREE_COIN_TREASURES value
 * @property {number} SuperRich=1018 SuperRich value
 * @property {number} CHICKEN_ROAD=1019 CHICKEN_ROAD value
 * @property {number} CHICKEN_ROAD2=1020 CHICKEN_ROAD2 value
 * @property {number} POSEIDON=1021 POSEIDON value
 * @property {number} TWIST=1022 TWIST value
 * @property {number} WinGo2=1023 WinGo2 value
 */
export const ServerType = $root.ServerType = (() => {
    const valuesById = {}, values = Object.create(valuesById);
    values[valuesById[0] = "NoN"] = 0;
    values[valuesById[1000] = "Crash"] = 1000;
    values[valuesById[1001] = "FortuneGems"] = 1001;
    values[valuesById[1002] = "FortuneGems1"] = 1002;
    values[valuesById[1003] = "FortuneGems2"] = 1003;
    values[valuesById[1004] = "SuperAce"] = 1004;
    values[valuesById[1005] = "CrazySeven"] = 1005;
    values[valuesById[1007] = "LegacyOfEgyptSeven"] = 1007;
    values[valuesById[1008] = "MoneyPot"] = 1008;
    values[valuesById[1009] = "FruityWheel"] = 1009;
    values[valuesById[1010] = "Aviator"] = 1010;
    values[valuesById[1011] = "MoneyComing"] = 1011;
    values[valuesById[1012] = "mahjong"] = 1012;
    values[valuesById[1013] = "BoxingKing"] = 1013;
    values[valuesById[1014] = "WinGo"] = 1014;
    values[valuesById[1015] = "SevenSevenSeven"] = 1015;
    values[valuesById[1016] = "ChargeBuffalo3"] = 1016;
    values[valuesById[1017] = "THREE_COIN_TREASURES"] = 1017;
    values[valuesById[1018] = "SuperRich"] = 1018;
    values[valuesById[1019] = "CHICKEN_ROAD"] = 1019;
    values[valuesById[1020] = "CHICKEN_ROAD2"] = 1020;
    values[valuesById[1021] = "POSEIDON"] = 1021;
    values[valuesById[1022] = "TWIST"] = 1022;
    values[valuesById[1023] = "WinGo2"] = 1023;
    return values;
})();

export const EnterRoomRequest = $root.EnterRoomRequest = (() => {

    /**
     * Properties of an EnterRoomRequest.
     * @exports IEnterRoomRequest
     * @interface IEnterRoomRequest
     */

    /**
     * Constructs a new EnterRoomRequest.
     * @exports EnterRoomRequest
     * @classdesc Represents an EnterRoomRequest.
     * @implements IEnterRoomRequest
     * @constructor
     * @param {IEnterRoomRequest=} [properties] Properties to set
     */
    function EnterRoomRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * Creates a new EnterRoomRequest instance using the specified properties.
     * @function create
     * @memberof EnterRoomRequest
     * @static
     * @param {IEnterRoomRequest=} [properties] Properties to set
     * @returns {EnterRoomRequest} EnterRoomRequest instance
     */
    EnterRoomRequest.create = function create(properties) {
        return new EnterRoomRequest(properties);
    };

    /**
     * Encodes the specified EnterRoomRequest message. Does not implicitly {@link EnterRoomRequest.verify|verify} messages.
     * @function encode
     * @memberof EnterRoomRequest
     * @static
     * @param {IEnterRoomRequest} message EnterRoomRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    EnterRoomRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        return writer;
    };

    /**
     * Encodes the specified EnterRoomRequest message, length delimited. Does not implicitly {@link EnterRoomRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof EnterRoomRequest
     * @static
     * @param {IEnterRoomRequest} message EnterRoomRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    EnterRoomRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an EnterRoomRequest message from the specified reader or buffer.
     * @function decode
     * @memberof EnterRoomRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {EnterRoomRequest} EnterRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    EnterRoomRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.EnterRoomRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes an EnterRoomRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof EnterRoomRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {EnterRoomRequest} EnterRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    EnterRoomRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an EnterRoomRequest message.
     * @function verify
     * @memberof EnterRoomRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    EnterRoomRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        return null;
    };

    /**
     * Creates an EnterRoomRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof EnterRoomRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {EnterRoomRequest} EnterRoomRequest
     */
    EnterRoomRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.EnterRoomRequest)
            return object;
        return new $root.EnterRoomRequest();
    };

    /**
     * Creates a plain object from an EnterRoomRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof EnterRoomRequest
     * @static
     * @param {EnterRoomRequest} message EnterRoomRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    EnterRoomRequest.toObject = function toObject() {
        return {};
    };

    /**
     * Converts this EnterRoomRequest to JSON.
     * @function toJSON
     * @memberof EnterRoomRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    EnterRoomRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for EnterRoomRequest
     * @function getTypeUrl
     * @memberof EnterRoomRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    EnterRoomRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/EnterRoomRequest";
    };

    return EnterRoomRequest;
})();

export const EnterRoomResponse = $root.EnterRoomResponse = (() => {

    /**
     * Properties of an EnterRoomResponse.
     * @exports IEnterRoomResponse
     * @interface IEnterRoomResponse
     * @property {number|null} [errorCode] EnterRoomResponse errorCode
     * @property {Array.<number>|null} [steps] EnterRoomResponse steps
     * @property {number|null} [betAmount] EnterRoomResponse betAmount
     * @property {boolean|null} [canCashOut] EnterRoomResponse canCashOut
     * @property {number|null} [minBetAmount] EnterRoomResponse minBetAmount
     * @property {number|null} [maxBetAmount] EnterRoomResponse maxBetAmount
     * @property {number|null} [defaultBetAmount] EnterRoomResponse defaultBetAmount
     * @property {number|null} [maxWinAmount] EnterRoomResponse maxWinAmount
     * @property {Array.<number>|null} [multiples_1] EnterRoomResponse multiples_1
     * @property {Array.<number>|null} [multiples_2] EnterRoomResponse multiples_2
     * @property {Array.<number>|null} [multiples_3] EnterRoomResponse multiples_3
     * @property {string|null} [avatar] EnterRoomResponse avatar
     * @property {string|null} [nickname] EnterRoomResponse nickname
     * @property {number|null} [userGold] EnterRoomResponse userGold
     * @property {string|null} [clientSeed] EnterRoomResponse clientSeed
     * @property {string|null} [serverSeed] EnterRoomResponse serverSeed
     */

    /**
     * Constructs a new EnterRoomResponse.
     * @exports EnterRoomResponse
     * @classdesc Represents an EnterRoomResponse.
     * @implements IEnterRoomResponse
     * @constructor
     * @param {IEnterRoomResponse=} [properties] Properties to set
     */
    function EnterRoomResponse(properties) {
        this.steps = [];
        this.multiples_1 = [];
        this.multiples_2 = [];
        this.multiples_3 = [];
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * EnterRoomResponse errorCode.
     * @member {number} errorCode
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.errorCode = 0;

    /**
     * EnterRoomResponse steps.
     * @member {Array.<number>} steps
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.steps = $util.emptyArray;

    /**
     * EnterRoomResponse betAmount.
     * @member {number} betAmount
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.betAmount = 0;

    /**
     * EnterRoomResponse canCashOut.
     * @member {boolean} canCashOut
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.canCashOut = false;

    /**
     * EnterRoomResponse minBetAmount.
     * @member {number} minBetAmount
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.minBetAmount = 0;

    /**
     * EnterRoomResponse maxBetAmount.
     * @member {number} maxBetAmount
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.maxBetAmount = 0;

    /**
     * EnterRoomResponse defaultBetAmount.
     * @member {number} defaultBetAmount
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.defaultBetAmount = 0;

    /**
     * EnterRoomResponse maxWinAmount.
     * @member {number} maxWinAmount
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.maxWinAmount = 0;

    /**
     * EnterRoomResponse multiples_1.
     * @member {Array.<number>} multiples_1
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.multiples_1 = $util.emptyArray;

    /**
     * EnterRoomResponse multiples_2.
     * @member {Array.<number>} multiples_2
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.multiples_2 = $util.emptyArray;

    /**
     * EnterRoomResponse multiples_3.
     * @member {Array.<number>} multiples_3
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.multiples_3 = $util.emptyArray;

    /**
     * EnterRoomResponse avatar.
     * @member {string} avatar
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.avatar = "";

    /**
     * EnterRoomResponse nickname.
     * @member {string} nickname
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.nickname = "";

    /**
     * EnterRoomResponse userGold.
     * @member {number} userGold
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.userGold = 0;

    /**
     * EnterRoomResponse clientSeed.
     * @member {string} clientSeed
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.clientSeed = "";

    /**
     * EnterRoomResponse serverSeed.
     * @member {string} serverSeed
     * @memberof EnterRoomResponse
     * @instance
     */
    EnterRoomResponse.prototype.serverSeed = "";

    /**
     * Creates a new EnterRoomResponse instance using the specified properties.
     * @function create
     * @memberof EnterRoomResponse
     * @static
     * @param {IEnterRoomResponse=} [properties] Properties to set
     * @returns {EnterRoomResponse} EnterRoomResponse instance
     */
    EnterRoomResponse.create = function create(properties) {
        return new EnterRoomResponse(properties);
    };

    /**
     * Encodes the specified EnterRoomResponse message. Does not implicitly {@link EnterRoomResponse.verify|verify} messages.
     * @function encode
     * @memberof EnterRoomResponse
     * @static
     * @param {IEnterRoomResponse} message EnterRoomResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    EnterRoomResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.steps != null && message.steps.length) {
            writer.uint32(/* id 2, wireType 2 =*/18).fork();
            for (let i = 0; i < message.steps.length; ++i)
                writer.int32(message.steps[i]);
            writer.ldelim();
        }
        if (message.betAmount != null && Object.hasOwnProperty.call(message, "betAmount"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.betAmount);
        if (message.canCashOut != null && Object.hasOwnProperty.call(message, "canCashOut"))
            writer.uint32(/* id 4, wireType 0 =*/32).bool(message.canCashOut);
        if (message.minBetAmount != null && Object.hasOwnProperty.call(message, "minBetAmount"))
            writer.uint32(/* id 5, wireType 1 =*/41).double(message.minBetAmount);
        if (message.maxBetAmount != null && Object.hasOwnProperty.call(message, "maxBetAmount"))
            writer.uint32(/* id 6, wireType 1 =*/49).double(message.maxBetAmount);
        if (message.defaultBetAmount != null && Object.hasOwnProperty.call(message, "defaultBetAmount"))
            writer.uint32(/* id 7, wireType 1 =*/57).double(message.defaultBetAmount);
        if (message.maxWinAmount != null && Object.hasOwnProperty.call(message, "maxWinAmount"))
            writer.uint32(/* id 8, wireType 1 =*/65).double(message.maxWinAmount);
        if (message.multiples_1 != null && message.multiples_1.length) {
            writer.uint32(/* id 9, wireType 2 =*/74).fork();
            for (let i = 0; i < message.multiples_1.length; ++i)
                writer.double(message.multiples_1[i]);
            writer.ldelim();
        }
        if (message.multiples_2 != null && message.multiples_2.length) {
            writer.uint32(/* id 10, wireType 2 =*/82).fork();
            for (let i = 0; i < message.multiples_2.length; ++i)
                writer.double(message.multiples_2[i]);
            writer.ldelim();
        }
        if (message.multiples_3 != null && message.multiples_3.length) {
            writer.uint32(/* id 11, wireType 2 =*/90).fork();
            for (let i = 0; i < message.multiples_3.length; ++i)
                writer.double(message.multiples_3[i]);
            writer.ldelim();
        }
        if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
            writer.uint32(/* id 12, wireType 2 =*/98).string(message.avatar);
        if (message.nickname != null && Object.hasOwnProperty.call(message, "nickname"))
            writer.uint32(/* id 13, wireType 2 =*/106).string(message.nickname);
        if (message.userGold != null && Object.hasOwnProperty.call(message, "userGold"))
            writer.uint32(/* id 14, wireType 1 =*/113).double(message.userGold);
        if (message.clientSeed != null && Object.hasOwnProperty.call(message, "clientSeed"))
            writer.uint32(/* id 15, wireType 2 =*/122).string(message.clientSeed);
        if (message.serverSeed != null && Object.hasOwnProperty.call(message, "serverSeed"))
            writer.uint32(/* id 16, wireType 2 =*/130).string(message.serverSeed);
        return writer;
    };

    /**
     * Encodes the specified EnterRoomResponse message, length delimited. Does not implicitly {@link EnterRoomResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof EnterRoomResponse
     * @static
     * @param {IEnterRoomResponse} message EnterRoomResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    EnterRoomResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an EnterRoomResponse message from the specified reader or buffer.
     * @function decode
     * @memberof EnterRoomResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {EnterRoomResponse} EnterRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    EnterRoomResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.EnterRoomResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    if (!(message.steps && message.steps.length))
                        message.steps = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.steps.push(reader.int32());
                    } else
                        message.steps.push(reader.int32());
                    break;
                }
            case 3: {
                    message.betAmount = reader.double();
                    break;
                }
            case 4: {
                    message.canCashOut = reader.bool();
                    break;
                }
            case 5: {
                    message.minBetAmount = reader.double();
                    break;
                }
            case 6: {
                    message.maxBetAmount = reader.double();
                    break;
                }
            case 7: {
                    message.defaultBetAmount = reader.double();
                    break;
                }
            case 8: {
                    message.maxWinAmount = reader.double();
                    break;
                }
            case 9: {
                    if (!(message.multiples_1 && message.multiples_1.length))
                        message.multiples_1 = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.multiples_1.push(reader.double());
                    } else
                        message.multiples_1.push(reader.double());
                    break;
                }
            case 10: {
                    if (!(message.multiples_2 && message.multiples_2.length))
                        message.multiples_2 = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.multiples_2.push(reader.double());
                    } else
                        message.multiples_2.push(reader.double());
                    break;
                }
            case 11: {
                    if (!(message.multiples_3 && message.multiples_3.length))
                        message.multiples_3 = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.multiples_3.push(reader.double());
                    } else
                        message.multiples_3.push(reader.double());
                    break;
                }
            case 12: {
                    message.avatar = reader.string();
                    break;
                }
            case 13: {
                    message.nickname = reader.string();
                    break;
                }
            case 14: {
                    message.userGold = reader.double();
                    break;
                }
            case 15: {
                    message.clientSeed = reader.string();
                    break;
                }
            case 16: {
                    message.serverSeed = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes an EnterRoomResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof EnterRoomResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {EnterRoomResponse} EnterRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    EnterRoomResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an EnterRoomResponse message.
     * @function verify
     * @memberof EnterRoomResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    EnterRoomResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.steps != null && message.hasOwnProperty("steps")) {
            if (!Array.isArray(message.steps))
                return "steps: array expected";
            for (let i = 0; i < message.steps.length; ++i)
                if (!$util.isInteger(message.steps[i]))
                    return "steps: integer[] expected";
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            if (typeof message.betAmount !== "number")
                return "betAmount: number expected";
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            if (typeof message.canCashOut !== "boolean")
                return "canCashOut: boolean expected";
        if (message.minBetAmount != null && message.hasOwnProperty("minBetAmount"))
            if (typeof message.minBetAmount !== "number")
                return "minBetAmount: number expected";
        if (message.maxBetAmount != null && message.hasOwnProperty("maxBetAmount"))
            if (typeof message.maxBetAmount !== "number")
                return "maxBetAmount: number expected";
        if (message.defaultBetAmount != null && message.hasOwnProperty("defaultBetAmount"))
            if (typeof message.defaultBetAmount !== "number")
                return "defaultBetAmount: number expected";
        if (message.maxWinAmount != null && message.hasOwnProperty("maxWinAmount"))
            if (typeof message.maxWinAmount !== "number")
                return "maxWinAmount: number expected";
        if (message.multiples_1 != null && message.hasOwnProperty("multiples_1")) {
            if (!Array.isArray(message.multiples_1))
                return "multiples_1: array expected";
            for (let i = 0; i < message.multiples_1.length; ++i)
                if (typeof message.multiples_1[i] !== "number")
                    return "multiples_1: number[] expected";
        }
        if (message.multiples_2 != null && message.hasOwnProperty("multiples_2")) {
            if (!Array.isArray(message.multiples_2))
                return "multiples_2: array expected";
            for (let i = 0; i < message.multiples_2.length; ++i)
                if (typeof message.multiples_2[i] !== "number")
                    return "multiples_2: number[] expected";
        }
        if (message.multiples_3 != null && message.hasOwnProperty("multiples_3")) {
            if (!Array.isArray(message.multiples_3))
                return "multiples_3: array expected";
            for (let i = 0; i < message.multiples_3.length; ++i)
                if (typeof message.multiples_3[i] !== "number")
                    return "multiples_3: number[] expected";
        }
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            if (!$util.isString(message.avatar))
                return "avatar: string expected";
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            if (!$util.isString(message.nickname))
                return "nickname: string expected";
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            if (typeof message.userGold !== "number")
                return "userGold: number expected";
        if (message.clientSeed != null && message.hasOwnProperty("clientSeed"))
            if (!$util.isString(message.clientSeed))
                return "clientSeed: string expected";
        if (message.serverSeed != null && message.hasOwnProperty("serverSeed"))
            if (!$util.isString(message.serverSeed))
                return "serverSeed: string expected";
        return null;
    };

    /**
     * Creates an EnterRoomResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof EnterRoomResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {EnterRoomResponse} EnterRoomResponse
     */
    EnterRoomResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.EnterRoomResponse)
            return object;
        let message = new $root.EnterRoomResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.steps) {
            if (!Array.isArray(object.steps))
                throw TypeError(".EnterRoomResponse.steps: array expected");
            message.steps = [];
            for (let i = 0; i < object.steps.length; ++i)
                message.steps[i] = object.steps[i] | 0;
        }
        if (object.betAmount != null)
            message.betAmount = Number(object.betAmount);
        if (object.canCashOut != null)
            message.canCashOut = Boolean(object.canCashOut);
        if (object.minBetAmount != null)
            message.minBetAmount = Number(object.minBetAmount);
        if (object.maxBetAmount != null)
            message.maxBetAmount = Number(object.maxBetAmount);
        if (object.defaultBetAmount != null)
            message.defaultBetAmount = Number(object.defaultBetAmount);
        if (object.maxWinAmount != null)
            message.maxWinAmount = Number(object.maxWinAmount);
        if (object.multiples_1) {
            if (!Array.isArray(object.multiples_1))
                throw TypeError(".EnterRoomResponse.multiples_1: array expected");
            message.multiples_1 = [];
            for (let i = 0; i < object.multiples_1.length; ++i)
                message.multiples_1[i] = Number(object.multiples_1[i]);
        }
        if (object.multiples_2) {
            if (!Array.isArray(object.multiples_2))
                throw TypeError(".EnterRoomResponse.multiples_2: array expected");
            message.multiples_2 = [];
            for (let i = 0; i < object.multiples_2.length; ++i)
                message.multiples_2[i] = Number(object.multiples_2[i]);
        }
        if (object.multiples_3) {
            if (!Array.isArray(object.multiples_3))
                throw TypeError(".EnterRoomResponse.multiples_3: array expected");
            message.multiples_3 = [];
            for (let i = 0; i < object.multiples_3.length; ++i)
                message.multiples_3[i] = Number(object.multiples_3[i]);
        }
        if (object.avatar != null)
            message.avatar = String(object.avatar);
        if (object.nickname != null)
            message.nickname = String(object.nickname);
        if (object.userGold != null)
            message.userGold = Number(object.userGold);
        if (object.clientSeed != null)
            message.clientSeed = String(object.clientSeed);
        if (object.serverSeed != null)
            message.serverSeed = String(object.serverSeed);
        return message;
    };

    /**
     * Creates a plain object from an EnterRoomResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof EnterRoomResponse
     * @static
     * @param {EnterRoomResponse} message EnterRoomResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    EnterRoomResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.arrays || options.defaults) {
            object.steps = [];
            object.multiples_1 = [];
            object.multiples_2 = [];
            object.multiples_3 = [];
        }
        if (options.defaults) {
            object.errorCode = 0;
            object.betAmount = 0;
            object.canCashOut = false;
            object.minBetAmount = 0;
            object.maxBetAmount = 0;
            object.defaultBetAmount = 0;
            object.maxWinAmount = 0;
            object.avatar = "";
            object.nickname = "";
            object.userGold = 0;
            object.clientSeed = "";
            object.serverSeed = "";
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.steps && message.steps.length) {
            object.steps = [];
            for (let j = 0; j < message.steps.length; ++j)
                object.steps[j] = message.steps[j];
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            object.betAmount = options.json && !isFinite(message.betAmount) ? String(message.betAmount) : message.betAmount;
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            object.canCashOut = message.canCashOut;
        if (message.minBetAmount != null && message.hasOwnProperty("minBetAmount"))
            object.minBetAmount = options.json && !isFinite(message.minBetAmount) ? String(message.minBetAmount) : message.minBetAmount;
        if (message.maxBetAmount != null && message.hasOwnProperty("maxBetAmount"))
            object.maxBetAmount = options.json && !isFinite(message.maxBetAmount) ? String(message.maxBetAmount) : message.maxBetAmount;
        if (message.defaultBetAmount != null && message.hasOwnProperty("defaultBetAmount"))
            object.defaultBetAmount = options.json && !isFinite(message.defaultBetAmount) ? String(message.defaultBetAmount) : message.defaultBetAmount;
        if (message.maxWinAmount != null && message.hasOwnProperty("maxWinAmount"))
            object.maxWinAmount = options.json && !isFinite(message.maxWinAmount) ? String(message.maxWinAmount) : message.maxWinAmount;
        if (message.multiples_1 && message.multiples_1.length) {
            object.multiples_1 = [];
            for (let j = 0; j < message.multiples_1.length; ++j)
                object.multiples_1[j] = options.json && !isFinite(message.multiples_1[j]) ? String(message.multiples_1[j]) : message.multiples_1[j];
        }
        if (message.multiples_2 && message.multiples_2.length) {
            object.multiples_2 = [];
            for (let j = 0; j < message.multiples_2.length; ++j)
                object.multiples_2[j] = options.json && !isFinite(message.multiples_2[j]) ? String(message.multiples_2[j]) : message.multiples_2[j];
        }
        if (message.multiples_3 && message.multiples_3.length) {
            object.multiples_3 = [];
            for (let j = 0; j < message.multiples_3.length; ++j)
                object.multiples_3[j] = options.json && !isFinite(message.multiples_3[j]) ? String(message.multiples_3[j]) : message.multiples_3[j];
        }
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            object.avatar = message.avatar;
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            object.nickname = message.nickname;
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            object.userGold = options.json && !isFinite(message.userGold) ? String(message.userGold) : message.userGold;
        if (message.clientSeed != null && message.hasOwnProperty("clientSeed"))
            object.clientSeed = message.clientSeed;
        if (message.serverSeed != null && message.hasOwnProperty("serverSeed"))
            object.serverSeed = message.serverSeed;
        return object;
    };

    /**
     * Converts this EnterRoomResponse to JSON.
     * @function toJSON
     * @memberof EnterRoomResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    EnterRoomResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for EnterRoomResponse
     * @function getTypeUrl
     * @memberof EnterRoomResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    EnterRoomResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/EnterRoomResponse";
    };

    return EnterRoomResponse;
})();

export const ExitRoomRequest = $root.ExitRoomRequest = (() => {

    /**
     * Properties of an ExitRoomRequest.
     * @exports IExitRoomRequest
     * @interface IExitRoomRequest
     */

    /**
     * Constructs a new ExitRoomRequest.
     * @exports ExitRoomRequest
     * @classdesc Represents an ExitRoomRequest.
     * @implements IExitRoomRequest
     * @constructor
     * @param {IExitRoomRequest=} [properties] Properties to set
     */
    function ExitRoomRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * Creates a new ExitRoomRequest instance using the specified properties.
     * @function create
     * @memberof ExitRoomRequest
     * @static
     * @param {IExitRoomRequest=} [properties] Properties to set
     * @returns {ExitRoomRequest} ExitRoomRequest instance
     */
    ExitRoomRequest.create = function create(properties) {
        return new ExitRoomRequest(properties);
    };

    /**
     * Encodes the specified ExitRoomRequest message. Does not implicitly {@link ExitRoomRequest.verify|verify} messages.
     * @function encode
     * @memberof ExitRoomRequest
     * @static
     * @param {IExitRoomRequest} message ExitRoomRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExitRoomRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        return writer;
    };

    /**
     * Encodes the specified ExitRoomRequest message, length delimited. Does not implicitly {@link ExitRoomRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ExitRoomRequest
     * @static
     * @param {IExitRoomRequest} message ExitRoomRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExitRoomRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an ExitRoomRequest message from the specified reader or buffer.
     * @function decode
     * @memberof ExitRoomRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ExitRoomRequest} ExitRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExitRoomRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ExitRoomRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes an ExitRoomRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ExitRoomRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ExitRoomRequest} ExitRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExitRoomRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an ExitRoomRequest message.
     * @function verify
     * @memberof ExitRoomRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ExitRoomRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        return null;
    };

    /**
     * Creates an ExitRoomRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ExitRoomRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ExitRoomRequest} ExitRoomRequest
     */
    ExitRoomRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.ExitRoomRequest)
            return object;
        return new $root.ExitRoomRequest();
    };

    /**
     * Creates a plain object from an ExitRoomRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ExitRoomRequest
     * @static
     * @param {ExitRoomRequest} message ExitRoomRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ExitRoomRequest.toObject = function toObject() {
        return {};
    };

    /**
     * Converts this ExitRoomRequest to JSON.
     * @function toJSON
     * @memberof ExitRoomRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ExitRoomRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ExitRoomRequest
     * @function getTypeUrl
     * @memberof ExitRoomRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ExitRoomRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ExitRoomRequest";
    };

    return ExitRoomRequest;
})();

export const ExitRoomResponse = $root.ExitRoomResponse = (() => {

    /**
     * Properties of an ExitRoomResponse.
     * @exports IExitRoomResponse
     * @interface IExitRoomResponse
     * @property {number|null} [errorCode] ExitRoomResponse errorCode
     * @property {number|null} [userGold] ExitRoomResponse userGold
     */

    /**
     * Constructs a new ExitRoomResponse.
     * @exports ExitRoomResponse
     * @classdesc Represents an ExitRoomResponse.
     * @implements IExitRoomResponse
     * @constructor
     * @param {IExitRoomResponse=} [properties] Properties to set
     */
    function ExitRoomResponse(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ExitRoomResponse errorCode.
     * @member {number} errorCode
     * @memberof ExitRoomResponse
     * @instance
     */
    ExitRoomResponse.prototype.errorCode = 0;

    /**
     * ExitRoomResponse userGold.
     * @member {number} userGold
     * @memberof ExitRoomResponse
     * @instance
     */
    ExitRoomResponse.prototype.userGold = 0;

    /**
     * Creates a new ExitRoomResponse instance using the specified properties.
     * @function create
     * @memberof ExitRoomResponse
     * @static
     * @param {IExitRoomResponse=} [properties] Properties to set
     * @returns {ExitRoomResponse} ExitRoomResponse instance
     */
    ExitRoomResponse.create = function create(properties) {
        return new ExitRoomResponse(properties);
    };

    /**
     * Encodes the specified ExitRoomResponse message. Does not implicitly {@link ExitRoomResponse.verify|verify} messages.
     * @function encode
     * @memberof ExitRoomResponse
     * @static
     * @param {IExitRoomResponse} message ExitRoomResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExitRoomResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.userGold != null && Object.hasOwnProperty.call(message, "userGold"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.userGold);
        return writer;
    };

    /**
     * Encodes the specified ExitRoomResponse message, length delimited. Does not implicitly {@link ExitRoomResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ExitRoomResponse
     * @static
     * @param {IExitRoomResponse} message ExitRoomResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ExitRoomResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an ExitRoomResponse message from the specified reader or buffer.
     * @function decode
     * @memberof ExitRoomResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ExitRoomResponse} ExitRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExitRoomResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ExitRoomResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 3: {
                    message.userGold = reader.double();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes an ExitRoomResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ExitRoomResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ExitRoomResponse} ExitRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ExitRoomResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an ExitRoomResponse message.
     * @function verify
     * @memberof ExitRoomResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ExitRoomResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            if (typeof message.userGold !== "number")
                return "userGold: number expected";
        return null;
    };

    /**
     * Creates an ExitRoomResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ExitRoomResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ExitRoomResponse} ExitRoomResponse
     */
    ExitRoomResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.ExitRoomResponse)
            return object;
        let message = new $root.ExitRoomResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.userGold != null)
            message.userGold = Number(object.userGold);
        return message;
    };

    /**
     * Creates a plain object from an ExitRoomResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ExitRoomResponse
     * @static
     * @param {ExitRoomResponse} message ExitRoomResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ExitRoomResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.errorCode = 0;
            object.userGold = 0;
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            object.userGold = options.json && !isFinite(message.userGold) ? String(message.userGold) : message.userGold;
        return object;
    };

    /**
     * Converts this ExitRoomResponse to JSON.
     * @function toJSON
     * @memberof ExitRoomResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ExitRoomResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ExitRoomResponse
     * @function getTypeUrl
     * @memberof ExitRoomResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ExitRoomResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ExitRoomResponse";
    };

    return ExitRoomResponse;
})();

export const StartGameRequest = $root.StartGameRequest = (() => {

    /**
     * Properties of a StartGameRequest.
     * @exports IStartGameRequest
     * @interface IStartGameRequest
     * @property {number|null} [betAmount] StartGameRequest betAmount
     */

    /**
     * Constructs a new StartGameRequest.
     * @exports StartGameRequest
     * @classdesc Represents a StartGameRequest.
     * @implements IStartGameRequest
     * @constructor
     * @param {IStartGameRequest=} [properties] Properties to set
     */
    function StartGameRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * StartGameRequest betAmount.
     * @member {number} betAmount
     * @memberof StartGameRequest
     * @instance
     */
    StartGameRequest.prototype.betAmount = 0;

    /**
     * Creates a new StartGameRequest instance using the specified properties.
     * @function create
     * @memberof StartGameRequest
     * @static
     * @param {IStartGameRequest=} [properties] Properties to set
     * @returns {StartGameRequest} StartGameRequest instance
     */
    StartGameRequest.create = function create(properties) {
        return new StartGameRequest(properties);
    };

    /**
     * Encodes the specified StartGameRequest message. Does not implicitly {@link StartGameRequest.verify|verify} messages.
     * @function encode
     * @memberof StartGameRequest
     * @static
     * @param {IStartGameRequest} message StartGameRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    StartGameRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.betAmount != null && Object.hasOwnProperty.call(message, "betAmount"))
            writer.uint32(/* id 1, wireType 1 =*/9).double(message.betAmount);
        return writer;
    };

    /**
     * Encodes the specified StartGameRequest message, length delimited. Does not implicitly {@link StartGameRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof StartGameRequest
     * @static
     * @param {IStartGameRequest} message StartGameRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    StartGameRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a StartGameRequest message from the specified reader or buffer.
     * @function decode
     * @memberof StartGameRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {StartGameRequest} StartGameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    StartGameRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.StartGameRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.betAmount = reader.double();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a StartGameRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof StartGameRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {StartGameRequest} StartGameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    StartGameRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a StartGameRequest message.
     * @function verify
     * @memberof StartGameRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    StartGameRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            if (typeof message.betAmount !== "number")
                return "betAmount: number expected";
        return null;
    };

    /**
     * Creates a StartGameRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof StartGameRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {StartGameRequest} StartGameRequest
     */
    StartGameRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.StartGameRequest)
            return object;
        let message = new $root.StartGameRequest();
        if (object.betAmount != null)
            message.betAmount = Number(object.betAmount);
        return message;
    };

    /**
     * Creates a plain object from a StartGameRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof StartGameRequest
     * @static
     * @param {StartGameRequest} message StartGameRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    StartGameRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.betAmount = 0;
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            object.betAmount = options.json && !isFinite(message.betAmount) ? String(message.betAmount) : message.betAmount;
        return object;
    };

    /**
     * Converts this StartGameRequest to JSON.
     * @function toJSON
     * @memberof StartGameRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    StartGameRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for StartGameRequest
     * @function getTypeUrl
     * @memberof StartGameRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    StartGameRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/StartGameRequest";
    };

    return StartGameRequest;
})();

export const StartGameResponse = $root.StartGameResponse = (() => {

    /**
     * Properties of a StartGameResponse.
     * @exports IStartGameResponse
     * @interface IStartGameResponse
     * @property {number|null} [errorCode] StartGameResponse errorCode
     * @property {number|null} [element] StartGameResponse element
     * @property {Array.<number>|null} [steps] StartGameResponse steps
     * @property {number|null} [betAmount] StartGameResponse betAmount
     * @property {number|null} [mul] StartGameResponse mul
     * @property {number|null} [winAmount] StartGameResponse winAmount
     * @property {boolean|null} [canCashOut] StartGameResponse canCashOut
     * @property {number|null} [userGold] StartGameResponse userGold
     */

    /**
     * Constructs a new StartGameResponse.
     * @exports StartGameResponse
     * @classdesc Represents a StartGameResponse.
     * @implements IStartGameResponse
     * @constructor
     * @param {IStartGameResponse=} [properties] Properties to set
     */
    function StartGameResponse(properties) {
        this.steps = [];
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * StartGameResponse errorCode.
     * @member {number} errorCode
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.errorCode = 0;

    /**
     * StartGameResponse element.
     * @member {number} element
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.element = 0;

    /**
     * StartGameResponse steps.
     * @member {Array.<number>} steps
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.steps = $util.emptyArray;

    /**
     * StartGameResponse betAmount.
     * @member {number} betAmount
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.betAmount = 0;

    /**
     * StartGameResponse mul.
     * @member {number} mul
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.mul = 0;

    /**
     * StartGameResponse winAmount.
     * @member {number} winAmount
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.winAmount = 0;

    /**
     * StartGameResponse canCashOut.
     * @member {boolean} canCashOut
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.canCashOut = false;

    /**
     * StartGameResponse userGold.
     * @member {number} userGold
     * @memberof StartGameResponse
     * @instance
     */
    StartGameResponse.prototype.userGold = 0;

    /**
     * Creates a new StartGameResponse instance using the specified properties.
     * @function create
     * @memberof StartGameResponse
     * @static
     * @param {IStartGameResponse=} [properties] Properties to set
     * @returns {StartGameResponse} StartGameResponse instance
     */
    StartGameResponse.create = function create(properties) {
        return new StartGameResponse(properties);
    };

    /**
     * Encodes the specified StartGameResponse message. Does not implicitly {@link StartGameResponse.verify|verify} messages.
     * @function encode
     * @memberof StartGameResponse
     * @static
     * @param {IStartGameResponse} message StartGameResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    StartGameResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.element != null && Object.hasOwnProperty.call(message, "element"))
            writer.uint32(/* id 2, wireType 0 =*/16).int32(message.element);
        if (message.steps != null && message.steps.length) {
            writer.uint32(/* id 3, wireType 2 =*/26).fork();
            for (let i = 0; i < message.steps.length; ++i)
                writer.int32(message.steps[i]);
            writer.ldelim();
        }
        if (message.betAmount != null && Object.hasOwnProperty.call(message, "betAmount"))
            writer.uint32(/* id 4, wireType 1 =*/33).double(message.betAmount);
        if (message.mul != null && Object.hasOwnProperty.call(message, "mul"))
            writer.uint32(/* id 5, wireType 1 =*/41).double(message.mul);
        if (message.winAmount != null && Object.hasOwnProperty.call(message, "winAmount"))
            writer.uint32(/* id 6, wireType 1 =*/49).double(message.winAmount);
        if (message.canCashOut != null && Object.hasOwnProperty.call(message, "canCashOut"))
            writer.uint32(/* id 7, wireType 0 =*/56).bool(message.canCashOut);
        if (message.userGold != null && Object.hasOwnProperty.call(message, "userGold"))
            writer.uint32(/* id 8, wireType 1 =*/65).double(message.userGold);
        return writer;
    };

    /**
     * Encodes the specified StartGameResponse message, length delimited. Does not implicitly {@link StartGameResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof StartGameResponse
     * @static
     * @param {IStartGameResponse} message StartGameResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    StartGameResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a StartGameResponse message from the specified reader or buffer.
     * @function decode
     * @memberof StartGameResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {StartGameResponse} StartGameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    StartGameResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.StartGameResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    message.element = reader.int32();
                    break;
                }
            case 3: {
                    if (!(message.steps && message.steps.length))
                        message.steps = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.steps.push(reader.int32());
                    } else
                        message.steps.push(reader.int32());
                    break;
                }
            case 4: {
                    message.betAmount = reader.double();
                    break;
                }
            case 5: {
                    message.mul = reader.double();
                    break;
                }
            case 6: {
                    message.winAmount = reader.double();
                    break;
                }
            case 7: {
                    message.canCashOut = reader.bool();
                    break;
                }
            case 8: {
                    message.userGold = reader.double();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a StartGameResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof StartGameResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {StartGameResponse} StartGameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    StartGameResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a StartGameResponse message.
     * @function verify
     * @memberof StartGameResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    StartGameResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.element != null && message.hasOwnProperty("element"))
            if (!$util.isInteger(message.element))
                return "element: integer expected";
        if (message.steps != null && message.hasOwnProperty("steps")) {
            if (!Array.isArray(message.steps))
                return "steps: array expected";
            for (let i = 0; i < message.steps.length; ++i)
                if (!$util.isInteger(message.steps[i]))
                    return "steps: integer[] expected";
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            if (typeof message.betAmount !== "number")
                return "betAmount: number expected";
        if (message.mul != null && message.hasOwnProperty("mul"))
            if (typeof message.mul !== "number")
                return "mul: number expected";
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            if (typeof message.winAmount !== "number")
                return "winAmount: number expected";
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            if (typeof message.canCashOut !== "boolean")
                return "canCashOut: boolean expected";
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            if (typeof message.userGold !== "number")
                return "userGold: number expected";
        return null;
    };

    /**
     * Creates a StartGameResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof StartGameResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {StartGameResponse} StartGameResponse
     */
    StartGameResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.StartGameResponse)
            return object;
        let message = new $root.StartGameResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.element != null)
            message.element = object.element | 0;
        if (object.steps) {
            if (!Array.isArray(object.steps))
                throw TypeError(".StartGameResponse.steps: array expected");
            message.steps = [];
            for (let i = 0; i < object.steps.length; ++i)
                message.steps[i] = object.steps[i] | 0;
        }
        if (object.betAmount != null)
            message.betAmount = Number(object.betAmount);
        if (object.mul != null)
            message.mul = Number(object.mul);
        if (object.winAmount != null)
            message.winAmount = Number(object.winAmount);
        if (object.canCashOut != null)
            message.canCashOut = Boolean(object.canCashOut);
        if (object.userGold != null)
            message.userGold = Number(object.userGold);
        return message;
    };

    /**
     * Creates a plain object from a StartGameResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof StartGameResponse
     * @static
     * @param {StartGameResponse} message StartGameResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    StartGameResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.arrays || options.defaults)
            object.steps = [];
        if (options.defaults) {
            object.errorCode = 0;
            object.element = 0;
            object.betAmount = 0;
            object.mul = 0;
            object.winAmount = 0;
            object.canCashOut = false;
            object.userGold = 0;
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.element != null && message.hasOwnProperty("element"))
            object.element = message.element;
        if (message.steps && message.steps.length) {
            object.steps = [];
            for (let j = 0; j < message.steps.length; ++j)
                object.steps[j] = message.steps[j];
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            object.betAmount = options.json && !isFinite(message.betAmount) ? String(message.betAmount) : message.betAmount;
        if (message.mul != null && message.hasOwnProperty("mul"))
            object.mul = options.json && !isFinite(message.mul) ? String(message.mul) : message.mul;
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            object.winAmount = options.json && !isFinite(message.winAmount) ? String(message.winAmount) : message.winAmount;
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            object.canCashOut = message.canCashOut;
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            object.userGold = options.json && !isFinite(message.userGold) ? String(message.userGold) : message.userGold;
        return object;
    };

    /**
     * Converts this StartGameResponse to JSON.
     * @function toJSON
     * @memberof StartGameResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    StartGameResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for StartGameResponse
     * @function getTypeUrl
     * @memberof StartGameResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    StartGameResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/StartGameResponse";
    };

    return StartGameResponse;
})();

export const CashOutRequest = $root.CashOutRequest = (() => {

    /**
     * Properties of a CashOutRequest.
     * @exports ICashOutRequest
     * @interface ICashOutRequest
     */

    /**
     * Constructs a new CashOutRequest.
     * @exports CashOutRequest
     * @classdesc Represents a CashOutRequest.
     * @implements ICashOutRequest
     * @constructor
     * @param {ICashOutRequest=} [properties] Properties to set
     */
    function CashOutRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * Creates a new CashOutRequest instance using the specified properties.
     * @function create
     * @memberof CashOutRequest
     * @static
     * @param {ICashOutRequest=} [properties] Properties to set
     * @returns {CashOutRequest} CashOutRequest instance
     */
    CashOutRequest.create = function create(properties) {
        return new CashOutRequest(properties);
    };

    /**
     * Encodes the specified CashOutRequest message. Does not implicitly {@link CashOutRequest.verify|verify} messages.
     * @function encode
     * @memberof CashOutRequest
     * @static
     * @param {ICashOutRequest} message CashOutRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CashOutRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        return writer;
    };

    /**
     * Encodes the specified CashOutRequest message, length delimited. Does not implicitly {@link CashOutRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof CashOutRequest
     * @static
     * @param {ICashOutRequest} message CashOutRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CashOutRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a CashOutRequest message from the specified reader or buffer.
     * @function decode
     * @memberof CashOutRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {CashOutRequest} CashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CashOutRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.CashOutRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a CashOutRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof CashOutRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {CashOutRequest} CashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CashOutRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a CashOutRequest message.
     * @function verify
     * @memberof CashOutRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    CashOutRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        return null;
    };

    /**
     * Creates a CashOutRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof CashOutRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {CashOutRequest} CashOutRequest
     */
    CashOutRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.CashOutRequest)
            return object;
        return new $root.CashOutRequest();
    };

    /**
     * Creates a plain object from a CashOutRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof CashOutRequest
     * @static
     * @param {CashOutRequest} message CashOutRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    CashOutRequest.toObject = function toObject() {
        return {};
    };

    /**
     * Converts this CashOutRequest to JSON.
     * @function toJSON
     * @memberof CashOutRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    CashOutRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for CashOutRequest
     * @function getTypeUrl
     * @memberof CashOutRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    CashOutRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/CashOutRequest";
    };

    return CashOutRequest;
})();

export const CashOutResponse = $root.CashOutResponse = (() => {

    /**
     * Properties of a CashOutResponse.
     * @exports ICashOutResponse
     * @interface ICashOutResponse
     * @property {number|null} [errorCode] CashOutResponse errorCode
     * @property {Array.<number>|null} [steps] CashOutResponse steps
     * @property {number|null} [winAmount] CashOutResponse winAmount
     * @property {boolean|null} [canCashOut] CashOutResponse canCashOut
     * @property {number|null} [userGold] CashOutResponse userGold
     */

    /**
     * Constructs a new CashOutResponse.
     * @exports CashOutResponse
     * @classdesc Represents a CashOutResponse.
     * @implements ICashOutResponse
     * @constructor
     * @param {ICashOutResponse=} [properties] Properties to set
     */
    function CashOutResponse(properties) {
        this.steps = [];
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * CashOutResponse errorCode.
     * @member {number} errorCode
     * @memberof CashOutResponse
     * @instance
     */
    CashOutResponse.prototype.errorCode = 0;

    /**
     * CashOutResponse steps.
     * @member {Array.<number>} steps
     * @memberof CashOutResponse
     * @instance
     */
    CashOutResponse.prototype.steps = $util.emptyArray;

    /**
     * CashOutResponse winAmount.
     * @member {number} winAmount
     * @memberof CashOutResponse
     * @instance
     */
    CashOutResponse.prototype.winAmount = 0;

    /**
     * CashOutResponse canCashOut.
     * @member {boolean} canCashOut
     * @memberof CashOutResponse
     * @instance
     */
    CashOutResponse.prototype.canCashOut = false;

    /**
     * CashOutResponse userGold.
     * @member {number} userGold
     * @memberof CashOutResponse
     * @instance
     */
    CashOutResponse.prototype.userGold = 0;

    /**
     * Creates a new CashOutResponse instance using the specified properties.
     * @function create
     * @memberof CashOutResponse
     * @static
     * @param {ICashOutResponse=} [properties] Properties to set
     * @returns {CashOutResponse} CashOutResponse instance
     */
    CashOutResponse.create = function create(properties) {
        return new CashOutResponse(properties);
    };

    /**
     * Encodes the specified CashOutResponse message. Does not implicitly {@link CashOutResponse.verify|verify} messages.
     * @function encode
     * @memberof CashOutResponse
     * @static
     * @param {ICashOutResponse} message CashOutResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CashOutResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.steps != null && message.steps.length) {
            writer.uint32(/* id 2, wireType 2 =*/18).fork();
            for (let i = 0; i < message.steps.length; ++i)
                writer.int32(message.steps[i]);
            writer.ldelim();
        }
        if (message.winAmount != null && Object.hasOwnProperty.call(message, "winAmount"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.winAmount);
        if (message.canCashOut != null && Object.hasOwnProperty.call(message, "canCashOut"))
            writer.uint32(/* id 4, wireType 0 =*/32).bool(message.canCashOut);
        if (message.userGold != null && Object.hasOwnProperty.call(message, "userGold"))
            writer.uint32(/* id 5, wireType 1 =*/41).double(message.userGold);
        return writer;
    };

    /**
     * Encodes the specified CashOutResponse message, length delimited. Does not implicitly {@link CashOutResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof CashOutResponse
     * @static
     * @param {ICashOutResponse} message CashOutResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CashOutResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a CashOutResponse message from the specified reader or buffer.
     * @function decode
     * @memberof CashOutResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {CashOutResponse} CashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CashOutResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.CashOutResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    if (!(message.steps && message.steps.length))
                        message.steps = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.steps.push(reader.int32());
                    } else
                        message.steps.push(reader.int32());
                    break;
                }
            case 3: {
                    message.winAmount = reader.double();
                    break;
                }
            case 4: {
                    message.canCashOut = reader.bool();
                    break;
                }
            case 5: {
                    message.userGold = reader.double();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a CashOutResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof CashOutResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {CashOutResponse} CashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CashOutResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a CashOutResponse message.
     * @function verify
     * @memberof CashOutResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    CashOutResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.steps != null && message.hasOwnProperty("steps")) {
            if (!Array.isArray(message.steps))
                return "steps: array expected";
            for (let i = 0; i < message.steps.length; ++i)
                if (!$util.isInteger(message.steps[i]))
                    return "steps: integer[] expected";
        }
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            if (typeof message.winAmount !== "number")
                return "winAmount: number expected";
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            if (typeof message.canCashOut !== "boolean")
                return "canCashOut: boolean expected";
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            if (typeof message.userGold !== "number")
                return "userGold: number expected";
        return null;
    };

    /**
     * Creates a CashOutResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof CashOutResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {CashOutResponse} CashOutResponse
     */
    CashOutResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.CashOutResponse)
            return object;
        let message = new $root.CashOutResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.steps) {
            if (!Array.isArray(object.steps))
                throw TypeError(".CashOutResponse.steps: array expected");
            message.steps = [];
            for (let i = 0; i < object.steps.length; ++i)
                message.steps[i] = object.steps[i] | 0;
        }
        if (object.winAmount != null)
            message.winAmount = Number(object.winAmount);
        if (object.canCashOut != null)
            message.canCashOut = Boolean(object.canCashOut);
        if (object.userGold != null)
            message.userGold = Number(object.userGold);
        return message;
    };

    /**
     * Creates a plain object from a CashOutResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof CashOutResponse
     * @static
     * @param {CashOutResponse} message CashOutResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    CashOutResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.arrays || options.defaults)
            object.steps = [];
        if (options.defaults) {
            object.errorCode = 0;
            object.winAmount = 0;
            object.canCashOut = false;
            object.userGold = 0;
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.steps && message.steps.length) {
            object.steps = [];
            for (let j = 0; j < message.steps.length; ++j)
                object.steps[j] = message.steps[j];
        }
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            object.winAmount = options.json && !isFinite(message.winAmount) ? String(message.winAmount) : message.winAmount;
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            object.canCashOut = message.canCashOut;
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            object.userGold = options.json && !isFinite(message.userGold) ? String(message.userGold) : message.userGold;
        return object;
    };

    /**
     * Converts this CashOutResponse to JSON.
     * @function toJSON
     * @memberof CashOutResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    CashOutResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for CashOutResponse
     * @function getTypeUrl
     * @memberof CashOutResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    CashOutResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/CashOutResponse";
    };

    return CashOutResponse;
})();

export const PartCashOutRequest = $root.PartCashOutRequest = (() => {

    /**
     * Properties of a PartCashOutRequest.
     * @exports IPartCashOutRequest
     * @interface IPartCashOutRequest
     */

    /**
     * Constructs a new PartCashOutRequest.
     * @exports PartCashOutRequest
     * @classdesc Represents a PartCashOutRequest.
     * @implements IPartCashOutRequest
     * @constructor
     * @param {IPartCashOutRequest=} [properties] Properties to set
     */
    function PartCashOutRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * Creates a new PartCashOutRequest instance using the specified properties.
     * @function create
     * @memberof PartCashOutRequest
     * @static
     * @param {IPartCashOutRequest=} [properties] Properties to set
     * @returns {PartCashOutRequest} PartCashOutRequest instance
     */
    PartCashOutRequest.create = function create(properties) {
        return new PartCashOutRequest(properties);
    };

    /**
     * Encodes the specified PartCashOutRequest message. Does not implicitly {@link PartCashOutRequest.verify|verify} messages.
     * @function encode
     * @memberof PartCashOutRequest
     * @static
     * @param {IPartCashOutRequest} message PartCashOutRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PartCashOutRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        return writer;
    };

    /**
     * Encodes the specified PartCashOutRequest message, length delimited. Does not implicitly {@link PartCashOutRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof PartCashOutRequest
     * @static
     * @param {IPartCashOutRequest} message PartCashOutRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PartCashOutRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a PartCashOutRequest message from the specified reader or buffer.
     * @function decode
     * @memberof PartCashOutRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {PartCashOutRequest} PartCashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PartCashOutRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.PartCashOutRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a PartCashOutRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof PartCashOutRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {PartCashOutRequest} PartCashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PartCashOutRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a PartCashOutRequest message.
     * @function verify
     * @memberof PartCashOutRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    PartCashOutRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        return null;
    };

    /**
     * Creates a PartCashOutRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof PartCashOutRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {PartCashOutRequest} PartCashOutRequest
     */
    PartCashOutRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.PartCashOutRequest)
            return object;
        return new $root.PartCashOutRequest();
    };

    /**
     * Creates a plain object from a PartCashOutRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof PartCashOutRequest
     * @static
     * @param {PartCashOutRequest} message PartCashOutRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    PartCashOutRequest.toObject = function toObject() {
        return {};
    };

    /**
     * Converts this PartCashOutRequest to JSON.
     * @function toJSON
     * @memberof PartCashOutRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    PartCashOutRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for PartCashOutRequest
     * @function getTypeUrl
     * @memberof PartCashOutRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    PartCashOutRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PartCashOutRequest";
    };

    return PartCashOutRequest;
})();

export const PartCashOutResponse = $root.PartCashOutResponse = (() => {

    /**
     * Properties of a PartCashOutResponse.
     * @exports IPartCashOutResponse
     * @interface IPartCashOutResponse
     * @property {number|null} [errorCode] PartCashOutResponse errorCode
     * @property {Array.<number>|null} [steps] PartCashOutResponse steps
     * @property {number|null} [betAmount] PartCashOutResponse betAmount
     * @property {number|null} [winAmount] PartCashOutResponse winAmount
     * @property {boolean|null} [canCashOut] PartCashOutResponse canCashOut
     * @property {number|null} [userGold] PartCashOutResponse userGold
     */

    /**
     * Constructs a new PartCashOutResponse.
     * @exports PartCashOutResponse
     * @classdesc Represents a PartCashOutResponse.
     * @implements IPartCashOutResponse
     * @constructor
     * @param {IPartCashOutResponse=} [properties] Properties to set
     */
    function PartCashOutResponse(properties) {
        this.steps = [];
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * PartCashOutResponse errorCode.
     * @member {number} errorCode
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.errorCode = 0;

    /**
     * PartCashOutResponse steps.
     * @member {Array.<number>} steps
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.steps = $util.emptyArray;

    /**
     * PartCashOutResponse betAmount.
     * @member {number} betAmount
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.betAmount = 0;

    /**
     * PartCashOutResponse winAmount.
     * @member {number} winAmount
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.winAmount = 0;

    /**
     * PartCashOutResponse canCashOut.
     * @member {boolean} canCashOut
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.canCashOut = false;

    /**
     * PartCashOutResponse userGold.
     * @member {number} userGold
     * @memberof PartCashOutResponse
     * @instance
     */
    PartCashOutResponse.prototype.userGold = 0;

    /**
     * Creates a new PartCashOutResponse instance using the specified properties.
     * @function create
     * @memberof PartCashOutResponse
     * @static
     * @param {IPartCashOutResponse=} [properties] Properties to set
     * @returns {PartCashOutResponse} PartCashOutResponse instance
     */
    PartCashOutResponse.create = function create(properties) {
        return new PartCashOutResponse(properties);
    };

    /**
     * Encodes the specified PartCashOutResponse message. Does not implicitly {@link PartCashOutResponse.verify|verify} messages.
     * @function encode
     * @memberof PartCashOutResponse
     * @static
     * @param {IPartCashOutResponse} message PartCashOutResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PartCashOutResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.steps != null && message.steps.length) {
            writer.uint32(/* id 2, wireType 2 =*/18).fork();
            for (let i = 0; i < message.steps.length; ++i)
                writer.int32(message.steps[i]);
            writer.ldelim();
        }
        if (message.betAmount != null && Object.hasOwnProperty.call(message, "betAmount"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.betAmount);
        if (message.winAmount != null && Object.hasOwnProperty.call(message, "winAmount"))
            writer.uint32(/* id 4, wireType 1 =*/33).double(message.winAmount);
        if (message.canCashOut != null && Object.hasOwnProperty.call(message, "canCashOut"))
            writer.uint32(/* id 5, wireType 0 =*/40).bool(message.canCashOut);
        if (message.userGold != null && Object.hasOwnProperty.call(message, "userGold"))
            writer.uint32(/* id 6, wireType 1 =*/49).double(message.userGold);
        return writer;
    };

    /**
     * Encodes the specified PartCashOutResponse message, length delimited. Does not implicitly {@link PartCashOutResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof PartCashOutResponse
     * @static
     * @param {IPartCashOutResponse} message PartCashOutResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PartCashOutResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a PartCashOutResponse message from the specified reader or buffer.
     * @function decode
     * @memberof PartCashOutResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {PartCashOutResponse} PartCashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PartCashOutResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.PartCashOutResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    if (!(message.steps && message.steps.length))
                        message.steps = [];
                    if ((tag & 7) === 2) {
                        let end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2)
                            message.steps.push(reader.int32());
                    } else
                        message.steps.push(reader.int32());
                    break;
                }
            case 3: {
                    message.betAmount = reader.double();
                    break;
                }
            case 4: {
                    message.winAmount = reader.double();
                    break;
                }
            case 5: {
                    message.canCashOut = reader.bool();
                    break;
                }
            case 6: {
                    message.userGold = reader.double();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a PartCashOutResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof PartCashOutResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {PartCashOutResponse} PartCashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PartCashOutResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a PartCashOutResponse message.
     * @function verify
     * @memberof PartCashOutResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    PartCashOutResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.steps != null && message.hasOwnProperty("steps")) {
            if (!Array.isArray(message.steps))
                return "steps: array expected";
            for (let i = 0; i < message.steps.length; ++i)
                if (!$util.isInteger(message.steps[i]))
                    return "steps: integer[] expected";
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            if (typeof message.betAmount !== "number")
                return "betAmount: number expected";
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            if (typeof message.winAmount !== "number")
                return "winAmount: number expected";
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            if (typeof message.canCashOut !== "boolean")
                return "canCashOut: boolean expected";
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            if (typeof message.userGold !== "number")
                return "userGold: number expected";
        return null;
    };

    /**
     * Creates a PartCashOutResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof PartCashOutResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {PartCashOutResponse} PartCashOutResponse
     */
    PartCashOutResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.PartCashOutResponse)
            return object;
        let message = new $root.PartCashOutResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.steps) {
            if (!Array.isArray(object.steps))
                throw TypeError(".PartCashOutResponse.steps: array expected");
            message.steps = [];
            for (let i = 0; i < object.steps.length; ++i)
                message.steps[i] = object.steps[i] | 0;
        }
        if (object.betAmount != null)
            message.betAmount = Number(object.betAmount);
        if (object.winAmount != null)
            message.winAmount = Number(object.winAmount);
        if (object.canCashOut != null)
            message.canCashOut = Boolean(object.canCashOut);
        if (object.userGold != null)
            message.userGold = Number(object.userGold);
        return message;
    };

    /**
     * Creates a plain object from a PartCashOutResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof PartCashOutResponse
     * @static
     * @param {PartCashOutResponse} message PartCashOutResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    PartCashOutResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.arrays || options.defaults)
            object.steps = [];
        if (options.defaults) {
            object.errorCode = 0;
            object.betAmount = 0;
            object.winAmount = 0;
            object.canCashOut = false;
            object.userGold = 0;
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.steps && message.steps.length) {
            object.steps = [];
            for (let j = 0; j < message.steps.length; ++j)
                object.steps[j] = message.steps[j];
        }
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            object.betAmount = options.json && !isFinite(message.betAmount) ? String(message.betAmount) : message.betAmount;
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            object.winAmount = options.json && !isFinite(message.winAmount) ? String(message.winAmount) : message.winAmount;
        if (message.canCashOut != null && message.hasOwnProperty("canCashOut"))
            object.canCashOut = message.canCashOut;
        if (message.userGold != null && message.hasOwnProperty("userGold"))
            object.userGold = options.json && !isFinite(message.userGold) ? String(message.userGold) : message.userGold;
        return object;
    };

    /**
     * Converts this PartCashOutResponse to JSON.
     * @function toJSON
     * @memberof PartCashOutResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    PartCashOutResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for PartCashOutResponse
     * @function getTypeUrl
     * @memberof PartCashOutResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    PartCashOutResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PartCashOutResponse";
    };

    return PartCashOutResponse;
})();

export const GetHistoryRequest = $root.GetHistoryRequest = (() => {

    /**
     * Properties of a GetHistoryRequest.
     * @exports IGetHistoryRequest
     * @interface IGetHistoryRequest
     * @property {number|null} [page] GetHistoryRequest page
     * @property {number|null} [pageSize] GetHistoryRequest pageSize
     */

    /**
     * Constructs a new GetHistoryRequest.
     * @exports GetHistoryRequest
     * @classdesc Represents a GetHistoryRequest.
     * @implements IGetHistoryRequest
     * @constructor
     * @param {IGetHistoryRequest=} [properties] Properties to set
     */
    function GetHistoryRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * GetHistoryRequest page.
     * @member {number} page
     * @memberof GetHistoryRequest
     * @instance
     */
    GetHistoryRequest.prototype.page = 0;

    /**
     * GetHistoryRequest pageSize.
     * @member {number} pageSize
     * @memberof GetHistoryRequest
     * @instance
     */
    GetHistoryRequest.prototype.pageSize = 0;

    /**
     * Creates a new GetHistoryRequest instance using the specified properties.
     * @function create
     * @memberof GetHistoryRequest
     * @static
     * @param {IGetHistoryRequest=} [properties] Properties to set
     * @returns {GetHistoryRequest} GetHistoryRequest instance
     */
    GetHistoryRequest.create = function create(properties) {
        return new GetHistoryRequest(properties);
    };

    /**
     * Encodes the specified GetHistoryRequest message. Does not implicitly {@link GetHistoryRequest.verify|verify} messages.
     * @function encode
     * @memberof GetHistoryRequest
     * @static
     * @param {IGetHistoryRequest} message GetHistoryRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GetHistoryRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.page != null && Object.hasOwnProperty.call(message, "page"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.page);
        if (message.pageSize != null && Object.hasOwnProperty.call(message, "pageSize"))
            writer.uint32(/* id 2, wireType 0 =*/16).int32(message.pageSize);
        return writer;
    };

    /**
     * Encodes the specified GetHistoryRequest message, length delimited. Does not implicitly {@link GetHistoryRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof GetHistoryRequest
     * @static
     * @param {IGetHistoryRequest} message GetHistoryRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GetHistoryRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a GetHistoryRequest message from the specified reader or buffer.
     * @function decode
     * @memberof GetHistoryRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {GetHistoryRequest} GetHistoryRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GetHistoryRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.GetHistoryRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.page = reader.int32();
                    break;
                }
            case 2: {
                    message.pageSize = reader.int32();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a GetHistoryRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof GetHistoryRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {GetHistoryRequest} GetHistoryRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GetHistoryRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a GetHistoryRequest message.
     * @function verify
     * @memberof GetHistoryRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    GetHistoryRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.page != null && message.hasOwnProperty("page"))
            if (!$util.isInteger(message.page))
                return "page: integer expected";
        if (message.pageSize != null && message.hasOwnProperty("pageSize"))
            if (!$util.isInteger(message.pageSize))
                return "pageSize: integer expected";
        return null;
    };

    /**
     * Creates a GetHistoryRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof GetHistoryRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {GetHistoryRequest} GetHistoryRequest
     */
    GetHistoryRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.GetHistoryRequest)
            return object;
        let message = new $root.GetHistoryRequest();
        if (object.page != null)
            message.page = object.page | 0;
        if (object.pageSize != null)
            message.pageSize = object.pageSize | 0;
        return message;
    };

    /**
     * Creates a plain object from a GetHistoryRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof GetHistoryRequest
     * @static
     * @param {GetHistoryRequest} message GetHistoryRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    GetHistoryRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.page = 0;
            object.pageSize = 0;
        }
        if (message.page != null && message.hasOwnProperty("page"))
            object.page = message.page;
        if (message.pageSize != null && message.hasOwnProperty("pageSize"))
            object.pageSize = message.pageSize;
        return object;
    };

    /**
     * Converts this GetHistoryRequest to JSON.
     * @function toJSON
     * @memberof GetHistoryRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    GetHistoryRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for GetHistoryRequest
     * @function getTypeUrl
     * @memberof GetHistoryRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    GetHistoryRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/GetHistoryRequest";
    };

    return GetHistoryRequest;
})();

export const GetHistoryResponse = $root.GetHistoryResponse = (() => {

    /**
     * Properties of a GetHistoryResponse.
     * @exports IGetHistoryResponse
     * @interface IGetHistoryResponse
     * @property {number|null} [errorCode] GetHistoryResponse errorCode
     * @property {number|null} [page] GetHistoryResponse page
     * @property {number|null} [totalCount] GetHistoryResponse totalCount
     * @property {Array.<IGameHistory>|null} [histories] GetHistoryResponse histories
     */

    /**
     * Constructs a new GetHistoryResponse.
     * @exports GetHistoryResponse
     * @classdesc Represents a GetHistoryResponse.
     * @implements IGetHistoryResponse
     * @constructor
     * @param {IGetHistoryResponse=} [properties] Properties to set
     */
    function GetHistoryResponse(properties) {
        this.histories = [];
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * GetHistoryResponse errorCode.
     * @member {number} errorCode
     * @memberof GetHistoryResponse
     * @instance
     */
    GetHistoryResponse.prototype.errorCode = 0;

    /**
     * GetHistoryResponse page.
     * @member {number} page
     * @memberof GetHistoryResponse
     * @instance
     */
    GetHistoryResponse.prototype.page = 0;

    /**
     * GetHistoryResponse totalCount.
     * @member {number} totalCount
     * @memberof GetHistoryResponse
     * @instance
     */
    GetHistoryResponse.prototype.totalCount = 0;

    /**
     * GetHistoryResponse histories.
     * @member {Array.<IGameHistory>} histories
     * @memberof GetHistoryResponse
     * @instance
     */
    GetHistoryResponse.prototype.histories = $util.emptyArray;

    /**
     * Creates a new GetHistoryResponse instance using the specified properties.
     * @function create
     * @memberof GetHistoryResponse
     * @static
     * @param {IGetHistoryResponse=} [properties] Properties to set
     * @returns {GetHistoryResponse} GetHistoryResponse instance
     */
    GetHistoryResponse.create = function create(properties) {
        return new GetHistoryResponse(properties);
    };

    /**
     * Encodes the specified GetHistoryResponse message. Does not implicitly {@link GetHistoryResponse.verify|verify} messages.
     * @function encode
     * @memberof GetHistoryResponse
     * @static
     * @param {IGetHistoryResponse} message GetHistoryResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GetHistoryResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.page != null && Object.hasOwnProperty.call(message, "page"))
            writer.uint32(/* id 2, wireType 0 =*/16).int32(message.page);
        if (message.totalCount != null && Object.hasOwnProperty.call(message, "totalCount"))
            writer.uint32(/* id 3, wireType 0 =*/24).int32(message.totalCount);
        if (message.histories != null && message.histories.length)
            for (let i = 0; i < message.histories.length; ++i)
                $root.GameHistory.encode(message.histories[i], writer.uint32(/* id 4, wireType 2 =*/34).fork()).ldelim();
        return writer;
    };

    /**
     * Encodes the specified GetHistoryResponse message, length delimited. Does not implicitly {@link GetHistoryResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof GetHistoryResponse
     * @static
     * @param {IGetHistoryResponse} message GetHistoryResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GetHistoryResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a GetHistoryResponse message from the specified reader or buffer.
     * @function decode
     * @memberof GetHistoryResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {GetHistoryResponse} GetHistoryResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GetHistoryResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.GetHistoryResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    message.page = reader.int32();
                    break;
                }
            case 3: {
                    message.totalCount = reader.int32();
                    break;
                }
            case 4: {
                    if (!(message.histories && message.histories.length))
                        message.histories = [];
                    message.histories.push($root.GameHistory.decode(reader, reader.uint32()));
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a GetHistoryResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof GetHistoryResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {GetHistoryResponse} GetHistoryResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GetHistoryResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a GetHistoryResponse message.
     * @function verify
     * @memberof GetHistoryResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    GetHistoryResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.page != null && message.hasOwnProperty("page"))
            if (!$util.isInteger(message.page))
                return "page: integer expected";
        if (message.totalCount != null && message.hasOwnProperty("totalCount"))
            if (!$util.isInteger(message.totalCount))
                return "totalCount: integer expected";
        if (message.histories != null && message.hasOwnProperty("histories")) {
            if (!Array.isArray(message.histories))
                return "histories: array expected";
            for (let i = 0; i < message.histories.length; ++i) {
                let error = $root.GameHistory.verify(message.histories[i]);
                if (error)
                    return "histories." + error;
            }
        }
        return null;
    };

    /**
     * Creates a GetHistoryResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof GetHistoryResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {GetHistoryResponse} GetHistoryResponse
     */
    GetHistoryResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.GetHistoryResponse)
            return object;
        let message = new $root.GetHistoryResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.page != null)
            message.page = object.page | 0;
        if (object.totalCount != null)
            message.totalCount = object.totalCount | 0;
        if (object.histories) {
            if (!Array.isArray(object.histories))
                throw TypeError(".GetHistoryResponse.histories: array expected");
            message.histories = [];
            for (let i = 0; i < object.histories.length; ++i) {
                if (typeof object.histories[i] !== "object")
                    throw TypeError(".GetHistoryResponse.histories: object expected");
                message.histories[i] = $root.GameHistory.fromObject(object.histories[i]);
            }
        }
        return message;
    };

    /**
     * Creates a plain object from a GetHistoryResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof GetHistoryResponse
     * @static
     * @param {GetHistoryResponse} message GetHistoryResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    GetHistoryResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.arrays || options.defaults)
            object.histories = [];
        if (options.defaults) {
            object.errorCode = 0;
            object.page = 0;
            object.totalCount = 0;
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.page != null && message.hasOwnProperty("page"))
            object.page = message.page;
        if (message.totalCount != null && message.hasOwnProperty("totalCount"))
            object.totalCount = message.totalCount;
        if (message.histories && message.histories.length) {
            object.histories = [];
            for (let j = 0; j < message.histories.length; ++j)
                object.histories[j] = $root.GameHistory.toObject(message.histories[j], options);
        }
        return object;
    };

    /**
     * Converts this GetHistoryResponse to JSON.
     * @function toJSON
     * @memberof GetHistoryResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    GetHistoryResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for GetHistoryResponse
     * @function getTypeUrl
     * @memberof GetHistoryResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    GetHistoryResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/GetHistoryResponse";
    };

    return GetHistoryResponse;
})();

export const GameHistory = $root.GameHistory = (() => {

    /**
     * Properties of a GameHistory.
     * @exports IGameHistory
     * @interface IGameHistory
     * @property {string|null} [id] GameHistory id
     * @property {number|Long|null} [time] GameHistory time
     * @property {number|null} [betAmount] GameHistory betAmount
     * @property {number|null} [multiple] GameHistory multiple
     * @property {number|null} [winAmount] GameHistory winAmount
     * @property {string|null} [serverSeed] GameHistory serverSeed
     * @property {string|null} [userSeed] GameHistory userSeed
     * @property {string|null} [hashCode] GameHistory hashCode
     * @property {string|null} [hexCode] GameHistory hexCode
     * @property {string|null} [decimalCode] GameHistory decimalCode
     */

    /**
     * Constructs a new GameHistory.
     * @exports GameHistory
     * @classdesc Represents a GameHistory.
     * @implements IGameHistory
     * @constructor
     * @param {IGameHistory=} [properties] Properties to set
     */
    function GameHistory(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * GameHistory id.
     * @member {string} id
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.id = "";

    /**
     * GameHistory time.
     * @member {number|Long} time
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.time = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * GameHistory betAmount.
     * @member {number} betAmount
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.betAmount = 0;

    /**
     * GameHistory multiple.
     * @member {number} multiple
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.multiple = 0;

    /**
     * GameHistory winAmount.
     * @member {number} winAmount
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.winAmount = 0;

    /**
     * GameHistory serverSeed.
     * @member {string} serverSeed
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.serverSeed = "";

    /**
     * GameHistory userSeed.
     * @member {string} userSeed
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.userSeed = "";

    /**
     * GameHistory hashCode.
     * @member {string} hashCode
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.hashCode = "";

    /**
     * GameHistory hexCode.
     * @member {string} hexCode
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.hexCode = "";

    /**
     * GameHistory decimalCode.
     * @member {string} decimalCode
     * @memberof GameHistory
     * @instance
     */
    GameHistory.prototype.decimalCode = "";

    /**
     * Creates a new GameHistory instance using the specified properties.
     * @function create
     * @memberof GameHistory
     * @static
     * @param {IGameHistory=} [properties] Properties to set
     * @returns {GameHistory} GameHistory instance
     */
    GameHistory.create = function create(properties) {
        return new GameHistory(properties);
    };

    /**
     * Encodes the specified GameHistory message. Does not implicitly {@link GameHistory.verify|verify} messages.
     * @function encode
     * @memberof GameHistory
     * @static
     * @param {IGameHistory} message GameHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GameHistory.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.id != null && Object.hasOwnProperty.call(message, "id"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.id);
        if (message.time != null && Object.hasOwnProperty.call(message, "time"))
            writer.uint32(/* id 2, wireType 0 =*/16).int64(message.time);
        if (message.betAmount != null && Object.hasOwnProperty.call(message, "betAmount"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.betAmount);
        if (message.multiple != null && Object.hasOwnProperty.call(message, "multiple"))
            writer.uint32(/* id 4, wireType 1 =*/33).double(message.multiple);
        if (message.winAmount != null && Object.hasOwnProperty.call(message, "winAmount"))
            writer.uint32(/* id 5, wireType 1 =*/41).double(message.winAmount);
        if (message.serverSeed != null && Object.hasOwnProperty.call(message, "serverSeed"))
            writer.uint32(/* id 6, wireType 2 =*/50).string(message.serverSeed);
        if (message.userSeed != null && Object.hasOwnProperty.call(message, "userSeed"))
            writer.uint32(/* id 7, wireType 2 =*/58).string(message.userSeed);
        if (message.hashCode != null && Object.hasOwnProperty.call(message, "hashCode"))
            writer.uint32(/* id 8, wireType 2 =*/66).string(message.hashCode);
        if (message.hexCode != null && Object.hasOwnProperty.call(message, "hexCode"))
            writer.uint32(/* id 9, wireType 2 =*/74).string(message.hexCode);
        if (message.decimalCode != null && Object.hasOwnProperty.call(message, "decimalCode"))
            writer.uint32(/* id 10, wireType 2 =*/82).string(message.decimalCode);
        return writer;
    };

    /**
     * Encodes the specified GameHistory message, length delimited. Does not implicitly {@link GameHistory.verify|verify} messages.
     * @function encodeDelimited
     * @memberof GameHistory
     * @static
     * @param {IGameHistory} message GameHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GameHistory.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a GameHistory message from the specified reader or buffer.
     * @function decode
     * @memberof GameHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {GameHistory} GameHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GameHistory.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.GameHistory();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.id = reader.string();
                    break;
                }
            case 2: {
                    message.time = reader.int64();
                    break;
                }
            case 3: {
                    message.betAmount = reader.double();
                    break;
                }
            case 4: {
                    message.multiple = reader.double();
                    break;
                }
            case 5: {
                    message.winAmount = reader.double();
                    break;
                }
            case 6: {
                    message.serverSeed = reader.string();
                    break;
                }
            case 7: {
                    message.userSeed = reader.string();
                    break;
                }
            case 8: {
                    message.hashCode = reader.string();
                    break;
                }
            case 9: {
                    message.hexCode = reader.string();
                    break;
                }
            case 10: {
                    message.decimalCode = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a GameHistory message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof GameHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {GameHistory} GameHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GameHistory.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a GameHistory message.
     * @function verify
     * @memberof GameHistory
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    GameHistory.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.id != null && message.hasOwnProperty("id"))
            if (!$util.isString(message.id))
                return "id: string expected";
        if (message.time != null && message.hasOwnProperty("time"))
            if (!$util.isInteger(message.time) && !(message.time && $util.isInteger(message.time.low) && $util.isInteger(message.time.high)))
                return "time: integer|Long expected";
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            if (typeof message.betAmount !== "number")
                return "betAmount: number expected";
        if (message.multiple != null && message.hasOwnProperty("multiple"))
            if (typeof message.multiple !== "number")
                return "multiple: number expected";
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            if (typeof message.winAmount !== "number")
                return "winAmount: number expected";
        if (message.serverSeed != null && message.hasOwnProperty("serverSeed"))
            if (!$util.isString(message.serverSeed))
                return "serverSeed: string expected";
        if (message.userSeed != null && message.hasOwnProperty("userSeed"))
            if (!$util.isString(message.userSeed))
                return "userSeed: string expected";
        if (message.hashCode != null && message.hasOwnProperty("hashCode"))
            if (!$util.isString(message.hashCode))
                return "hashCode: string expected";
        if (message.hexCode != null && message.hasOwnProperty("hexCode"))
            if (!$util.isString(message.hexCode))
                return "hexCode: string expected";
        if (message.decimalCode != null && message.hasOwnProperty("decimalCode"))
            if (!$util.isString(message.decimalCode))
                return "decimalCode: string expected";
        return null;
    };

    /**
     * Creates a GameHistory message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof GameHistory
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {GameHistory} GameHistory
     */
    GameHistory.fromObject = function fromObject(object) {
        if (object instanceof $root.GameHistory)
            return object;
        let message = new $root.GameHistory();
        if (object.id != null)
            message.id = String(object.id);
        if (object.time != null)
            if ($util.Long)
                (message.time = $util.Long.fromValue(object.time)).unsigned = false;
            else if (typeof object.time === "string")
                message.time = parseInt(object.time, 10);
            else if (typeof object.time === "number")
                message.time = object.time;
            else if (typeof object.time === "object")
                message.time = new $util.LongBits(object.time.low >>> 0, object.time.high >>> 0).toNumber();
        if (object.betAmount != null)
            message.betAmount = Number(object.betAmount);
        if (object.multiple != null)
            message.multiple = Number(object.multiple);
        if (object.winAmount != null)
            message.winAmount = Number(object.winAmount);
        if (object.serverSeed != null)
            message.serverSeed = String(object.serverSeed);
        if (object.userSeed != null)
            message.userSeed = String(object.userSeed);
        if (object.hashCode != null)
            message.hashCode = String(object.hashCode);
        if (object.hexCode != null)
            message.hexCode = String(object.hexCode);
        if (object.decimalCode != null)
            message.decimalCode = String(object.decimalCode);
        return message;
    };

    /**
     * Creates a plain object from a GameHistory message. Also converts values to other types if specified.
     * @function toObject
     * @memberof GameHistory
     * @static
     * @param {GameHistory} message GameHistory
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    GameHistory.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.id = "";
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.time = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.time = options.longs === String ? "0" : 0;
            object.betAmount = 0;
            object.multiple = 0;
            object.winAmount = 0;
            object.serverSeed = "";
            object.userSeed = "";
            object.hashCode = "";
            object.hexCode = "";
            object.decimalCode = "";
        }
        if (message.id != null && message.hasOwnProperty("id"))
            object.id = message.id;
        if (message.time != null && message.hasOwnProperty("time"))
            if (typeof message.time === "number")
                object.time = options.longs === String ? String(message.time) : message.time;
            else
                object.time = options.longs === String ? $util.Long.prototype.toString.call(message.time) : options.longs === Number ? new $util.LongBits(message.time.low >>> 0, message.time.high >>> 0).toNumber() : message.time;
        if (message.betAmount != null && message.hasOwnProperty("betAmount"))
            object.betAmount = options.json && !isFinite(message.betAmount) ? String(message.betAmount) : message.betAmount;
        if (message.multiple != null && message.hasOwnProperty("multiple"))
            object.multiple = options.json && !isFinite(message.multiple) ? String(message.multiple) : message.multiple;
        if (message.winAmount != null && message.hasOwnProperty("winAmount"))
            object.winAmount = options.json && !isFinite(message.winAmount) ? String(message.winAmount) : message.winAmount;
        if (message.serverSeed != null && message.hasOwnProperty("serverSeed"))
            object.serverSeed = message.serverSeed;
        if (message.userSeed != null && message.hasOwnProperty("userSeed"))
            object.userSeed = message.userSeed;
        if (message.hashCode != null && message.hasOwnProperty("hashCode"))
            object.hashCode = message.hashCode;
        if (message.hexCode != null && message.hasOwnProperty("hexCode"))
            object.hexCode = message.hexCode;
        if (message.decimalCode != null && message.hasOwnProperty("decimalCode"))
            object.decimalCode = message.decimalCode;
        return object;
    };

    /**
     * Converts this GameHistory to JSON.
     * @function toJSON
     * @memberof GameHistory
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    GameHistory.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for GameHistory
     * @function getTypeUrl
     * @memberof GameHistory
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    GameHistory.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/GameHistory";
    };

    return GameHistory;
})();

export const ChangeNicknameRequest = $root.ChangeNicknameRequest = (() => {

    /**
     * Properties of a ChangeNicknameRequest.
     * @exports IChangeNicknameRequest
     * @interface IChangeNicknameRequest
     * @property {string|null} [nickname] ChangeNicknameRequest nickname
     */

    /**
     * Constructs a new ChangeNicknameRequest.
     * @exports ChangeNicknameRequest
     * @classdesc Represents a ChangeNicknameRequest.
     * @implements IChangeNicknameRequest
     * @constructor
     * @param {IChangeNicknameRequest=} [properties] Properties to set
     */
    function ChangeNicknameRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ChangeNicknameRequest nickname.
     * @member {string} nickname
     * @memberof ChangeNicknameRequest
     * @instance
     */
    ChangeNicknameRequest.prototype.nickname = "";

    /**
     * Creates a new ChangeNicknameRequest instance using the specified properties.
     * @function create
     * @memberof ChangeNicknameRequest
     * @static
     * @param {IChangeNicknameRequest=} [properties] Properties to set
     * @returns {ChangeNicknameRequest} ChangeNicknameRequest instance
     */
    ChangeNicknameRequest.create = function create(properties) {
        return new ChangeNicknameRequest(properties);
    };

    /**
     * Encodes the specified ChangeNicknameRequest message. Does not implicitly {@link ChangeNicknameRequest.verify|verify} messages.
     * @function encode
     * @memberof ChangeNicknameRequest
     * @static
     * @param {IChangeNicknameRequest} message ChangeNicknameRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeNicknameRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.nickname != null && Object.hasOwnProperty.call(message, "nickname"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.nickname);
        return writer;
    };

    /**
     * Encodes the specified ChangeNicknameRequest message, length delimited. Does not implicitly {@link ChangeNicknameRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ChangeNicknameRequest
     * @static
     * @param {IChangeNicknameRequest} message ChangeNicknameRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeNicknameRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ChangeNicknameRequest message from the specified reader or buffer.
     * @function decode
     * @memberof ChangeNicknameRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ChangeNicknameRequest} ChangeNicknameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeNicknameRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ChangeNicknameRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.nickname = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a ChangeNicknameRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ChangeNicknameRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ChangeNicknameRequest} ChangeNicknameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeNicknameRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ChangeNicknameRequest message.
     * @function verify
     * @memberof ChangeNicknameRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ChangeNicknameRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            if (!$util.isString(message.nickname))
                return "nickname: string expected";
        return null;
    };

    /**
     * Creates a ChangeNicknameRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ChangeNicknameRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ChangeNicknameRequest} ChangeNicknameRequest
     */
    ChangeNicknameRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.ChangeNicknameRequest)
            return object;
        let message = new $root.ChangeNicknameRequest();
        if (object.nickname != null)
            message.nickname = String(object.nickname);
        return message;
    };

    /**
     * Creates a plain object from a ChangeNicknameRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ChangeNicknameRequest
     * @static
     * @param {ChangeNicknameRequest} message ChangeNicknameRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ChangeNicknameRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.nickname = "";
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            object.nickname = message.nickname;
        return object;
    };

    /**
     * Converts this ChangeNicknameRequest to JSON.
     * @function toJSON
     * @memberof ChangeNicknameRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ChangeNicknameRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ChangeNicknameRequest
     * @function getTypeUrl
     * @memberof ChangeNicknameRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ChangeNicknameRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeNicknameRequest";
    };

    return ChangeNicknameRequest;
})();

export const ChangeNicknameResponse = $root.ChangeNicknameResponse = (() => {

    /**
     * Properties of a ChangeNicknameResponse.
     * @exports IChangeNicknameResponse
     * @interface IChangeNicknameResponse
     * @property {number|null} [errorCode] ChangeNicknameResponse errorCode
     * @property {string|null} [nickname] ChangeNicknameResponse nickname
     */

    /**
     * Constructs a new ChangeNicknameResponse.
     * @exports ChangeNicknameResponse
     * @classdesc Represents a ChangeNicknameResponse.
     * @implements IChangeNicknameResponse
     * @constructor
     * @param {IChangeNicknameResponse=} [properties] Properties to set
     */
    function ChangeNicknameResponse(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ChangeNicknameResponse errorCode.
     * @member {number} errorCode
     * @memberof ChangeNicknameResponse
     * @instance
     */
    ChangeNicknameResponse.prototype.errorCode = 0;

    /**
     * ChangeNicknameResponse nickname.
     * @member {string} nickname
     * @memberof ChangeNicknameResponse
     * @instance
     */
    ChangeNicknameResponse.prototype.nickname = "";

    /**
     * Creates a new ChangeNicknameResponse instance using the specified properties.
     * @function create
     * @memberof ChangeNicknameResponse
     * @static
     * @param {IChangeNicknameResponse=} [properties] Properties to set
     * @returns {ChangeNicknameResponse} ChangeNicknameResponse instance
     */
    ChangeNicknameResponse.create = function create(properties) {
        return new ChangeNicknameResponse(properties);
    };

    /**
     * Encodes the specified ChangeNicknameResponse message. Does not implicitly {@link ChangeNicknameResponse.verify|verify} messages.
     * @function encode
     * @memberof ChangeNicknameResponse
     * @static
     * @param {IChangeNicknameResponse} message ChangeNicknameResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeNicknameResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.nickname != null && Object.hasOwnProperty.call(message, "nickname"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.nickname);
        return writer;
    };

    /**
     * Encodes the specified ChangeNicknameResponse message, length delimited. Does not implicitly {@link ChangeNicknameResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ChangeNicknameResponse
     * @static
     * @param {IChangeNicknameResponse} message ChangeNicknameResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeNicknameResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ChangeNicknameResponse message from the specified reader or buffer.
     * @function decode
     * @memberof ChangeNicknameResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ChangeNicknameResponse} ChangeNicknameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeNicknameResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ChangeNicknameResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    message.nickname = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a ChangeNicknameResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ChangeNicknameResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ChangeNicknameResponse} ChangeNicknameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeNicknameResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ChangeNicknameResponse message.
     * @function verify
     * @memberof ChangeNicknameResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ChangeNicknameResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            if (!$util.isString(message.nickname))
                return "nickname: string expected";
        return null;
    };

    /**
     * Creates a ChangeNicknameResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ChangeNicknameResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ChangeNicknameResponse} ChangeNicknameResponse
     */
    ChangeNicknameResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.ChangeNicknameResponse)
            return object;
        let message = new $root.ChangeNicknameResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.nickname != null)
            message.nickname = String(object.nickname);
        return message;
    };

    /**
     * Creates a plain object from a ChangeNicknameResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ChangeNicknameResponse
     * @static
     * @param {ChangeNicknameResponse} message ChangeNicknameResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ChangeNicknameResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.errorCode = 0;
            object.nickname = "";
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.nickname != null && message.hasOwnProperty("nickname"))
            object.nickname = message.nickname;
        return object;
    };

    /**
     * Converts this ChangeNicknameResponse to JSON.
     * @function toJSON
     * @memberof ChangeNicknameResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ChangeNicknameResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ChangeNicknameResponse
     * @function getTypeUrl
     * @memberof ChangeNicknameResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ChangeNicknameResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeNicknameResponse";
    };

    return ChangeNicknameResponse;
})();

export const ChangeAvatarRequest = $root.ChangeAvatarRequest = (() => {

    /**
     * Properties of a ChangeAvatarRequest.
     * @exports IChangeAvatarRequest
     * @interface IChangeAvatarRequest
     * @property {string|null} [avatar] ChangeAvatarRequest avatar
     */

    /**
     * Constructs a new ChangeAvatarRequest.
     * @exports ChangeAvatarRequest
     * @classdesc Represents a ChangeAvatarRequest.
     * @implements IChangeAvatarRequest
     * @constructor
     * @param {IChangeAvatarRequest=} [properties] Properties to set
     */
    function ChangeAvatarRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ChangeAvatarRequest avatar.
     * @member {string} avatar
     * @memberof ChangeAvatarRequest
     * @instance
     */
    ChangeAvatarRequest.prototype.avatar = "";

    /**
     * Creates a new ChangeAvatarRequest instance using the specified properties.
     * @function create
     * @memberof ChangeAvatarRequest
     * @static
     * @param {IChangeAvatarRequest=} [properties] Properties to set
     * @returns {ChangeAvatarRequest} ChangeAvatarRequest instance
     */
    ChangeAvatarRequest.create = function create(properties) {
        return new ChangeAvatarRequest(properties);
    };

    /**
     * Encodes the specified ChangeAvatarRequest message. Does not implicitly {@link ChangeAvatarRequest.verify|verify} messages.
     * @function encode
     * @memberof ChangeAvatarRequest
     * @static
     * @param {IChangeAvatarRequest} message ChangeAvatarRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeAvatarRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.avatar);
        return writer;
    };

    /**
     * Encodes the specified ChangeAvatarRequest message, length delimited. Does not implicitly {@link ChangeAvatarRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ChangeAvatarRequest
     * @static
     * @param {IChangeAvatarRequest} message ChangeAvatarRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeAvatarRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ChangeAvatarRequest message from the specified reader or buffer.
     * @function decode
     * @memberof ChangeAvatarRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ChangeAvatarRequest} ChangeAvatarRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeAvatarRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ChangeAvatarRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.avatar = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a ChangeAvatarRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ChangeAvatarRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ChangeAvatarRequest} ChangeAvatarRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeAvatarRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ChangeAvatarRequest message.
     * @function verify
     * @memberof ChangeAvatarRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ChangeAvatarRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            if (!$util.isString(message.avatar))
                return "avatar: string expected";
        return null;
    };

    /**
     * Creates a ChangeAvatarRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ChangeAvatarRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ChangeAvatarRequest} ChangeAvatarRequest
     */
    ChangeAvatarRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.ChangeAvatarRequest)
            return object;
        let message = new $root.ChangeAvatarRequest();
        if (object.avatar != null)
            message.avatar = String(object.avatar);
        return message;
    };

    /**
     * Creates a plain object from a ChangeAvatarRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ChangeAvatarRequest
     * @static
     * @param {ChangeAvatarRequest} message ChangeAvatarRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ChangeAvatarRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.avatar = "";
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            object.avatar = message.avatar;
        return object;
    };

    /**
     * Converts this ChangeAvatarRequest to JSON.
     * @function toJSON
     * @memberof ChangeAvatarRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ChangeAvatarRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ChangeAvatarRequest
     * @function getTypeUrl
     * @memberof ChangeAvatarRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ChangeAvatarRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeAvatarRequest";
    };

    return ChangeAvatarRequest;
})();

export const ChangeAvatarResponse = $root.ChangeAvatarResponse = (() => {

    /**
     * Properties of a ChangeAvatarResponse.
     * @exports IChangeAvatarResponse
     * @interface IChangeAvatarResponse
     * @property {number|null} [errorCode] ChangeAvatarResponse errorCode
     * @property {string|null} [avatar] ChangeAvatarResponse avatar
     */

    /**
     * Constructs a new ChangeAvatarResponse.
     * @exports ChangeAvatarResponse
     * @classdesc Represents a ChangeAvatarResponse.
     * @implements IChangeAvatarResponse
     * @constructor
     * @param {IChangeAvatarResponse=} [properties] Properties to set
     */
    function ChangeAvatarResponse(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ChangeAvatarResponse errorCode.
     * @member {number} errorCode
     * @memberof ChangeAvatarResponse
     * @instance
     */
    ChangeAvatarResponse.prototype.errorCode = 0;

    /**
     * ChangeAvatarResponse avatar.
     * @member {string} avatar
     * @memberof ChangeAvatarResponse
     * @instance
     */
    ChangeAvatarResponse.prototype.avatar = "";

    /**
     * Creates a new ChangeAvatarResponse instance using the specified properties.
     * @function create
     * @memberof ChangeAvatarResponse
     * @static
     * @param {IChangeAvatarResponse=} [properties] Properties to set
     * @returns {ChangeAvatarResponse} ChangeAvatarResponse instance
     */
    ChangeAvatarResponse.create = function create(properties) {
        return new ChangeAvatarResponse(properties);
    };

    /**
     * Encodes the specified ChangeAvatarResponse message. Does not implicitly {@link ChangeAvatarResponse.verify|verify} messages.
     * @function encode
     * @memberof ChangeAvatarResponse
     * @static
     * @param {IChangeAvatarResponse} message ChangeAvatarResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeAvatarResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
            writer.uint32(/* id 3, wireType 2 =*/26).string(message.avatar);
        return writer;
    };

    /**
     * Encodes the specified ChangeAvatarResponse message, length delimited. Does not implicitly {@link ChangeAvatarResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ChangeAvatarResponse
     * @static
     * @param {IChangeAvatarResponse} message ChangeAvatarResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ChangeAvatarResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ChangeAvatarResponse message from the specified reader or buffer.
     * @function decode
     * @memberof ChangeAvatarResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ChangeAvatarResponse} ChangeAvatarResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeAvatarResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ChangeAvatarResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 3: {
                    message.avatar = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a ChangeAvatarResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ChangeAvatarResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ChangeAvatarResponse} ChangeAvatarResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ChangeAvatarResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ChangeAvatarResponse message.
     * @function verify
     * @memberof ChangeAvatarResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ChangeAvatarResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            if (!$util.isString(message.avatar))
                return "avatar: string expected";
        return null;
    };

    /**
     * Creates a ChangeAvatarResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ChangeAvatarResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ChangeAvatarResponse} ChangeAvatarResponse
     */
    ChangeAvatarResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.ChangeAvatarResponse)
            return object;
        let message = new $root.ChangeAvatarResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.avatar != null)
            message.avatar = String(object.avatar);
        return message;
    };

    /**
     * Creates a plain object from a ChangeAvatarResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ChangeAvatarResponse
     * @static
     * @param {ChangeAvatarResponse} message ChangeAvatarResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ChangeAvatarResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.errorCode = 0;
            object.avatar = "";
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            object.avatar = message.avatar;
        return object;
    };

    /**
     * Converts this ChangeAvatarResponse to JSON.
     * @function toJSON
     * @memberof ChangeAvatarResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ChangeAvatarResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ChangeAvatarResponse
     * @function getTypeUrl
     * @memberof ChangeAvatarResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ChangeAvatarResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeAvatarResponse";
    };

    return ChangeAvatarResponse;
})();

export const MessageHeader = $root.MessageHeader = (() => {

    /**
     * Properties of a MessageHeader.
     * @exports IMessageHeader
     * @interface IMessageHeader
     * @property {number|null} [serverId] MessageHeader serverId
     * @property {number|null} [messageId] MessageHeader messageId
     * @property {number|null} [requestId] MessageHeader requestId
     * @property {boolean|null} [isResponse] MessageHeader isResponse
     * @property {number|null} [errCode] MessageHeader errCode
     */

    /**
     * Constructs a new MessageHeader.
     * @exports MessageHeader
     * @classdesc Represents a MessageHeader.
     * @implements IMessageHeader
     * @constructor
     * @param {IMessageHeader=} [properties] Properties to set
     */
    function MessageHeader(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * MessageHeader serverId.
     * @member {number} serverId
     * @memberof MessageHeader
     * @instance
     */
    MessageHeader.prototype.serverId = 0;

    /**
     * MessageHeader messageId.
     * @member {number} messageId
     * @memberof MessageHeader
     * @instance
     */
    MessageHeader.prototype.messageId = 0;

    /**
     * MessageHeader requestId.
     * @member {number} requestId
     * @memberof MessageHeader
     * @instance
     */
    MessageHeader.prototype.requestId = 0;

    /**
     * MessageHeader isResponse.
     * @member {boolean} isResponse
     * @memberof MessageHeader
     * @instance
     */
    MessageHeader.prototype.isResponse = false;

    /**
     * MessageHeader errCode.
     * @member {number} errCode
     * @memberof MessageHeader
     * @instance
     */
    MessageHeader.prototype.errCode = 0;

    /**
     * Creates a new MessageHeader instance using the specified properties.
     * @function create
     * @memberof MessageHeader
     * @static
     * @param {IMessageHeader=} [properties] Properties to set
     * @returns {MessageHeader} MessageHeader instance
     */
    MessageHeader.create = function create(properties) {
        return new MessageHeader(properties);
    };

    /**
     * Encodes the specified MessageHeader message. Does not implicitly {@link MessageHeader.verify|verify} messages.
     * @function encode
     * @memberof MessageHeader
     * @static
     * @param {IMessageHeader} message MessageHeader message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MessageHeader.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.serverId != null && Object.hasOwnProperty.call(message, "serverId"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.serverId);
        if (message.messageId != null && Object.hasOwnProperty.call(message, "messageId"))
            writer.uint32(/* id 2, wireType 0 =*/16).uint32(message.messageId);
        if (message.requestId != null && Object.hasOwnProperty.call(message, "requestId"))
            writer.uint32(/* id 3, wireType 0 =*/24).uint32(message.requestId);
        if (message.isResponse != null && Object.hasOwnProperty.call(message, "isResponse"))
            writer.uint32(/* id 4, wireType 0 =*/32).bool(message.isResponse);
        if (message.errCode != null && Object.hasOwnProperty.call(message, "errCode"))
            writer.uint32(/* id 5, wireType 0 =*/40).int32(message.errCode);
        return writer;
    };

    /**
     * Encodes the specified MessageHeader message, length delimited. Does not implicitly {@link MessageHeader.verify|verify} messages.
     * @function encodeDelimited
     * @memberof MessageHeader
     * @static
     * @param {IMessageHeader} message MessageHeader message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MessageHeader.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a MessageHeader message from the specified reader or buffer.
     * @function decode
     * @memberof MessageHeader
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {MessageHeader} MessageHeader
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MessageHeader.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.MessageHeader();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.serverId = reader.int32();
                    break;
                }
            case 2: {
                    message.messageId = reader.uint32();
                    break;
                }
            case 3: {
                    message.requestId = reader.uint32();
                    break;
                }
            case 4: {
                    message.isResponse = reader.bool();
                    break;
                }
            case 5: {
                    message.errCode = reader.int32();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a MessageHeader message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof MessageHeader
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {MessageHeader} MessageHeader
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MessageHeader.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a MessageHeader message.
     * @function verify
     * @memberof MessageHeader
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    MessageHeader.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.serverId != null && message.hasOwnProperty("serverId"))
            if (!$util.isInteger(message.serverId))
                return "serverId: integer expected";
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            if (!$util.isInteger(message.messageId))
                return "messageId: integer expected";
        if (message.requestId != null && message.hasOwnProperty("requestId"))
            if (!$util.isInteger(message.requestId))
                return "requestId: integer expected";
        if (message.isResponse != null && message.hasOwnProperty("isResponse"))
            if (typeof message.isResponse !== "boolean")
                return "isResponse: boolean expected";
        if (message.errCode != null && message.hasOwnProperty("errCode"))
            if (!$util.isInteger(message.errCode))
                return "errCode: integer expected";
        return null;
    };

    /**
     * Creates a MessageHeader message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof MessageHeader
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {MessageHeader} MessageHeader
     */
    MessageHeader.fromObject = function fromObject(object) {
        if (object instanceof $root.MessageHeader)
            return object;
        let message = new $root.MessageHeader();
        if (object.serverId != null)
            message.serverId = object.serverId | 0;
        if (object.messageId != null)
            message.messageId = object.messageId >>> 0;
        if (object.requestId != null)
            message.requestId = object.requestId >>> 0;
        if (object.isResponse != null)
            message.isResponse = Boolean(object.isResponse);
        if (object.errCode != null)
            message.errCode = object.errCode | 0;
        return message;
    };

    /**
     * Creates a plain object from a MessageHeader message. Also converts values to other types if specified.
     * @function toObject
     * @memberof MessageHeader
     * @static
     * @param {MessageHeader} message MessageHeader
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    MessageHeader.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.serverId = 0;
            object.messageId = 0;
            object.requestId = 0;
            object.isResponse = false;
            object.errCode = 0;
        }
        if (message.serverId != null && message.hasOwnProperty("serverId"))
            object.serverId = message.serverId;
        if (message.messageId != null && message.hasOwnProperty("messageId"))
            object.messageId = message.messageId;
        if (message.requestId != null && message.hasOwnProperty("requestId"))
            object.requestId = message.requestId;
        if (message.isResponse != null && message.hasOwnProperty("isResponse"))
            object.isResponse = message.isResponse;
        if (message.errCode != null && message.hasOwnProperty("errCode"))
            object.errCode = message.errCode;
        return object;
    };

    /**
     * Converts this MessageHeader to JSON.
     * @function toJSON
     * @memberof MessageHeader
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    MessageHeader.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for MessageHeader
     * @function getTypeUrl
     * @memberof MessageHeader
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    MessageHeader.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/MessageHeader";
    };

    return MessageHeader;
})();

export const WrappedMessage = $root.WrappedMessage = (() => {

    /**
     * Properties of a WrappedMessage.
     * @exports IWrappedMessage
     * @interface IWrappedMessage
     * @property {IMessageHeader|null} [header] WrappedMessage header
     * @property {Uint8Array|null} [body] WrappedMessage body
     */

    /**
     * Constructs a new WrappedMessage.
     * @exports WrappedMessage
     * @classdesc Represents a WrappedMessage.
     * @implements IWrappedMessage
     * @constructor
     * @param {IWrappedMessage=} [properties] Properties to set
     */
    function WrappedMessage(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * WrappedMessage header.
     * @member {IMessageHeader|null|undefined} header
     * @memberof WrappedMessage
     * @instance
     */
    WrappedMessage.prototype.header = null;

    /**
     * WrappedMessage body.
     * @member {Uint8Array} body
     * @memberof WrappedMessage
     * @instance
     */
    WrappedMessage.prototype.body = $util.newBuffer([]);

    /**
     * Creates a new WrappedMessage instance using the specified properties.
     * @function create
     * @memberof WrappedMessage
     * @static
     * @param {IWrappedMessage=} [properties] Properties to set
     * @returns {WrappedMessage} WrappedMessage instance
     */
    WrappedMessage.create = function create(properties) {
        return new WrappedMessage(properties);
    };

    /**
     * Encodes the specified WrappedMessage message. Does not implicitly {@link WrappedMessage.verify|verify} messages.
     * @function encode
     * @memberof WrappedMessage
     * @static
     * @param {IWrappedMessage} message WrappedMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    WrappedMessage.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.header != null && Object.hasOwnProperty.call(message, "header"))
            $root.MessageHeader.encode(message.header, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
        if (message.body != null && Object.hasOwnProperty.call(message, "body"))
            writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.body);
        return writer;
    };

    /**
     * Encodes the specified WrappedMessage message, length delimited. Does not implicitly {@link WrappedMessage.verify|verify} messages.
     * @function encodeDelimited
     * @memberof WrappedMessage
     * @static
     * @param {IWrappedMessage} message WrappedMessage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    WrappedMessage.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a WrappedMessage message from the specified reader or buffer.
     * @function decode
     * @memberof WrappedMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {WrappedMessage} WrappedMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    WrappedMessage.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.WrappedMessage();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.header = $root.MessageHeader.decode(reader, reader.uint32());
                    break;
                }
            case 2: {
                    message.body = reader.bytes();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a WrappedMessage message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof WrappedMessage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {WrappedMessage} WrappedMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    WrappedMessage.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a WrappedMessage message.
     * @function verify
     * @memberof WrappedMessage
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    WrappedMessage.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.header != null && message.hasOwnProperty("header")) {
            let error = $root.MessageHeader.verify(message.header);
            if (error)
                return "header." + error;
        }
        if (message.body != null && message.hasOwnProperty("body"))
            if (!(message.body && typeof message.body.length === "number" || $util.isString(message.body)))
                return "body: buffer expected";
        return null;
    };

    /**
     * Creates a WrappedMessage message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof WrappedMessage
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {WrappedMessage} WrappedMessage
     */
    WrappedMessage.fromObject = function fromObject(object) {
        if (object instanceof $root.WrappedMessage)
            return object;
        let message = new $root.WrappedMessage();
        if (object.header != null) {
            if (typeof object.header !== "object")
                throw TypeError(".WrappedMessage.header: object expected");
            message.header = $root.MessageHeader.fromObject(object.header);
        }
        if (object.body != null)
            if (typeof object.body === "string")
                $util.base64.decode(object.body, message.body = $util.newBuffer($util.base64.length(object.body)), 0);
            else if (object.body.length >= 0)
                message.body = object.body;
        return message;
    };

    /**
     * Creates a plain object from a WrappedMessage message. Also converts values to other types if specified.
     * @function toObject
     * @memberof WrappedMessage
     * @static
     * @param {WrappedMessage} message WrappedMessage
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    WrappedMessage.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.header = null;
            if (options.bytes === String)
                object.body = "";
            else {
                object.body = [];
                if (options.bytes !== Array)
                    object.body = $util.newBuffer(object.body);
            }
        }
        if (message.header != null && message.hasOwnProperty("header"))
            object.header = $root.MessageHeader.toObject(message.header, options);
        if (message.body != null && message.hasOwnProperty("body"))
            object.body = options.bytes === String ? $util.base64.encode(message.body, 0, message.body.length) : options.bytes === Array ? Array.prototype.slice.call(message.body) : message.body;
        return object;
    };

    /**
     * Converts this WrappedMessage to JSON.
     * @function toJSON
     * @memberof WrappedMessage
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    WrappedMessage.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for WrappedMessage
     * @function getTypeUrl
     * @memberof WrappedMessage
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    WrappedMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/WrappedMessage";
    };

    return WrappedMessage;
})();

export const LoginRequest = $root.LoginRequest = (() => {

    /**
     * Properties of a LoginRequest.
     * @exports ILoginRequest
     * @interface ILoginRequest
     * @property {string|null} [userName] LoginRequest userName
     * @property {string|null} [password] LoginRequest password
     */

    /**
     * Constructs a new LoginRequest.
     * @exports LoginRequest
     * @classdesc Represents a LoginRequest.
     * @implements ILoginRequest
     * @constructor
     * @param {ILoginRequest=} [properties] Properties to set
     */
    function LoginRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * LoginRequest userName.
     * @member {string} userName
     * @memberof LoginRequest
     * @instance
     */
    LoginRequest.prototype.userName = "";

    /**
     * LoginRequest password.
     * @member {string} password
     * @memberof LoginRequest
     * @instance
     */
    LoginRequest.prototype.password = "";

    /**
     * Creates a new LoginRequest instance using the specified properties.
     * @function create
     * @memberof LoginRequest
     * @static
     * @param {ILoginRequest=} [properties] Properties to set
     * @returns {LoginRequest} LoginRequest instance
     */
    LoginRequest.create = function create(properties) {
        return new LoginRequest(properties);
    };

    /**
     * Encodes the specified LoginRequest message. Does not implicitly {@link LoginRequest.verify|verify} messages.
     * @function encode
     * @memberof LoginRequest
     * @static
     * @param {ILoginRequest} message LoginRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.userName != null && Object.hasOwnProperty.call(message, "userName"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.userName);
        if (message.password != null && Object.hasOwnProperty.call(message, "password"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.password);
        return writer;
    };

    /**
     * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link LoginRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof LoginRequest
     * @static
     * @param {ILoginRequest} message LoginRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a LoginRequest message from the specified reader or buffer.
     * @function decode
     * @memberof LoginRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {LoginRequest} LoginRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.LoginRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.userName = reader.string();
                    break;
                }
            case 2: {
                    message.password = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof LoginRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {LoginRequest} LoginRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a LoginRequest message.
     * @function verify
     * @memberof LoginRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    LoginRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.userName != null && message.hasOwnProperty("userName"))
            if (!$util.isString(message.userName))
                return "userName: string expected";
        if (message.password != null && message.hasOwnProperty("password"))
            if (!$util.isString(message.password))
                return "password: string expected";
        return null;
    };

    /**
     * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof LoginRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {LoginRequest} LoginRequest
     */
    LoginRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.LoginRequest)
            return object;
        let message = new $root.LoginRequest();
        if (object.userName != null)
            message.userName = String(object.userName);
        if (object.password != null)
            message.password = String(object.password);
        return message;
    };

    /**
     * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof LoginRequest
     * @static
     * @param {LoginRequest} message LoginRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    LoginRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.userName = "";
            object.password = "";
        }
        if (message.userName != null && message.hasOwnProperty("userName"))
            object.userName = message.userName;
        if (message.password != null && message.hasOwnProperty("password"))
            object.password = message.password;
        return object;
    };

    /**
     * Converts this LoginRequest to JSON.
     * @function toJSON
     * @memberof LoginRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    LoginRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for LoginRequest
     * @function getTypeUrl
     * @memberof LoginRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    LoginRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LoginRequest";
    };

    return LoginRequest;
})();

export const LoginResponse = $root.LoginResponse = (() => {

    /**
     * Properties of a LoginResponse.
     * @exports ILoginResponse
     * @interface ILoginResponse
     * @property {number|null} [errorCode] LoginResponse errorCode
     * @property {string|null} [userName] LoginResponse userName
     * @property {number|null} [gold] LoginResponse gold
     * @property {string|null} [nickName] LoginResponse nickName
     * @property {string|null} [avatar] LoginResponse avatar
     * @property {string|null} [currency] LoginResponse currency
     * @property {string|null} [userId] LoginResponse userId
     */

    /**
     * Constructs a new LoginResponse.
     * @exports LoginResponse
     * @classdesc Represents a LoginResponse.
     * @implements ILoginResponse
     * @constructor
     * @param {ILoginResponse=} [properties] Properties to set
     */
    function LoginResponse(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * LoginResponse errorCode.
     * @member {number} errorCode
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.errorCode = 0;

    /**
     * LoginResponse userName.
     * @member {string} userName
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.userName = "";

    /**
     * LoginResponse gold.
     * @member {number} gold
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.gold = 0;

    /**
     * LoginResponse nickName.
     * @member {string} nickName
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.nickName = "";

    /**
     * LoginResponse avatar.
     * @member {string} avatar
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.avatar = "";

    /**
     * LoginResponse currency.
     * @member {string} currency
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.currency = "";

    /**
     * LoginResponse userId.
     * @member {string} userId
     * @memberof LoginResponse
     * @instance
     */
    LoginResponse.prototype.userId = "";

    /**
     * Creates a new LoginResponse instance using the specified properties.
     * @function create
     * @memberof LoginResponse
     * @static
     * @param {ILoginResponse=} [properties] Properties to set
     * @returns {LoginResponse} LoginResponse instance
     */
    LoginResponse.create = function create(properties) {
        return new LoginResponse(properties);
    };

    /**
     * Encodes the specified LoginResponse message. Does not implicitly {@link LoginResponse.verify|verify} messages.
     * @function encode
     * @memberof LoginResponse
     * @static
     * @param {ILoginResponse} message LoginResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        if (message.userName != null && Object.hasOwnProperty.call(message, "userName"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.userName);
        if (message.gold != null && Object.hasOwnProperty.call(message, "gold"))
            writer.uint32(/* id 3, wireType 1 =*/25).double(message.gold);
        if (message.nickName != null && Object.hasOwnProperty.call(message, "nickName"))
            writer.uint32(/* id 4, wireType 2 =*/34).string(message.nickName);
        if (message.avatar != null && Object.hasOwnProperty.call(message, "avatar"))
            writer.uint32(/* id 5, wireType 2 =*/42).string(message.avatar);
        if (message.currency != null && Object.hasOwnProperty.call(message, "currency"))
            writer.uint32(/* id 6, wireType 2 =*/50).string(message.currency);
        if (message.userId != null && Object.hasOwnProperty.call(message, "userId"))
            writer.uint32(/* id 7, wireType 2 =*/58).string(message.userId);
        return writer;
    };

    /**
     * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link LoginResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof LoginResponse
     * @static
     * @param {ILoginResponse} message LoginResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a LoginResponse message from the specified reader or buffer.
     * @function decode
     * @memberof LoginResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {LoginResponse} LoginResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.LoginResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            case 2: {
                    message.userName = reader.string();
                    break;
                }
            case 3: {
                    message.gold = reader.double();
                    break;
                }
            case 4: {
                    message.nickName = reader.string();
                    break;
                }
            case 5: {
                    message.avatar = reader.string();
                    break;
                }
            case 6: {
                    message.currency = reader.string();
                    break;
                }
            case 7: {
                    message.userId = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof LoginResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {LoginResponse} LoginResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a LoginResponse message.
     * @function verify
     * @memberof LoginResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    LoginResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        if (message.userName != null && message.hasOwnProperty("userName"))
            if (!$util.isString(message.userName))
                return "userName: string expected";
        if (message.gold != null && message.hasOwnProperty("gold"))
            if (typeof message.gold !== "number")
                return "gold: number expected";
        if (message.nickName != null && message.hasOwnProperty("nickName"))
            if (!$util.isString(message.nickName))
                return "nickName: string expected";
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            if (!$util.isString(message.avatar))
                return "avatar: string expected";
        if (message.currency != null && message.hasOwnProperty("currency"))
            if (!$util.isString(message.currency))
                return "currency: string expected";
        if (message.userId != null && message.hasOwnProperty("userId"))
            if (!$util.isString(message.userId))
                return "userId: string expected";
        return null;
    };

    /**
     * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof LoginResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {LoginResponse} LoginResponse
     */
    LoginResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.LoginResponse)
            return object;
        let message = new $root.LoginResponse();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        if (object.userName != null)
            message.userName = String(object.userName);
        if (object.gold != null)
            message.gold = Number(object.gold);
        if (object.nickName != null)
            message.nickName = String(object.nickName);
        if (object.avatar != null)
            message.avatar = String(object.avatar);
        if (object.currency != null)
            message.currency = String(object.currency);
        if (object.userId != null)
            message.userId = String(object.userId);
        return message;
    };

    /**
     * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof LoginResponse
     * @static
     * @param {LoginResponse} message LoginResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    LoginResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.errorCode = 0;
            object.userName = "";
            object.gold = 0;
            object.nickName = "";
            object.avatar = "";
            object.currency = "";
            object.userId = "";
        }
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        if (message.userName != null && message.hasOwnProperty("userName"))
            object.userName = message.userName;
        if (message.gold != null && message.hasOwnProperty("gold"))
            object.gold = options.json && !isFinite(message.gold) ? String(message.gold) : message.gold;
        if (message.nickName != null && message.hasOwnProperty("nickName"))
            object.nickName = message.nickName;
        if (message.avatar != null && message.hasOwnProperty("avatar"))
            object.avatar = message.avatar;
        if (message.currency != null && message.hasOwnProperty("currency"))
            object.currency = message.currency;
        if (message.userId != null && message.hasOwnProperty("userId"))
            object.userId = message.userId;
        return object;
    };

    /**
     * Converts this LoginResponse to JSON.
     * @function toJSON
     * @memberof LoginResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    LoginResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for LoginResponse
     * @function getTypeUrl
     * @memberof LoginResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    LoginResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LoginResponse";
    };

    return LoginResponse;
})();

export const LoginTokenRequest = $root.LoginTokenRequest = (() => {

    /**
     * Properties of a LoginTokenRequest.
     * @exports ILoginTokenRequest
     * @interface ILoginTokenRequest
     * @property {string|null} [token] LoginTokenRequest token
     */

    /**
     * Constructs a new LoginTokenRequest.
     * @exports LoginTokenRequest
     * @classdesc Represents a LoginTokenRequest.
     * @implements ILoginTokenRequest
     * @constructor
     * @param {ILoginTokenRequest=} [properties] Properties to set
     */
    function LoginTokenRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * LoginTokenRequest token.
     * @member {string} token
     * @memberof LoginTokenRequest
     * @instance
     */
    LoginTokenRequest.prototype.token = "";

    /**
     * Creates a new LoginTokenRequest instance using the specified properties.
     * @function create
     * @memberof LoginTokenRequest
     * @static
     * @param {ILoginTokenRequest=} [properties] Properties to set
     * @returns {LoginTokenRequest} LoginTokenRequest instance
     */
    LoginTokenRequest.create = function create(properties) {
        return new LoginTokenRequest(properties);
    };

    /**
     * Encodes the specified LoginTokenRequest message. Does not implicitly {@link LoginTokenRequest.verify|verify} messages.
     * @function encode
     * @memberof LoginTokenRequest
     * @static
     * @param {ILoginTokenRequest} message LoginTokenRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginTokenRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.token != null && Object.hasOwnProperty.call(message, "token"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.token);
        return writer;
    };

    /**
     * Encodes the specified LoginTokenRequest message, length delimited. Does not implicitly {@link LoginTokenRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof LoginTokenRequest
     * @static
     * @param {ILoginTokenRequest} message LoginTokenRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    LoginTokenRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a LoginTokenRequest message from the specified reader or buffer.
     * @function decode
     * @memberof LoginTokenRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {LoginTokenRequest} LoginTokenRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginTokenRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.LoginTokenRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.token = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a LoginTokenRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof LoginTokenRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {LoginTokenRequest} LoginTokenRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    LoginTokenRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a LoginTokenRequest message.
     * @function verify
     * @memberof LoginTokenRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    LoginTokenRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.token != null && message.hasOwnProperty("token"))
            if (!$util.isString(message.token))
                return "token: string expected";
        return null;
    };

    /**
     * Creates a LoginTokenRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof LoginTokenRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {LoginTokenRequest} LoginTokenRequest
     */
    LoginTokenRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.LoginTokenRequest)
            return object;
        let message = new $root.LoginTokenRequest();
        if (object.token != null)
            message.token = String(object.token);
        return message;
    };

    /**
     * Creates a plain object from a LoginTokenRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof LoginTokenRequest
     * @static
     * @param {LoginTokenRequest} message LoginTokenRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    LoginTokenRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.token = "";
        if (message.token != null && message.hasOwnProperty("token"))
            object.token = message.token;
        return object;
    };

    /**
     * Converts this LoginTokenRequest to JSON.
     * @function toJSON
     * @memberof LoginTokenRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    LoginTokenRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for LoginTokenRequest
     * @function getTypeUrl
     * @memberof LoginTokenRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    LoginTokenRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LoginTokenRequest";
    };

    return LoginTokenRequest;
})();

export const loginConflictRequest = $root.loginConflictRequest = (() => {

    /**
     * Properties of a loginConflictRequest.
     * @exports IloginConflictRequest
     * @interface IloginConflictRequest
     * @property {number|Long|null} [sysTime] loginConflictRequest sysTime
     */

    /**
     * Constructs a new loginConflictRequest.
     * @exports loginConflictRequest
     * @classdesc Represents a loginConflictRequest.
     * @implements IloginConflictRequest
     * @constructor
     * @param {IloginConflictRequest=} [properties] Properties to set
     */
    function loginConflictRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * loginConflictRequest sysTime.
     * @member {number|Long} sysTime
     * @memberof loginConflictRequest
     * @instance
     */
    loginConflictRequest.prototype.sysTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * Creates a new loginConflictRequest instance using the specified properties.
     * @function create
     * @memberof loginConflictRequest
     * @static
     * @param {IloginConflictRequest=} [properties] Properties to set
     * @returns {loginConflictRequest} loginConflictRequest instance
     */
    loginConflictRequest.create = function create(properties) {
        return new loginConflictRequest(properties);
    };

    /**
     * Encodes the specified loginConflictRequest message. Does not implicitly {@link loginConflictRequest.verify|verify} messages.
     * @function encode
     * @memberof loginConflictRequest
     * @static
     * @param {IloginConflictRequest} message loginConflictRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    loginConflictRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.sysTime != null && Object.hasOwnProperty.call(message, "sysTime"))
            writer.uint32(/* id 1, wireType 0 =*/8).int64(message.sysTime);
        return writer;
    };

    /**
     * Encodes the specified loginConflictRequest message, length delimited. Does not implicitly {@link loginConflictRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof loginConflictRequest
     * @static
     * @param {IloginConflictRequest} message loginConflictRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    loginConflictRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a loginConflictRequest message from the specified reader or buffer.
     * @function decode
     * @memberof loginConflictRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {loginConflictRequest} loginConflictRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    loginConflictRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.loginConflictRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.sysTime = reader.int64();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a loginConflictRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof loginConflictRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {loginConflictRequest} loginConflictRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    loginConflictRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a loginConflictRequest message.
     * @function verify
     * @memberof loginConflictRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    loginConflictRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (!$util.isInteger(message.sysTime) && !(message.sysTime && $util.isInteger(message.sysTime.low) && $util.isInteger(message.sysTime.high)))
                return "sysTime: integer|Long expected";
        return null;
    };

    /**
     * Creates a loginConflictRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof loginConflictRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {loginConflictRequest} loginConflictRequest
     */
    loginConflictRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.loginConflictRequest)
            return object;
        let message = new $root.loginConflictRequest();
        if (object.sysTime != null)
            if ($util.Long)
                (message.sysTime = $util.Long.fromValue(object.sysTime)).unsigned = false;
            else if (typeof object.sysTime === "string")
                message.sysTime = parseInt(object.sysTime, 10);
            else if (typeof object.sysTime === "number")
                message.sysTime = object.sysTime;
            else if (typeof object.sysTime === "object")
                message.sysTime = new $util.LongBits(object.sysTime.low >>> 0, object.sysTime.high >>> 0).toNumber();
        return message;
    };

    /**
     * Creates a plain object from a loginConflictRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof loginConflictRequest
     * @static
     * @param {loginConflictRequest} message loginConflictRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    loginConflictRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.sysTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.sysTime = options.longs === String ? "0" : 0;
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (typeof message.sysTime === "number")
                object.sysTime = options.longs === String ? String(message.sysTime) : message.sysTime;
            else
                object.sysTime = options.longs === String ? $util.Long.prototype.toString.call(message.sysTime) : options.longs === Number ? new $util.LongBits(message.sysTime.low >>> 0, message.sysTime.high >>> 0).toNumber() : message.sysTime;
        return object;
    };

    /**
     * Converts this loginConflictRequest to JSON.
     * @function toJSON
     * @memberof loginConflictRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    loginConflictRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for loginConflictRequest
     * @function getTypeUrl
     * @memberof loginConflictRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    loginConflictRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/loginConflictRequest";
    };

    return loginConflictRequest;
})();

export const SessionTerminatedRequest = $root.SessionTerminatedRequest = (() => {

    /**
     * Properties of a SessionTerminatedRequest.
     * @exports ISessionTerminatedRequest
     * @interface ISessionTerminatedRequest
     * @property {number|Long|null} [sysTime] SessionTerminatedRequest sysTime
     */

    /**
     * Constructs a new SessionTerminatedRequest.
     * @exports SessionTerminatedRequest
     * @classdesc Represents a SessionTerminatedRequest.
     * @implements ISessionTerminatedRequest
     * @constructor
     * @param {ISessionTerminatedRequest=} [properties] Properties to set
     */
    function SessionTerminatedRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * SessionTerminatedRequest sysTime.
     * @member {number|Long} sysTime
     * @memberof SessionTerminatedRequest
     * @instance
     */
    SessionTerminatedRequest.prototype.sysTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * Creates a new SessionTerminatedRequest instance using the specified properties.
     * @function create
     * @memberof SessionTerminatedRequest
     * @static
     * @param {ISessionTerminatedRequest=} [properties] Properties to set
     * @returns {SessionTerminatedRequest} SessionTerminatedRequest instance
     */
    SessionTerminatedRequest.create = function create(properties) {
        return new SessionTerminatedRequest(properties);
    };

    /**
     * Encodes the specified SessionTerminatedRequest message. Does not implicitly {@link SessionTerminatedRequest.verify|verify} messages.
     * @function encode
     * @memberof SessionTerminatedRequest
     * @static
     * @param {ISessionTerminatedRequest} message SessionTerminatedRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    SessionTerminatedRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.sysTime != null && Object.hasOwnProperty.call(message, "sysTime"))
            writer.uint32(/* id 1, wireType 0 =*/8).int64(message.sysTime);
        return writer;
    };

    /**
     * Encodes the specified SessionTerminatedRequest message, length delimited. Does not implicitly {@link SessionTerminatedRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof SessionTerminatedRequest
     * @static
     * @param {ISessionTerminatedRequest} message SessionTerminatedRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    SessionTerminatedRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a SessionTerminatedRequest message from the specified reader or buffer.
     * @function decode
     * @memberof SessionTerminatedRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {SessionTerminatedRequest} SessionTerminatedRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    SessionTerminatedRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.SessionTerminatedRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.sysTime = reader.int64();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a SessionTerminatedRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof SessionTerminatedRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {SessionTerminatedRequest} SessionTerminatedRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    SessionTerminatedRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a SessionTerminatedRequest message.
     * @function verify
     * @memberof SessionTerminatedRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    SessionTerminatedRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (!$util.isInteger(message.sysTime) && !(message.sysTime && $util.isInteger(message.sysTime.low) && $util.isInteger(message.sysTime.high)))
                return "sysTime: integer|Long expected";
        return null;
    };

    /**
     * Creates a SessionTerminatedRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof SessionTerminatedRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {SessionTerminatedRequest} SessionTerminatedRequest
     */
    SessionTerminatedRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.SessionTerminatedRequest)
            return object;
        let message = new $root.SessionTerminatedRequest();
        if (object.sysTime != null)
            if ($util.Long)
                (message.sysTime = $util.Long.fromValue(object.sysTime)).unsigned = false;
            else if (typeof object.sysTime === "string")
                message.sysTime = parseInt(object.sysTime, 10);
            else if (typeof object.sysTime === "number")
                message.sysTime = object.sysTime;
            else if (typeof object.sysTime === "object")
                message.sysTime = new $util.LongBits(object.sysTime.low >>> 0, object.sysTime.high >>> 0).toNumber();
        return message;
    };

    /**
     * Creates a plain object from a SessionTerminatedRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof SessionTerminatedRequest
     * @static
     * @param {SessionTerminatedRequest} message SessionTerminatedRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    SessionTerminatedRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.sysTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.sysTime = options.longs === String ? "0" : 0;
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (typeof message.sysTime === "number")
                object.sysTime = options.longs === String ? String(message.sysTime) : message.sysTime;
            else
                object.sysTime = options.longs === String ? $util.Long.prototype.toString.call(message.sysTime) : options.longs === Number ? new $util.LongBits(message.sysTime.low >>> 0, message.sysTime.high >>> 0).toNumber() : message.sysTime;
        return object;
    };

    /**
     * Converts this SessionTerminatedRequest to JSON.
     * @function toJSON
     * @memberof SessionTerminatedRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    SessionTerminatedRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for SessionTerminatedRequest
     * @function getTypeUrl
     * @memberof SessionTerminatedRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    SessionTerminatedRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/SessionTerminatedRequest";
    };

    return SessionTerminatedRequest;
})();

export const OfflineRequest = $root.OfflineRequest = (() => {

    /**
     * Properties of an OfflineRequest.
     * @exports IOfflineRequest
     * @interface IOfflineRequest
     * @property {string|null} [userId] OfflineRequest userId
     * @property {string|null} [channelId] OfflineRequest channelId
     */

    /**
     * Constructs a new OfflineRequest.
     * @exports OfflineRequest
     * @classdesc Represents an OfflineRequest.
     * @implements IOfflineRequest
     * @constructor
     * @param {IOfflineRequest=} [properties] Properties to set
     */
    function OfflineRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * OfflineRequest userId.
     * @member {string} userId
     * @memberof OfflineRequest
     * @instance
     */
    OfflineRequest.prototype.userId = "";

    /**
     * OfflineRequest channelId.
     * @member {string} channelId
     * @memberof OfflineRequest
     * @instance
     */
    OfflineRequest.prototype.channelId = "";

    /**
     * Creates a new OfflineRequest instance using the specified properties.
     * @function create
     * @memberof OfflineRequest
     * @static
     * @param {IOfflineRequest=} [properties] Properties to set
     * @returns {OfflineRequest} OfflineRequest instance
     */
    OfflineRequest.create = function create(properties) {
        return new OfflineRequest(properties);
    };

    /**
     * Encodes the specified OfflineRequest message. Does not implicitly {@link OfflineRequest.verify|verify} messages.
     * @function encode
     * @memberof OfflineRequest
     * @static
     * @param {IOfflineRequest} message OfflineRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    OfflineRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.userId != null && Object.hasOwnProperty.call(message, "userId"))
            writer.uint32(/* id 1, wireType 2 =*/10).string(message.userId);
        if (message.channelId != null && Object.hasOwnProperty.call(message, "channelId"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.channelId);
        return writer;
    };

    /**
     * Encodes the specified OfflineRequest message, length delimited. Does not implicitly {@link OfflineRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof OfflineRequest
     * @static
     * @param {IOfflineRequest} message OfflineRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    OfflineRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes an OfflineRequest message from the specified reader or buffer.
     * @function decode
     * @memberof OfflineRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {OfflineRequest} OfflineRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    OfflineRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.OfflineRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.userId = reader.string();
                    break;
                }
            case 2: {
                    message.channelId = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes an OfflineRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof OfflineRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {OfflineRequest} OfflineRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    OfflineRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies an OfflineRequest message.
     * @function verify
     * @memberof OfflineRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    OfflineRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.userId != null && message.hasOwnProperty("userId"))
            if (!$util.isString(message.userId))
                return "userId: string expected";
        if (message.channelId != null && message.hasOwnProperty("channelId"))
            if (!$util.isString(message.channelId))
                return "channelId: string expected";
        return null;
    };

    /**
     * Creates an OfflineRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof OfflineRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {OfflineRequest} OfflineRequest
     */
    OfflineRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.OfflineRequest)
            return object;
        let message = new $root.OfflineRequest();
        if (object.userId != null)
            message.userId = String(object.userId);
        if (object.channelId != null)
            message.channelId = String(object.channelId);
        return message;
    };

    /**
     * Creates a plain object from an OfflineRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof OfflineRequest
     * @static
     * @param {OfflineRequest} message OfflineRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    OfflineRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.userId = "";
            object.channelId = "";
        }
        if (message.userId != null && message.hasOwnProperty("userId"))
            object.userId = message.userId;
        if (message.channelId != null && message.hasOwnProperty("channelId"))
            object.channelId = message.channelId;
        return object;
    };

    /**
     * Converts this OfflineRequest to JSON.
     * @function toJSON
     * @memberof OfflineRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    OfflineRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for OfflineRequest
     * @function getTypeUrl
     * @memberof OfflineRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    OfflineRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/OfflineRequest";
    };

    return OfflineRequest;
})();

export const HeartbeatRequest = $root.HeartbeatRequest = (() => {

    /**
     * Properties of a HeartbeatRequest.
     * @exports IHeartbeatRequest
     * @interface IHeartbeatRequest
     * @property {number|Long|null} [sysTime] HeartbeatRequest sysTime
     */

    /**
     * Constructs a new HeartbeatRequest.
     * @exports HeartbeatRequest
     * @classdesc 心跳
     * @implements IHeartbeatRequest
     * @constructor
     * @param {IHeartbeatRequest=} [properties] Properties to set
     */
    function HeartbeatRequest(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * HeartbeatRequest sysTime.
     * @member {number|Long} sysTime
     * @memberof HeartbeatRequest
     * @instance
     */
    HeartbeatRequest.prototype.sysTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * Creates a new HeartbeatRequest instance using the specified properties.
     * @function create
     * @memberof HeartbeatRequest
     * @static
     * @param {IHeartbeatRequest=} [properties] Properties to set
     * @returns {HeartbeatRequest} HeartbeatRequest instance
     */
    HeartbeatRequest.create = function create(properties) {
        return new HeartbeatRequest(properties);
    };

    /**
     * Encodes the specified HeartbeatRequest message. Does not implicitly {@link HeartbeatRequest.verify|verify} messages.
     * @function encode
     * @memberof HeartbeatRequest
     * @static
     * @param {IHeartbeatRequest} message HeartbeatRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    HeartbeatRequest.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.sysTime != null && Object.hasOwnProperty.call(message, "sysTime"))
            writer.uint32(/* id 1, wireType 0 =*/8).int64(message.sysTime);
        return writer;
    };

    /**
     * Encodes the specified HeartbeatRequest message, length delimited. Does not implicitly {@link HeartbeatRequest.verify|verify} messages.
     * @function encodeDelimited
     * @memberof HeartbeatRequest
     * @static
     * @param {IHeartbeatRequest} message HeartbeatRequest message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    HeartbeatRequest.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a HeartbeatRequest message from the specified reader or buffer.
     * @function decode
     * @memberof HeartbeatRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {HeartbeatRequest} HeartbeatRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    HeartbeatRequest.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.HeartbeatRequest();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.sysTime = reader.int64();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a HeartbeatRequest message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof HeartbeatRequest
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {HeartbeatRequest} HeartbeatRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    HeartbeatRequest.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a HeartbeatRequest message.
     * @function verify
     * @memberof HeartbeatRequest
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    HeartbeatRequest.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (!$util.isInteger(message.sysTime) && !(message.sysTime && $util.isInteger(message.sysTime.low) && $util.isInteger(message.sysTime.high)))
                return "sysTime: integer|Long expected";
        return null;
    };

    /**
     * Creates a HeartbeatRequest message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof HeartbeatRequest
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {HeartbeatRequest} HeartbeatRequest
     */
    HeartbeatRequest.fromObject = function fromObject(object) {
        if (object instanceof $root.HeartbeatRequest)
            return object;
        let message = new $root.HeartbeatRequest();
        if (object.sysTime != null)
            if ($util.Long)
                (message.sysTime = $util.Long.fromValue(object.sysTime)).unsigned = false;
            else if (typeof object.sysTime === "string")
                message.sysTime = parseInt(object.sysTime, 10);
            else if (typeof object.sysTime === "number")
                message.sysTime = object.sysTime;
            else if (typeof object.sysTime === "object")
                message.sysTime = new $util.LongBits(object.sysTime.low >>> 0, object.sysTime.high >>> 0).toNumber();
        return message;
    };

    /**
     * Creates a plain object from a HeartbeatRequest message. Also converts values to other types if specified.
     * @function toObject
     * @memberof HeartbeatRequest
     * @static
     * @param {HeartbeatRequest} message HeartbeatRequest
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    HeartbeatRequest.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.sysTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.sysTime = options.longs === String ? "0" : 0;
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (typeof message.sysTime === "number")
                object.sysTime = options.longs === String ? String(message.sysTime) : message.sysTime;
            else
                object.sysTime = options.longs === String ? $util.Long.prototype.toString.call(message.sysTime) : options.longs === Number ? new $util.LongBits(message.sysTime.low >>> 0, message.sysTime.high >>> 0).toNumber() : message.sysTime;
        return object;
    };

    /**
     * Converts this HeartbeatRequest to JSON.
     * @function toJSON
     * @memberof HeartbeatRequest
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    HeartbeatRequest.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for HeartbeatRequest
     * @function getTypeUrl
     * @memberof HeartbeatRequest
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    HeartbeatRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/HeartbeatRequest";
    };

    return HeartbeatRequest;
})();

export const HeartbeatResponse = $root.HeartbeatResponse = (() => {

    /**
     * Properties of a HeartbeatResponse.
     * @exports IHeartbeatResponse
     * @interface IHeartbeatResponse
     * @property {number|Long|null} [sysTime] HeartbeatResponse sysTime
     */

    /**
     * Constructs a new HeartbeatResponse.
     * @exports HeartbeatResponse
     * @classdesc Represents a HeartbeatResponse.
     * @implements IHeartbeatResponse
     * @constructor
     * @param {IHeartbeatResponse=} [properties] Properties to set
     */
    function HeartbeatResponse(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * HeartbeatResponse sysTime.
     * @member {number|Long} sysTime
     * @memberof HeartbeatResponse
     * @instance
     */
    HeartbeatResponse.prototype.sysTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    /**
     * Creates a new HeartbeatResponse instance using the specified properties.
     * @function create
     * @memberof HeartbeatResponse
     * @static
     * @param {IHeartbeatResponse=} [properties] Properties to set
     * @returns {HeartbeatResponse} HeartbeatResponse instance
     */
    HeartbeatResponse.create = function create(properties) {
        return new HeartbeatResponse(properties);
    };

    /**
     * Encodes the specified HeartbeatResponse message. Does not implicitly {@link HeartbeatResponse.verify|verify} messages.
     * @function encode
     * @memberof HeartbeatResponse
     * @static
     * @param {IHeartbeatResponse} message HeartbeatResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    HeartbeatResponse.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.sysTime != null && Object.hasOwnProperty.call(message, "sysTime"))
            writer.uint32(/* id 1, wireType 0 =*/8).int64(message.sysTime);
        return writer;
    };

    /**
     * Encodes the specified HeartbeatResponse message, length delimited. Does not implicitly {@link HeartbeatResponse.verify|verify} messages.
     * @function encodeDelimited
     * @memberof HeartbeatResponse
     * @static
     * @param {IHeartbeatResponse} message HeartbeatResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    HeartbeatResponse.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a HeartbeatResponse message from the specified reader or buffer.
     * @function decode
     * @memberof HeartbeatResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {HeartbeatResponse} HeartbeatResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    HeartbeatResponse.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.HeartbeatResponse();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.sysTime = reader.int64();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a HeartbeatResponse message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof HeartbeatResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {HeartbeatResponse} HeartbeatResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    HeartbeatResponse.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a HeartbeatResponse message.
     * @function verify
     * @memberof HeartbeatResponse
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    HeartbeatResponse.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (!$util.isInteger(message.sysTime) && !(message.sysTime && $util.isInteger(message.sysTime.low) && $util.isInteger(message.sysTime.high)))
                return "sysTime: integer|Long expected";
        return null;
    };

    /**
     * Creates a HeartbeatResponse message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof HeartbeatResponse
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {HeartbeatResponse} HeartbeatResponse
     */
    HeartbeatResponse.fromObject = function fromObject(object) {
        if (object instanceof $root.HeartbeatResponse)
            return object;
        let message = new $root.HeartbeatResponse();
        if (object.sysTime != null)
            if ($util.Long)
                (message.sysTime = $util.Long.fromValue(object.sysTime)).unsigned = false;
            else if (typeof object.sysTime === "string")
                message.sysTime = parseInt(object.sysTime, 10);
            else if (typeof object.sysTime === "number")
                message.sysTime = object.sysTime;
            else if (typeof object.sysTime === "object")
                message.sysTime = new $util.LongBits(object.sysTime.low >>> 0, object.sysTime.high >>> 0).toNumber();
        return message;
    };

    /**
     * Creates a plain object from a HeartbeatResponse message. Also converts values to other types if specified.
     * @function toObject
     * @memberof HeartbeatResponse
     * @static
     * @param {HeartbeatResponse} message HeartbeatResponse
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    HeartbeatResponse.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            if ($util.Long) {
                let long = new $util.Long(0, 0, false);
                object.sysTime = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
            } else
                object.sysTime = options.longs === String ? "0" : 0;
        if (message.sysTime != null && message.hasOwnProperty("sysTime"))
            if (typeof message.sysTime === "number")
                object.sysTime = options.longs === String ? String(message.sysTime) : message.sysTime;
            else
                object.sysTime = options.longs === String ? $util.Long.prototype.toString.call(message.sysTime) : options.longs === Number ? new $util.LongBits(message.sysTime.low >>> 0, message.sysTime.high >>> 0).toNumber() : message.sysTime;
        return object;
    };

    /**
     * Converts this HeartbeatResponse to JSON.
     * @function toJSON
     * @memberof HeartbeatResponse
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    HeartbeatResponse.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for HeartbeatResponse
     * @function getTypeUrl
     * @memberof HeartbeatResponse
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    HeartbeatResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/HeartbeatResponse";
    };

    return HeartbeatResponse;
})();

export const DefaultErrorCode = $root.DefaultErrorCode = (() => {

    /**
     * Properties of a DefaultErrorCode.
     * @exports IDefaultErrorCode
     * @interface IDefaultErrorCode
     * @property {number|null} [errorCode] DefaultErrorCode errorCode
     */

    /**
     * Constructs a new DefaultErrorCode.
     * @exports DefaultErrorCode
     * @classdesc Represents a DefaultErrorCode.
     * @implements IDefaultErrorCode
     * @constructor
     * @param {IDefaultErrorCode=} [properties] Properties to set
     */
    function DefaultErrorCode(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * DefaultErrorCode errorCode.
     * @member {number} errorCode
     * @memberof DefaultErrorCode
     * @instance
     */
    DefaultErrorCode.prototype.errorCode = 0;

    /**
     * Creates a new DefaultErrorCode instance using the specified properties.
     * @function create
     * @memberof DefaultErrorCode
     * @static
     * @param {IDefaultErrorCode=} [properties] Properties to set
     * @returns {DefaultErrorCode} DefaultErrorCode instance
     */
    DefaultErrorCode.create = function create(properties) {
        return new DefaultErrorCode(properties);
    };

    /**
     * Encodes the specified DefaultErrorCode message. Does not implicitly {@link DefaultErrorCode.verify|verify} messages.
     * @function encode
     * @memberof DefaultErrorCode
     * @static
     * @param {IDefaultErrorCode} message DefaultErrorCode message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DefaultErrorCode.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.errorCode != null && Object.hasOwnProperty.call(message, "errorCode"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.errorCode);
        return writer;
    };

    /**
     * Encodes the specified DefaultErrorCode message, length delimited. Does not implicitly {@link DefaultErrorCode.verify|verify} messages.
     * @function encodeDelimited
     * @memberof DefaultErrorCode
     * @static
     * @param {IDefaultErrorCode} message DefaultErrorCode message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DefaultErrorCode.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a DefaultErrorCode message from the specified reader or buffer.
     * @function decode
     * @memberof DefaultErrorCode
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {DefaultErrorCode} DefaultErrorCode
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DefaultErrorCode.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.DefaultErrorCode();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.errorCode = reader.int32();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a DefaultErrorCode message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof DefaultErrorCode
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {DefaultErrorCode} DefaultErrorCode
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DefaultErrorCode.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a DefaultErrorCode message.
     * @function verify
     * @memberof DefaultErrorCode
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    DefaultErrorCode.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            if (!$util.isInteger(message.errorCode))
                return "errorCode: integer expected";
        return null;
    };

    /**
     * Creates a DefaultErrorCode message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof DefaultErrorCode
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {DefaultErrorCode} DefaultErrorCode
     */
    DefaultErrorCode.fromObject = function fromObject(object) {
        if (object instanceof $root.DefaultErrorCode)
            return object;
        let message = new $root.DefaultErrorCode();
        if (object.errorCode != null)
            message.errorCode = object.errorCode | 0;
        return message;
    };

    /**
     * Creates a plain object from a DefaultErrorCode message. Also converts values to other types if specified.
     * @function toObject
     * @memberof DefaultErrorCode
     * @static
     * @param {DefaultErrorCode} message DefaultErrorCode
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    DefaultErrorCode.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults)
            object.errorCode = 0;
        if (message.errorCode != null && message.hasOwnProperty("errorCode"))
            object.errorCode = message.errorCode;
        return object;
    };

    /**
     * Converts this DefaultErrorCode to JSON.
     * @function toJSON
     * @memberof DefaultErrorCode
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    DefaultErrorCode.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for DefaultErrorCode
     * @function getTypeUrl
     * @memberof DefaultErrorCode
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    DefaultErrorCode.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/DefaultErrorCode";
    };

    return DefaultErrorCode;
})();

export const ServerErrorBody = $root.ServerErrorBody = (() => {

    /**
     * Properties of a ServerErrorBody.
     * @exports IServerErrorBody
     * @interface IServerErrorBody
     * @property {number|null} [code] ServerErrorBody code
     * @property {string|null} [message] ServerErrorBody message
     */

    /**
     * Constructs a new ServerErrorBody.
     * @exports ServerErrorBody
     * @classdesc Represents a ServerErrorBody.
     * @implements IServerErrorBody
     * @constructor
     * @param {IServerErrorBody=} [properties] Properties to set
     */
    function ServerErrorBody(properties) {
        if (properties)
            for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                if (properties[keys[i]] != null)
                    this[keys[i]] = properties[keys[i]];
    }

    /**
     * ServerErrorBody code.
     * @member {number} code
     * @memberof ServerErrorBody
     * @instance
     */
    ServerErrorBody.prototype.code = 0;

    /**
     * ServerErrorBody message.
     * @member {string} message
     * @memberof ServerErrorBody
     * @instance
     */
    ServerErrorBody.prototype.message = "";

    /**
     * Creates a new ServerErrorBody instance using the specified properties.
     * @function create
     * @memberof ServerErrorBody
     * @static
     * @param {IServerErrorBody=} [properties] Properties to set
     * @returns {ServerErrorBody} ServerErrorBody instance
     */
    ServerErrorBody.create = function create(properties) {
        return new ServerErrorBody(properties);
    };

    /**
     * Encodes the specified ServerErrorBody message. Does not implicitly {@link ServerErrorBody.verify|verify} messages.
     * @function encode
     * @memberof ServerErrorBody
     * @static
     * @param {IServerErrorBody} message ServerErrorBody message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ServerErrorBody.encode = function encode(message, writer) {
        if (!writer)
            writer = $Writer.create();
        if (message.code != null && Object.hasOwnProperty.call(message, "code"))
            writer.uint32(/* id 1, wireType 0 =*/8).int32(message.code);
        if (message.message != null && Object.hasOwnProperty.call(message, "message"))
            writer.uint32(/* id 2, wireType 2 =*/18).string(message.message);
        return writer;
    };

    /**
     * Encodes the specified ServerErrorBody message, length delimited. Does not implicitly {@link ServerErrorBody.verify|verify} messages.
     * @function encodeDelimited
     * @memberof ServerErrorBody
     * @static
     * @param {IServerErrorBody} message ServerErrorBody message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ServerErrorBody.encodeDelimited = function encodeDelimited(message, writer) {
        return this.encode(message, writer).ldelim();
    };

    /**
     * Decodes a ServerErrorBody message from the specified reader or buffer.
     * @function decode
     * @memberof ServerErrorBody
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {ServerErrorBody} ServerErrorBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ServerErrorBody.decode = function decode(reader, length, error) {
        if (!(reader instanceof $Reader))
            reader = $Reader.create(reader);
        let end = length === undefined ? reader.len : reader.pos + length, message = new $root.ServerErrorBody();
        while (reader.pos < end) {
            let tag = reader.uint32();
            if (tag === error)
                break;
            switch (tag >>> 3) {
            case 1: {
                    message.code = reader.int32();
                    break;
                }
            case 2: {
                    message.message = reader.string();
                    break;
                }
            default:
                reader.skipType(tag & 7);
                break;
            }
        }
        return message;
    };

    /**
     * Decodes a ServerErrorBody message from the specified reader or buffer, length delimited.
     * @function decodeDelimited
     * @memberof ServerErrorBody
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @returns {ServerErrorBody} ServerErrorBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ServerErrorBody.decodeDelimited = function decodeDelimited(reader) {
        if (!(reader instanceof $Reader))
            reader = new $Reader(reader);
        return this.decode(reader, reader.uint32());
    };

    /**
     * Verifies a ServerErrorBody message.
     * @function verify
     * @memberof ServerErrorBody
     * @static
     * @param {Object.<string,*>} message Plain object to verify
     * @returns {string|null} `null` if valid, otherwise the reason why it is not
     */
    ServerErrorBody.verify = function verify(message) {
        if (typeof message !== "object" || message === null)
            return "object expected";
        if (message.code != null && message.hasOwnProperty("code"))
            if (!$util.isInteger(message.code))
                return "code: integer expected";
        if (message.message != null && message.hasOwnProperty("message"))
            if (!$util.isString(message.message))
                return "message: string expected";
        return null;
    };

    /**
     * Creates a ServerErrorBody message from a plain object. Also converts values to their respective internal types.
     * @function fromObject
     * @memberof ServerErrorBody
     * @static
     * @param {Object.<string,*>} object Plain object
     * @returns {ServerErrorBody} ServerErrorBody
     */
    ServerErrorBody.fromObject = function fromObject(object) {
        if (object instanceof $root.ServerErrorBody)
            return object;
        let message = new $root.ServerErrorBody();
        if (object.code != null)
            message.code = object.code | 0;
        if (object.message != null)
            message.message = String(object.message);
        return message;
    };

    /**
     * Creates a plain object from a ServerErrorBody message. Also converts values to other types if specified.
     * @function toObject
     * @memberof ServerErrorBody
     * @static
     * @param {ServerErrorBody} message ServerErrorBody
     * @param {$protobuf.IConversionOptions} [options] Conversion options
     * @returns {Object.<string,*>} Plain object
     */
    ServerErrorBody.toObject = function toObject(message, options) {
        if (!options)
            options = {};
        let object = {};
        if (options.defaults) {
            object.code = 0;
            object.message = "";
        }
        if (message.code != null && message.hasOwnProperty("code"))
            object.code = message.code;
        if (message.message != null && message.hasOwnProperty("message"))
            object.message = message.message;
        return object;
    };

    /**
     * Converts this ServerErrorBody to JSON.
     * @function toJSON
     * @memberof ServerErrorBody
     * @instance
     * @returns {Object.<string,*>} JSON object
     */
    ServerErrorBody.prototype.toJSON = function toJSON() {
        return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
    };

    /**
     * Gets the default type url for ServerErrorBody
     * @function getTypeUrl
     * @memberof ServerErrorBody
     * @static
     * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns {string} The default type url
     */
    ServerErrorBody.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ServerErrorBody";
    };

    return ServerErrorBody;
})();

export { $root as default };
