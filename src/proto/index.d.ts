import * as $protobuf from "protobufjs";
import Long = require("long");
/** ServerType enum. */
export enum ServerType {
    NoN = 0,
    Crash = 1000,
    FortuneGems = 1001,
    FortuneGems1 = 1002,
    FortuneGems2 = 1003,
    SuperAce = 1004,
    CrazySeven = 1005,
    LegacyOfEgyptSeven = 1007,
    MoneyPot = 1008,
    FruityWheel = 1009,
    Aviator = 1010,
    MoneyComing = 1011,
    mahjong = 1012,
    BoxingKing = 1013,
    WinGo = 1014,
    SevenSevenSeven = 1015,
    ChargeBuffalo3 = 1016,
    THREE_COIN_TREASURES = 1017,
    SuperRich = 1018,
    CHICKEN_ROAD = 1019,
    CHICKEN_ROAD2 = 1020,
    POSEIDON = 1021,
    TWIST = 1022,
    WinGo2 = 1023
}

/** Represents an EnterRoomRequest. */
export class EnterRoomRequest implements IEnterRoomRequest {

    /**
     * Constructs a new EnterRoomRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IEnterRoomRequest);

    /**
     * Creates a new EnterRoomRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns EnterRoomRequest instance
     */
    public static create(properties?: IEnterRoomRequest): EnterRoomRequest;

    /**
     * Encodes the specified EnterRoomRequest message. Does not implicitly {@link EnterRoomRequest.verify|verify} messages.
     * @param message EnterRoomRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IEnterRoomRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified EnterRoomRequest message, length delimited. Does not implicitly {@link EnterRoomRequest.verify|verify} messages.
     * @param message EnterRoomRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IEnterRoomRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an EnterRoomRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns EnterRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): EnterRoomRequest;

    /**
     * Decodes an EnterRoomRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns EnterRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): EnterRoomRequest;

    /**
     * Verifies an EnterRoomRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an EnterRoomRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns EnterRoomRequest
     */
    public static fromObject(object: { [k: string]: any }): EnterRoomRequest;

    /**
     * Creates a plain object from an EnterRoomRequest message. Also converts values to other types if specified.
     * @param message EnterRoomRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: EnterRoomRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this EnterRoomRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for EnterRoomRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents an EnterRoomResponse. */
export class EnterRoomResponse implements IEnterRoomResponse {

    /**
     * Constructs a new EnterRoomResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IEnterRoomResponse);

    /** EnterRoomResponse errorCode. */
    public errorCode: number;

    /** EnterRoomResponse steps. */
    public steps: number[];

    /** EnterRoomResponse betAmount. */
    public betAmount: number;

    /** EnterRoomResponse canCashOut. */
    public canCashOut: boolean;

    /** EnterRoomResponse minBetAmount. */
    public minBetAmount: number;

    /** EnterRoomResponse maxBetAmount. */
    public maxBetAmount: number;

    /** EnterRoomResponse defaultBetAmount. */
    public defaultBetAmount: number;

    /** EnterRoomResponse maxWinAmount. */
    public maxWinAmount: number;

    /** EnterRoomResponse multiples_1. */
    public multiples_1: number[];

    /** EnterRoomResponse multiples_2. */
    public multiples_2: number[];

    /** EnterRoomResponse multiples_3. */
    public multiples_3: number[];

    /** EnterRoomResponse avatar. */
    public avatar: string;

    /** EnterRoomResponse nickname. */
    public nickname: string;

    /** EnterRoomResponse userGold. */
    public userGold: number;

    /** EnterRoomResponse clientSeed. */
    public clientSeed: string;

    /** EnterRoomResponse serverSeed. */
    public serverSeed: string;

    /**
     * Creates a new EnterRoomResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns EnterRoomResponse instance
     */
    public static create(properties?: IEnterRoomResponse): EnterRoomResponse;

    /**
     * Encodes the specified EnterRoomResponse message. Does not implicitly {@link EnterRoomResponse.verify|verify} messages.
     * @param message EnterRoomResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IEnterRoomResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified EnterRoomResponse message, length delimited. Does not implicitly {@link EnterRoomResponse.verify|verify} messages.
     * @param message EnterRoomResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IEnterRoomResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an EnterRoomResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns EnterRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): EnterRoomResponse;

    /**
     * Decodes an EnterRoomResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns EnterRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): EnterRoomResponse;

    /**
     * Verifies an EnterRoomResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an EnterRoomResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns EnterRoomResponse
     */
    public static fromObject(object: { [k: string]: any }): EnterRoomResponse;

    /**
     * Creates a plain object from an EnterRoomResponse message. Also converts values to other types if specified.
     * @param message EnterRoomResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: EnterRoomResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this EnterRoomResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for EnterRoomResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents an ExitRoomRequest. */
export class ExitRoomRequest implements IExitRoomRequest {

    /**
     * Constructs a new ExitRoomRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IExitRoomRequest);

    /**
     * Creates a new ExitRoomRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ExitRoomRequest instance
     */
    public static create(properties?: IExitRoomRequest): ExitRoomRequest;

    /**
     * Encodes the specified ExitRoomRequest message. Does not implicitly {@link ExitRoomRequest.verify|verify} messages.
     * @param message ExitRoomRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IExitRoomRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ExitRoomRequest message, length delimited. Does not implicitly {@link ExitRoomRequest.verify|verify} messages.
     * @param message ExitRoomRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IExitRoomRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an ExitRoomRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ExitRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ExitRoomRequest;

    /**
     * Decodes an ExitRoomRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ExitRoomRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ExitRoomRequest;

    /**
     * Verifies an ExitRoomRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an ExitRoomRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ExitRoomRequest
     */
    public static fromObject(object: { [k: string]: any }): ExitRoomRequest;

    /**
     * Creates a plain object from an ExitRoomRequest message. Also converts values to other types if specified.
     * @param message ExitRoomRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ExitRoomRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ExitRoomRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ExitRoomRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents an ExitRoomResponse. */
export class ExitRoomResponse implements IExitRoomResponse {

    /**
     * Constructs a new ExitRoomResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IExitRoomResponse);

    /** ExitRoomResponse errorCode. */
    public errorCode: number;

    /** ExitRoomResponse userGold. */
    public userGold: number;

    /**
     * Creates a new ExitRoomResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ExitRoomResponse instance
     */
    public static create(properties?: IExitRoomResponse): ExitRoomResponse;

    /**
     * Encodes the specified ExitRoomResponse message. Does not implicitly {@link ExitRoomResponse.verify|verify} messages.
     * @param message ExitRoomResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IExitRoomResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ExitRoomResponse message, length delimited. Does not implicitly {@link ExitRoomResponse.verify|verify} messages.
     * @param message ExitRoomResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IExitRoomResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an ExitRoomResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ExitRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ExitRoomResponse;

    /**
     * Decodes an ExitRoomResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ExitRoomResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ExitRoomResponse;

    /**
     * Verifies an ExitRoomResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an ExitRoomResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ExitRoomResponse
     */
    public static fromObject(object: { [k: string]: any }): ExitRoomResponse;

    /**
     * Creates a plain object from an ExitRoomResponse message. Also converts values to other types if specified.
     * @param message ExitRoomResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ExitRoomResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ExitRoomResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ExitRoomResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a StartGameRequest. */
export class StartGameRequest implements IStartGameRequest {

    /**
     * Constructs a new StartGameRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IStartGameRequest);

    /** StartGameRequest betAmount. */
    public betAmount: number;

    /**
     * Creates a new StartGameRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns StartGameRequest instance
     */
    public static create(properties?: IStartGameRequest): StartGameRequest;

    /**
     * Encodes the specified StartGameRequest message. Does not implicitly {@link StartGameRequest.verify|verify} messages.
     * @param message StartGameRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IStartGameRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified StartGameRequest message, length delimited. Does not implicitly {@link StartGameRequest.verify|verify} messages.
     * @param message StartGameRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IStartGameRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a StartGameRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns StartGameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): StartGameRequest;

    /**
     * Decodes a StartGameRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns StartGameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): StartGameRequest;

    /**
     * Verifies a StartGameRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a StartGameRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns StartGameRequest
     */
    public static fromObject(object: { [k: string]: any }): StartGameRequest;

    /**
     * Creates a plain object from a StartGameRequest message. Also converts values to other types if specified.
     * @param message StartGameRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: StartGameRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this StartGameRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for StartGameRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a StartGameResponse. */
export class StartGameResponse implements IStartGameResponse {

    /**
     * Constructs a new StartGameResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IStartGameResponse);

    /** StartGameResponse errorCode. */
    public errorCode: number;

    /** StartGameResponse element. */
    public element: number;

    /** StartGameResponse steps. */
    public steps: number[];

    /** StartGameResponse betAmount. */
    public betAmount: number;

    /** StartGameResponse mul. */
    public mul: number;

    /** StartGameResponse winAmount. */
    public winAmount: number;

    /** StartGameResponse canCashOut. */
    public canCashOut: boolean;

    /** StartGameResponse userGold. */
    public userGold: number;

    /**
     * Creates a new StartGameResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns StartGameResponse instance
     */
    public static create(properties?: IStartGameResponse): StartGameResponse;

    /**
     * Encodes the specified StartGameResponse message. Does not implicitly {@link StartGameResponse.verify|verify} messages.
     * @param message StartGameResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IStartGameResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified StartGameResponse message, length delimited. Does not implicitly {@link StartGameResponse.verify|verify} messages.
     * @param message StartGameResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IStartGameResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a StartGameResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns StartGameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): StartGameResponse;

    /**
     * Decodes a StartGameResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns StartGameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): StartGameResponse;

    /**
     * Verifies a StartGameResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a StartGameResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns StartGameResponse
     */
    public static fromObject(object: { [k: string]: any }): StartGameResponse;

    /**
     * Creates a plain object from a StartGameResponse message. Also converts values to other types if specified.
     * @param message StartGameResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: StartGameResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this StartGameResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for StartGameResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a CashOutRequest. */
export class CashOutRequest implements ICashOutRequest {

    /**
     * Constructs a new CashOutRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: ICashOutRequest);

    /**
     * Creates a new CashOutRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns CashOutRequest instance
     */
    public static create(properties?: ICashOutRequest): CashOutRequest;

    /**
     * Encodes the specified CashOutRequest message. Does not implicitly {@link CashOutRequest.verify|verify} messages.
     * @param message CashOutRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ICashOutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified CashOutRequest message, length delimited. Does not implicitly {@link CashOutRequest.verify|verify} messages.
     * @param message CashOutRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ICashOutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a CashOutRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns CashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): CashOutRequest;

    /**
     * Decodes a CashOutRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns CashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): CashOutRequest;

    /**
     * Verifies a CashOutRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a CashOutRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns CashOutRequest
     */
    public static fromObject(object: { [k: string]: any }): CashOutRequest;

    /**
     * Creates a plain object from a CashOutRequest message. Also converts values to other types if specified.
     * @param message CashOutRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: CashOutRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this CashOutRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for CashOutRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a CashOutResponse. */
export class CashOutResponse implements ICashOutResponse {

    /**
     * Constructs a new CashOutResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: ICashOutResponse);

    /** CashOutResponse errorCode. */
    public errorCode: number;

    /** CashOutResponse steps. */
    public steps: number[];

    /** CashOutResponse winAmount. */
    public winAmount: number;

    /** CashOutResponse canCashOut. */
    public canCashOut: boolean;

    /** CashOutResponse userGold. */
    public userGold: number;

    /**
     * Creates a new CashOutResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns CashOutResponse instance
     */
    public static create(properties?: ICashOutResponse): CashOutResponse;

    /**
     * Encodes the specified CashOutResponse message. Does not implicitly {@link CashOutResponse.verify|verify} messages.
     * @param message CashOutResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ICashOutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified CashOutResponse message, length delimited. Does not implicitly {@link CashOutResponse.verify|verify} messages.
     * @param message CashOutResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ICashOutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a CashOutResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns CashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): CashOutResponse;

    /**
     * Decodes a CashOutResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns CashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): CashOutResponse;

    /**
     * Verifies a CashOutResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a CashOutResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns CashOutResponse
     */
    public static fromObject(object: { [k: string]: any }): CashOutResponse;

    /**
     * Creates a plain object from a CashOutResponse message. Also converts values to other types if specified.
     * @param message CashOutResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: CashOutResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this CashOutResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for CashOutResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a PartCashOutRequest. */
export class PartCashOutRequest implements IPartCashOutRequest {

    /**
     * Constructs a new PartCashOutRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IPartCashOutRequest);

    /**
     * Creates a new PartCashOutRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns PartCashOutRequest instance
     */
    public static create(properties?: IPartCashOutRequest): PartCashOutRequest;

    /**
     * Encodes the specified PartCashOutRequest message. Does not implicitly {@link PartCashOutRequest.verify|verify} messages.
     * @param message PartCashOutRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IPartCashOutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified PartCashOutRequest message, length delimited. Does not implicitly {@link PartCashOutRequest.verify|verify} messages.
     * @param message PartCashOutRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IPartCashOutRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a PartCashOutRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PartCashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): PartCashOutRequest;

    /**
     * Decodes a PartCashOutRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns PartCashOutRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): PartCashOutRequest;

    /**
     * Verifies a PartCashOutRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a PartCashOutRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns PartCashOutRequest
     */
    public static fromObject(object: { [k: string]: any }): PartCashOutRequest;

    /**
     * Creates a plain object from a PartCashOutRequest message. Also converts values to other types if specified.
     * @param message PartCashOutRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: PartCashOutRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this PartCashOutRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for PartCashOutRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a PartCashOutResponse. */
export class PartCashOutResponse implements IPartCashOutResponse {

    /**
     * Constructs a new PartCashOutResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IPartCashOutResponse);

    /** PartCashOutResponse errorCode. */
    public errorCode: number;

    /** PartCashOutResponse steps. */
    public steps: number[];

    /** PartCashOutResponse betAmount. */
    public betAmount: number;

    /** PartCashOutResponse winAmount. */
    public winAmount: number;

    /** PartCashOutResponse canCashOut. */
    public canCashOut: boolean;

    /** PartCashOutResponse userGold. */
    public userGold: number;

    /**
     * Creates a new PartCashOutResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns PartCashOutResponse instance
     */
    public static create(properties?: IPartCashOutResponse): PartCashOutResponse;

    /**
     * Encodes the specified PartCashOutResponse message. Does not implicitly {@link PartCashOutResponse.verify|verify} messages.
     * @param message PartCashOutResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IPartCashOutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified PartCashOutResponse message, length delimited. Does not implicitly {@link PartCashOutResponse.verify|verify} messages.
     * @param message PartCashOutResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IPartCashOutResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a PartCashOutResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PartCashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): PartCashOutResponse;

    /**
     * Decodes a PartCashOutResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns PartCashOutResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): PartCashOutResponse;

    /**
     * Verifies a PartCashOutResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a PartCashOutResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns PartCashOutResponse
     */
    public static fromObject(object: { [k: string]: any }): PartCashOutResponse;

    /**
     * Creates a plain object from a PartCashOutResponse message. Also converts values to other types if specified.
     * @param message PartCashOutResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: PartCashOutResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this PartCashOutResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for PartCashOutResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a GetHistoryRequest. */
export class GetHistoryRequest implements IGetHistoryRequest {

    /**
     * Constructs a new GetHistoryRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IGetHistoryRequest);

    /** GetHistoryRequest page. */
    public page: number;

    /** GetHistoryRequest pageSize. */
    public pageSize: number;

    /**
     * Creates a new GetHistoryRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns GetHistoryRequest instance
     */
    public static create(properties?: IGetHistoryRequest): GetHistoryRequest;

    /**
     * Encodes the specified GetHistoryRequest message. Does not implicitly {@link GetHistoryRequest.verify|verify} messages.
     * @param message GetHistoryRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IGetHistoryRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified GetHistoryRequest message, length delimited. Does not implicitly {@link GetHistoryRequest.verify|verify} messages.
     * @param message GetHistoryRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IGetHistoryRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a GetHistoryRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GetHistoryRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): GetHistoryRequest;

    /**
     * Decodes a GetHistoryRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns GetHistoryRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): GetHistoryRequest;

    /**
     * Verifies a GetHistoryRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a GetHistoryRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns GetHistoryRequest
     */
    public static fromObject(object: { [k: string]: any }): GetHistoryRequest;

    /**
     * Creates a plain object from a GetHistoryRequest message. Also converts values to other types if specified.
     * @param message GetHistoryRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: GetHistoryRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this GetHistoryRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for GetHistoryRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a GetHistoryResponse. */
export class GetHistoryResponse implements IGetHistoryResponse {

    /**
     * Constructs a new GetHistoryResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IGetHistoryResponse);

    /** GetHistoryResponse errorCode. */
    public errorCode: number;

    /** GetHistoryResponse page. */
    public page: number;

    /** GetHistoryResponse totalCount. */
    public totalCount: number;

    /** GetHistoryResponse histories. */
    public histories: IGameHistory[];

    /**
     * Creates a new GetHistoryResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns GetHistoryResponse instance
     */
    public static create(properties?: IGetHistoryResponse): GetHistoryResponse;

    /**
     * Encodes the specified GetHistoryResponse message. Does not implicitly {@link GetHistoryResponse.verify|verify} messages.
     * @param message GetHistoryResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IGetHistoryResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified GetHistoryResponse message, length delimited. Does not implicitly {@link GetHistoryResponse.verify|verify} messages.
     * @param message GetHistoryResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IGetHistoryResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a GetHistoryResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GetHistoryResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): GetHistoryResponse;

    /**
     * Decodes a GetHistoryResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns GetHistoryResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): GetHistoryResponse;

    /**
     * Verifies a GetHistoryResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a GetHistoryResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns GetHistoryResponse
     */
    public static fromObject(object: { [k: string]: any }): GetHistoryResponse;

    /**
     * Creates a plain object from a GetHistoryResponse message. Also converts values to other types if specified.
     * @param message GetHistoryResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: GetHistoryResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this GetHistoryResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for GetHistoryResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a GameHistory. */
export class GameHistory implements IGameHistory {

    /**
     * Constructs a new GameHistory.
     * @param [properties] Properties to set
     */
    constructor(properties?: IGameHistory);

    /** GameHistory id. */
    public id: string;

    /** GameHistory time. */
    public time: (number|Long);

    /** GameHistory betAmount. */
    public betAmount: number;

    /** GameHistory multiple. */
    public multiple: number;

    /** GameHistory winAmount. */
    public winAmount: number;

    /** GameHistory serverSeed. */
    public serverSeed: string;

    /** GameHistory userSeed. */
    public userSeed: string;

    /** GameHistory hashCode. */
    public hashCode: string;

    /** GameHistory hexCode. */
    public hexCode: string;

    /** GameHistory decimalCode. */
    public decimalCode: string;

    /**
     * Creates a new GameHistory instance using the specified properties.
     * @param [properties] Properties to set
     * @returns GameHistory instance
     */
    public static create(properties?: IGameHistory): GameHistory;

    /**
     * Encodes the specified GameHistory message. Does not implicitly {@link GameHistory.verify|verify} messages.
     * @param message GameHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IGameHistory, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified GameHistory message, length delimited. Does not implicitly {@link GameHistory.verify|verify} messages.
     * @param message GameHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IGameHistory, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a GameHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GameHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): GameHistory;

    /**
     * Decodes a GameHistory message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns GameHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): GameHistory;

    /**
     * Verifies a GameHistory message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a GameHistory message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns GameHistory
     */
    public static fromObject(object: { [k: string]: any }): GameHistory;

    /**
     * Creates a plain object from a GameHistory message. Also converts values to other types if specified.
     * @param message GameHistory
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: GameHistory, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this GameHistory to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for GameHistory
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a ChangeNicknameRequest. */
export class ChangeNicknameRequest implements IChangeNicknameRequest {

    /**
     * Constructs a new ChangeNicknameRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IChangeNicknameRequest);

    /** ChangeNicknameRequest nickname. */
    public nickname: string;

    /**
     * Creates a new ChangeNicknameRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ChangeNicknameRequest instance
     */
    public static create(properties?: IChangeNicknameRequest): ChangeNicknameRequest;

    /**
     * Encodes the specified ChangeNicknameRequest message. Does not implicitly {@link ChangeNicknameRequest.verify|verify} messages.
     * @param message ChangeNicknameRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IChangeNicknameRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ChangeNicknameRequest message, length delimited. Does not implicitly {@link ChangeNicknameRequest.verify|verify} messages.
     * @param message ChangeNicknameRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IChangeNicknameRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ChangeNicknameRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ChangeNicknameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ChangeNicknameRequest;

    /**
     * Decodes a ChangeNicknameRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ChangeNicknameRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ChangeNicknameRequest;

    /**
     * Verifies a ChangeNicknameRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ChangeNicknameRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ChangeNicknameRequest
     */
    public static fromObject(object: { [k: string]: any }): ChangeNicknameRequest;

    /**
     * Creates a plain object from a ChangeNicknameRequest message. Also converts values to other types if specified.
     * @param message ChangeNicknameRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ChangeNicknameRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ChangeNicknameRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ChangeNicknameRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a ChangeNicknameResponse. */
export class ChangeNicknameResponse implements IChangeNicknameResponse {

    /**
     * Constructs a new ChangeNicknameResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IChangeNicknameResponse);

    /** ChangeNicknameResponse errorCode. */
    public errorCode: number;

    /** ChangeNicknameResponse nickname. */
    public nickname: string;

    /**
     * Creates a new ChangeNicknameResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ChangeNicknameResponse instance
     */
    public static create(properties?: IChangeNicknameResponse): ChangeNicknameResponse;

    /**
     * Encodes the specified ChangeNicknameResponse message. Does not implicitly {@link ChangeNicknameResponse.verify|verify} messages.
     * @param message ChangeNicknameResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IChangeNicknameResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ChangeNicknameResponse message, length delimited. Does not implicitly {@link ChangeNicknameResponse.verify|verify} messages.
     * @param message ChangeNicknameResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IChangeNicknameResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ChangeNicknameResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ChangeNicknameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ChangeNicknameResponse;

    /**
     * Decodes a ChangeNicknameResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ChangeNicknameResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ChangeNicknameResponse;

    /**
     * Verifies a ChangeNicknameResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ChangeNicknameResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ChangeNicknameResponse
     */
    public static fromObject(object: { [k: string]: any }): ChangeNicknameResponse;

    /**
     * Creates a plain object from a ChangeNicknameResponse message. Also converts values to other types if specified.
     * @param message ChangeNicknameResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ChangeNicknameResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ChangeNicknameResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ChangeNicknameResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a ChangeAvatarRequest. */
export class ChangeAvatarRequest implements IChangeAvatarRequest {

    /**
     * Constructs a new ChangeAvatarRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IChangeAvatarRequest);

    /** ChangeAvatarRequest avatar. */
    public avatar: string;

    /**
     * Creates a new ChangeAvatarRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ChangeAvatarRequest instance
     */
    public static create(properties?: IChangeAvatarRequest): ChangeAvatarRequest;

    /**
     * Encodes the specified ChangeAvatarRequest message. Does not implicitly {@link ChangeAvatarRequest.verify|verify} messages.
     * @param message ChangeAvatarRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IChangeAvatarRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ChangeAvatarRequest message, length delimited. Does not implicitly {@link ChangeAvatarRequest.verify|verify} messages.
     * @param message ChangeAvatarRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IChangeAvatarRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ChangeAvatarRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ChangeAvatarRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ChangeAvatarRequest;

    /**
     * Decodes a ChangeAvatarRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ChangeAvatarRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ChangeAvatarRequest;

    /**
     * Verifies a ChangeAvatarRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ChangeAvatarRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ChangeAvatarRequest
     */
    public static fromObject(object: { [k: string]: any }): ChangeAvatarRequest;

    /**
     * Creates a plain object from a ChangeAvatarRequest message. Also converts values to other types if specified.
     * @param message ChangeAvatarRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ChangeAvatarRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ChangeAvatarRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ChangeAvatarRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a ChangeAvatarResponse. */
export class ChangeAvatarResponse implements IChangeAvatarResponse {

    /**
     * Constructs a new ChangeAvatarResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IChangeAvatarResponse);

    /** ChangeAvatarResponse errorCode. */
    public errorCode: number;

    /** ChangeAvatarResponse avatar. */
    public avatar: string;

    /**
     * Creates a new ChangeAvatarResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ChangeAvatarResponse instance
     */
    public static create(properties?: IChangeAvatarResponse): ChangeAvatarResponse;

    /**
     * Encodes the specified ChangeAvatarResponse message. Does not implicitly {@link ChangeAvatarResponse.verify|verify} messages.
     * @param message ChangeAvatarResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IChangeAvatarResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ChangeAvatarResponse message, length delimited. Does not implicitly {@link ChangeAvatarResponse.verify|verify} messages.
     * @param message ChangeAvatarResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IChangeAvatarResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ChangeAvatarResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ChangeAvatarResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ChangeAvatarResponse;

    /**
     * Decodes a ChangeAvatarResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ChangeAvatarResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ChangeAvatarResponse;

    /**
     * Verifies a ChangeAvatarResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ChangeAvatarResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ChangeAvatarResponse
     */
    public static fromObject(object: { [k: string]: any }): ChangeAvatarResponse;

    /**
     * Creates a plain object from a ChangeAvatarResponse message. Also converts values to other types if specified.
     * @param message ChangeAvatarResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ChangeAvatarResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ChangeAvatarResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ChangeAvatarResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a MessageHeader. */
export class MessageHeader implements IMessageHeader {

    /**
     * Constructs a new MessageHeader.
     * @param [properties] Properties to set
     */
    constructor(properties?: IMessageHeader);

    /** MessageHeader serverId. */
    public serverId: number;

    /** MessageHeader messageId. */
    public messageId: number;

    /** MessageHeader requestId. */
    public requestId: number;

    /** MessageHeader isResponse. */
    public isResponse: boolean;

    /** MessageHeader errCode. */
    public errCode: number;

    /**
     * Creates a new MessageHeader instance using the specified properties.
     * @param [properties] Properties to set
     * @returns MessageHeader instance
     */
    public static create(properties?: IMessageHeader): MessageHeader;

    /**
     * Encodes the specified MessageHeader message. Does not implicitly {@link MessageHeader.verify|verify} messages.
     * @param message MessageHeader message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IMessageHeader, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified MessageHeader message, length delimited. Does not implicitly {@link MessageHeader.verify|verify} messages.
     * @param message MessageHeader message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IMessageHeader, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a MessageHeader message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MessageHeader
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): MessageHeader;

    /**
     * Decodes a MessageHeader message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns MessageHeader
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): MessageHeader;

    /**
     * Verifies a MessageHeader message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a MessageHeader message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns MessageHeader
     */
    public static fromObject(object: { [k: string]: any }): MessageHeader;

    /**
     * Creates a plain object from a MessageHeader message. Also converts values to other types if specified.
     * @param message MessageHeader
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: MessageHeader, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this MessageHeader to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for MessageHeader
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a WrappedMessage. */
export class WrappedMessage implements IWrappedMessage {

    /**
     * Constructs a new WrappedMessage.
     * @param [properties] Properties to set
     */
    constructor(properties?: IWrappedMessage);

    /** WrappedMessage header. */
    public header?: (IMessageHeader|null);

    /** WrappedMessage body. */
    public body: Uint8Array;

    /**
     * Creates a new WrappedMessage instance using the specified properties.
     * @param [properties] Properties to set
     * @returns WrappedMessage instance
     */
    public static create(properties?: IWrappedMessage): WrappedMessage;

    /**
     * Encodes the specified WrappedMessage message. Does not implicitly {@link WrappedMessage.verify|verify} messages.
     * @param message WrappedMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IWrappedMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified WrappedMessage message, length delimited. Does not implicitly {@link WrappedMessage.verify|verify} messages.
     * @param message WrappedMessage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IWrappedMessage, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a WrappedMessage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns WrappedMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): WrappedMessage;

    /**
     * Decodes a WrappedMessage message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns WrappedMessage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): WrappedMessage;

    /**
     * Verifies a WrappedMessage message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a WrappedMessage message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns WrappedMessage
     */
    public static fromObject(object: { [k: string]: any }): WrappedMessage;

    /**
     * Creates a plain object from a WrappedMessage message. Also converts values to other types if specified.
     * @param message WrappedMessage
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: WrappedMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this WrappedMessage to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for WrappedMessage
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a LoginRequest. */
export class LoginRequest implements ILoginRequest {

    /**
     * Constructs a new LoginRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: ILoginRequest);

    /** LoginRequest userName. */
    public userName: string;

    /** LoginRequest password. */
    public password: string;

    /**
     * Creates a new LoginRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns LoginRequest instance
     */
    public static create(properties?: ILoginRequest): LoginRequest;

    /**
     * Encodes the specified LoginRequest message. Does not implicitly {@link LoginRequest.verify|verify} messages.
     * @param message LoginRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified LoginRequest message, length delimited. Does not implicitly {@link LoginRequest.verify|verify} messages.
     * @param message LoginRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ILoginRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a LoginRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns LoginRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LoginRequest;

    /**
     * Decodes a LoginRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns LoginRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LoginRequest;

    /**
     * Verifies a LoginRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a LoginRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns LoginRequest
     */
    public static fromObject(object: { [k: string]: any }): LoginRequest;

    /**
     * Creates a plain object from a LoginRequest message. Also converts values to other types if specified.
     * @param message LoginRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: LoginRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this LoginRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for LoginRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a LoginResponse. */
export class LoginResponse implements ILoginResponse {

    /**
     * Constructs a new LoginResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: ILoginResponse);

    /** LoginResponse errorCode. */
    public errorCode: number;

    /** LoginResponse userName. */
    public userName: string;

    /** LoginResponse gold. */
    public gold: number;

    /** LoginResponse nickName. */
    public nickName: string;

    /** LoginResponse avatar. */
    public avatar: string;

    /** LoginResponse currency. */
    public currency: string;

    /** LoginResponse userId. */
    public userId: string;

    /**
     * Creates a new LoginResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns LoginResponse instance
     */
    public static create(properties?: ILoginResponse): LoginResponse;

    /**
     * Encodes the specified LoginResponse message. Does not implicitly {@link LoginResponse.verify|verify} messages.
     * @param message LoginResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified LoginResponse message, length delimited. Does not implicitly {@link LoginResponse.verify|verify} messages.
     * @param message LoginResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ILoginResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a LoginResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns LoginResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LoginResponse;

    /**
     * Decodes a LoginResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns LoginResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LoginResponse;

    /**
     * Verifies a LoginResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a LoginResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns LoginResponse
     */
    public static fromObject(object: { [k: string]: any }): LoginResponse;

    /**
     * Creates a plain object from a LoginResponse message. Also converts values to other types if specified.
     * @param message LoginResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: LoginResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this LoginResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for LoginResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a LoginTokenRequest. */
export class LoginTokenRequest implements ILoginTokenRequest {

    /**
     * Constructs a new LoginTokenRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: ILoginTokenRequest);

    /** LoginTokenRequest token. */
    public token: string;

    /**
     * Creates a new LoginTokenRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns LoginTokenRequest instance
     */
    public static create(properties?: ILoginTokenRequest): LoginTokenRequest;

    /**
     * Encodes the specified LoginTokenRequest message. Does not implicitly {@link LoginTokenRequest.verify|verify} messages.
     * @param message LoginTokenRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ILoginTokenRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified LoginTokenRequest message, length delimited. Does not implicitly {@link LoginTokenRequest.verify|verify} messages.
     * @param message LoginTokenRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ILoginTokenRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a LoginTokenRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns LoginTokenRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): LoginTokenRequest;

    /**
     * Decodes a LoginTokenRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns LoginTokenRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): LoginTokenRequest;

    /**
     * Verifies a LoginTokenRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a LoginTokenRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns LoginTokenRequest
     */
    public static fromObject(object: { [k: string]: any }): LoginTokenRequest;

    /**
     * Creates a plain object from a LoginTokenRequest message. Also converts values to other types if specified.
     * @param message LoginTokenRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: LoginTokenRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this LoginTokenRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for LoginTokenRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a loginConflictRequest. */
export class loginConflictRequest implements IloginConflictRequest {

    /**
     * Constructs a new loginConflictRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IloginConflictRequest);

    /** loginConflictRequest sysTime. */
    public sysTime: (number|Long);

    /**
     * Creates a new loginConflictRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns loginConflictRequest instance
     */
    public static create(properties?: IloginConflictRequest): loginConflictRequest;

    /**
     * Encodes the specified loginConflictRequest message. Does not implicitly {@link loginConflictRequest.verify|verify} messages.
     * @param message loginConflictRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IloginConflictRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified loginConflictRequest message, length delimited. Does not implicitly {@link loginConflictRequest.verify|verify} messages.
     * @param message loginConflictRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IloginConflictRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a loginConflictRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns loginConflictRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): loginConflictRequest;

    /**
     * Decodes a loginConflictRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns loginConflictRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): loginConflictRequest;

    /**
     * Verifies a loginConflictRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a loginConflictRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns loginConflictRequest
     */
    public static fromObject(object: { [k: string]: any }): loginConflictRequest;

    /**
     * Creates a plain object from a loginConflictRequest message. Also converts values to other types if specified.
     * @param message loginConflictRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: loginConflictRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this loginConflictRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for loginConflictRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a SessionTerminatedRequest. */
export class SessionTerminatedRequest implements ISessionTerminatedRequest {

    /**
     * Constructs a new SessionTerminatedRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: ISessionTerminatedRequest);

    /** SessionTerminatedRequest sysTime. */
    public sysTime: (number|Long);

    /**
     * Creates a new SessionTerminatedRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns SessionTerminatedRequest instance
     */
    public static create(properties?: ISessionTerminatedRequest): SessionTerminatedRequest;

    /**
     * Encodes the specified SessionTerminatedRequest message. Does not implicitly {@link SessionTerminatedRequest.verify|verify} messages.
     * @param message SessionTerminatedRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: ISessionTerminatedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified SessionTerminatedRequest message, length delimited. Does not implicitly {@link SessionTerminatedRequest.verify|verify} messages.
     * @param message SessionTerminatedRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: ISessionTerminatedRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a SessionTerminatedRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns SessionTerminatedRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): SessionTerminatedRequest;

    /**
     * Decodes a SessionTerminatedRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns SessionTerminatedRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): SessionTerminatedRequest;

    /**
     * Verifies a SessionTerminatedRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a SessionTerminatedRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns SessionTerminatedRequest
     */
    public static fromObject(object: { [k: string]: any }): SessionTerminatedRequest;

    /**
     * Creates a plain object from a SessionTerminatedRequest message. Also converts values to other types if specified.
     * @param message SessionTerminatedRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: SessionTerminatedRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this SessionTerminatedRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for SessionTerminatedRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents an OfflineRequest. */
export class OfflineRequest implements IOfflineRequest {

    /**
     * Constructs a new OfflineRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IOfflineRequest);

    /** OfflineRequest userId. */
    public userId: string;

    /** OfflineRequest channelId. */
    public channelId: string;

    /**
     * Creates a new OfflineRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns OfflineRequest instance
     */
    public static create(properties?: IOfflineRequest): OfflineRequest;

    /**
     * Encodes the specified OfflineRequest message. Does not implicitly {@link OfflineRequest.verify|verify} messages.
     * @param message OfflineRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IOfflineRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified OfflineRequest message, length delimited. Does not implicitly {@link OfflineRequest.verify|verify} messages.
     * @param message OfflineRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IOfflineRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes an OfflineRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns OfflineRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): OfflineRequest;

    /**
     * Decodes an OfflineRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns OfflineRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): OfflineRequest;

    /**
     * Verifies an OfflineRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates an OfflineRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns OfflineRequest
     */
    public static fromObject(object: { [k: string]: any }): OfflineRequest;

    /**
     * Creates a plain object from an OfflineRequest message. Also converts values to other types if specified.
     * @param message OfflineRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: OfflineRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this OfflineRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for OfflineRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** 心跳 */
export class HeartbeatRequest implements IHeartbeatRequest {

    /**
     * Constructs a new HeartbeatRequest.
     * @param [properties] Properties to set
     */
    constructor(properties?: IHeartbeatRequest);

    /** HeartbeatRequest sysTime. */
    public sysTime: (number|Long);

    /**
     * Creates a new HeartbeatRequest instance using the specified properties.
     * @param [properties] Properties to set
     * @returns HeartbeatRequest instance
     */
    public static create(properties?: IHeartbeatRequest): HeartbeatRequest;

    /**
     * Encodes the specified HeartbeatRequest message. Does not implicitly {@link HeartbeatRequest.verify|verify} messages.
     * @param message HeartbeatRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IHeartbeatRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified HeartbeatRequest message, length delimited. Does not implicitly {@link HeartbeatRequest.verify|verify} messages.
     * @param message HeartbeatRequest message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IHeartbeatRequest, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a HeartbeatRequest message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns HeartbeatRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): HeartbeatRequest;

    /**
     * Decodes a HeartbeatRequest message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns HeartbeatRequest
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): HeartbeatRequest;

    /**
     * Verifies a HeartbeatRequest message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a HeartbeatRequest message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns HeartbeatRequest
     */
    public static fromObject(object: { [k: string]: any }): HeartbeatRequest;

    /**
     * Creates a plain object from a HeartbeatRequest message. Also converts values to other types if specified.
     * @param message HeartbeatRequest
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: HeartbeatRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this HeartbeatRequest to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for HeartbeatRequest
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a HeartbeatResponse. */
export class HeartbeatResponse implements IHeartbeatResponse {

    /**
     * Constructs a new HeartbeatResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: IHeartbeatResponse);

    /** HeartbeatResponse sysTime. */
    public sysTime: (number|Long);

    /**
     * Creates a new HeartbeatResponse instance using the specified properties.
     * @param [properties] Properties to set
     * @returns HeartbeatResponse instance
     */
    public static create(properties?: IHeartbeatResponse): HeartbeatResponse;

    /**
     * Encodes the specified HeartbeatResponse message. Does not implicitly {@link HeartbeatResponse.verify|verify} messages.
     * @param message HeartbeatResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IHeartbeatResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified HeartbeatResponse message, length delimited. Does not implicitly {@link HeartbeatResponse.verify|verify} messages.
     * @param message HeartbeatResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IHeartbeatResponse, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a HeartbeatResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns HeartbeatResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): HeartbeatResponse;

    /**
     * Decodes a HeartbeatResponse message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns HeartbeatResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): HeartbeatResponse;

    /**
     * Verifies a HeartbeatResponse message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a HeartbeatResponse message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns HeartbeatResponse
     */
    public static fromObject(object: { [k: string]: any }): HeartbeatResponse;

    /**
     * Creates a plain object from a HeartbeatResponse message. Also converts values to other types if specified.
     * @param message HeartbeatResponse
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: HeartbeatResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this HeartbeatResponse to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for HeartbeatResponse
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a DefaultErrorCode. */
export class DefaultErrorCode implements IDefaultErrorCode {

    /**
     * Constructs a new DefaultErrorCode.
     * @param [properties] Properties to set
     */
    constructor(properties?: IDefaultErrorCode);

    /** DefaultErrorCode errorCode. */
    public errorCode: number;

    /**
     * Creates a new DefaultErrorCode instance using the specified properties.
     * @param [properties] Properties to set
     * @returns DefaultErrorCode instance
     */
    public static create(properties?: IDefaultErrorCode): DefaultErrorCode;

    /**
     * Encodes the specified DefaultErrorCode message. Does not implicitly {@link DefaultErrorCode.verify|verify} messages.
     * @param message DefaultErrorCode message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IDefaultErrorCode, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified DefaultErrorCode message, length delimited. Does not implicitly {@link DefaultErrorCode.verify|verify} messages.
     * @param message DefaultErrorCode message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IDefaultErrorCode, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a DefaultErrorCode message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DefaultErrorCode
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): DefaultErrorCode;

    /**
     * Decodes a DefaultErrorCode message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns DefaultErrorCode
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): DefaultErrorCode;

    /**
     * Verifies a DefaultErrorCode message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a DefaultErrorCode message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns DefaultErrorCode
     */
    public static fromObject(object: { [k: string]: any }): DefaultErrorCode;

    /**
     * Creates a plain object from a DefaultErrorCode message. Also converts values to other types if specified.
     * @param message DefaultErrorCode
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: DefaultErrorCode, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this DefaultErrorCode to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for DefaultErrorCode
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}

/** Represents a ServerErrorBody. */
export class ServerErrorBody implements IServerErrorBody {

    /**
     * Constructs a new ServerErrorBody.
     * @param [properties] Properties to set
     */
    constructor(properties?: IServerErrorBody);

    /** ServerErrorBody code. */
    public code: number;

    /** ServerErrorBody message. */
    public message: string;

    /**
     * Creates a new ServerErrorBody instance using the specified properties.
     * @param [properties] Properties to set
     * @returns ServerErrorBody instance
     */
    public static create(properties?: IServerErrorBody): ServerErrorBody;

    /**
     * Encodes the specified ServerErrorBody message. Does not implicitly {@link ServerErrorBody.verify|verify} messages.
     * @param message ServerErrorBody message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(message: IServerErrorBody, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Encodes the specified ServerErrorBody message, length delimited. Does not implicitly {@link ServerErrorBody.verify|verify} messages.
     * @param message ServerErrorBody message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encodeDelimited(message: IServerErrorBody, writer?: $protobuf.Writer): $protobuf.Writer;

    /**
     * Decodes a ServerErrorBody message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ServerErrorBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): ServerErrorBody;

    /**
     * Decodes a ServerErrorBody message from the specified reader or buffer, length delimited.
     * @param reader Reader or buffer to decode from
     * @returns ServerErrorBody
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): ServerErrorBody;

    /**
     * Verifies a ServerErrorBody message.
     * @param message Plain object to verify
     * @returns `null` if valid, otherwise the reason why it is not
     */
    public static verify(message: { [k: string]: any }): (string|null);

    /**
     * Creates a ServerErrorBody message from a plain object. Also converts values to their respective internal types.
     * @param object Plain object
     * @returns ServerErrorBody
     */
    public static fromObject(object: { [k: string]: any }): ServerErrorBody;

    /**
     * Creates a plain object from a ServerErrorBody message. Also converts values to other types if specified.
     * @param message ServerErrorBody
     * @param [options] Conversion options
     * @returns Plain object
     */
    public static toObject(message: ServerErrorBody, options?: $protobuf.IConversionOptions): { [k: string]: any };

    /**
     * Converts this ServerErrorBody to JSON.
     * @returns JSON object
     */
    public toJSON(): { [k: string]: any };

    /**
     * Gets the default type url for ServerErrorBody
     * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
     * @returns The default type url
     */
    public static getTypeUrl(typeUrlPrefix?: string): string;
}
