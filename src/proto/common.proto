syntax = "proto3";

option java_package = "com.bat.protocol.common.msg";
option java_outer_classname = "CommonProto";

// 消息头
message MessageHeader {
  int32 serverId = 1;   // 服务器Id
  uint32 messageId = 2;    //消息号
  uint32 requestId = 3;    //消息唯一ID
  bool isResponse = 4;     // 是否是响应消息
  int32 errCode = 5; //返回标识

}

//整体消息
message WrappedMessage{
  MessageHeader header = 1;  //消息头
  bytes body = 2; //消息体
}

//登录请求
message LoginRequest { //101
  string userName = 1;
  string password = 2;
}

//登录返回
message LoginResponse { //102
  int32 errorCode = 1; //返回标识

  string userName = 2;
  double gold = 3;
  string nickName = 4;
  string avatar = 5;
  string currency = 6;
  string userId = 7;
}

//登录请求
message LoginTokenRequest { //103
  string token = 1;
}
//推送顶号提示
message loginConflictRequest { //104
  int64 sysTime = 1; //时间戳
}
//推送踢人提示
message SessionTerminatedRequest { //105
  int64 sysTime = 1; //时间戳
}

// 离线请求
message OfflineRequest { //107
  string userId = 1;
  string channelId = 2;
}

///心跳
message HeartbeatRequest { //0
  int64 sysTime = 1; //时间戳
}
message HeartbeatResponse { //0
  int64 sysTime = 1; //服务器时间戳（毫秒级）
}

// 默认异常码消息体
message DefaultErrorCode {
  int32 errorCode = 1;
}

// 服务器异常消息体
message ServerErrorBody {
  int32 code = 1;
  string message = 2;
}