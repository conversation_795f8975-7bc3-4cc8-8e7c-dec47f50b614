import { SocketEventCode } from '@/constant';
import { glog } from '@/utils/utils';
import protoRoot from '@/proto/index';

export class MessageService {
  /**
   * 编码消息
   */
  static encode(eventCode: SocketEventCode, requestId: number, data?: any): Uint8Array {
    const header = {
      serverId: eventCode === 0 ? 0 : protoRoot.ServerType.TWIST,
      messageId: eventCode,
      requestId: requestId,
      isResponse: false,
      errCode: 0,
    };

    glog('send data', header, data);

    const encodedData = data ? this.encodeMessageData(eventCode, data) : new Uint8Array();
    const buffer = protoRoot.WrappedMessage.encode({ header, body: encodedData }).finish();
    
    return buffer;
  }

  /**
   * 解码消息
   */
  static decode(buffer: Uint8Array): DecodedMessage<any> | null {
    try {
      const message = protoRoot.WrappedMessage.decode(buffer);
      const { header, body } = message;

      if (!header) {
        throw new Error('Message header is missing');
      }

      const decodedData = header.errCode ? {} : this.decodeMessageData(header.messageId, body);
      
      return { header, data: decodedData };
    } catch (error) {
      console.error('Message decode error:', error);
      return null;
    }
  }

  /**
   * 根据消息类型编码数据
   */
  private static encodeMessageData(messageId: number, data: any): Uint8Array {
    switch (messageId) {
      case SocketEventCode.HEARTBEAT:
        return protoRoot.HeartbeatRequest.encode(data).finish();

      case SocketEventCode.LOGIN_REQ:
        return protoRoot.LoginRequest.encode(data).finish();

      case SocketEventCode.LOGIN_TOKEN_REQ:
        return protoRoot.LoginTokenRequest.encode(data).finish();

      case SocketEventCode.ENTER_ROOM_REQ:
        return protoRoot.EnterRoomRequest.encode(data).finish();

      case SocketEventCode.EXIT_ROOM_REQ:
        return protoRoot.ExitRoomRequest.encode(data).finish();

      case SocketEventCode.USER_BET_REQ:
        return protoRoot.StartGameRequest.encode(data).finish();

      case SocketEventCode.USER_FULL_CASHOUT_REQ:
        return protoRoot.CashOutRequest.encode(data).finish();

      case SocketEventCode.USER_PART_CASHOUT_REQ:
        return protoRoot.PartCashOutRequest.encode(data).finish();

      case SocketEventCode.USER_BET_HISTORY_REQ:
        return protoRoot.GetHistoryRequest.encode(data).finish();

      case SocketEventCode.USER_UPDATE_AVATAR_REQ:
        return protoRoot.ChangeAvatarRequest.encode(data).finish();

      default:
        console.warn(`Unknown message type for encoding: ${messageId}`);
        return new Uint8Array();
    }
  }

  /**
   * 根据消息类型解码数据
   */
  private static decodeMessageData(messageId: number, body: Uint8Array): any {
    if (!body || body.length === 0) {
      return null;
    }

    try {
      switch (messageId) {
        case SocketEventCode.HEARTBEAT:
          return protoRoot.HeartbeatResponse.decode(body);

        case SocketEventCode.LOGIN_RES:
          return protoRoot.LoginResponse.decode(body);

        case SocketEventCode.PUSH_CONFLICT:
          return protoRoot.loginConflictRequest.decode(body);

        case SocketEventCode.PUSH_KICK_OUT:
          return protoRoot.SessionTerminatedRequest.decode(body);

        case SocketEventCode.ENTER_ROOM_RES:
          return protoRoot.EnterRoomResponse.decode(body);

        case SocketEventCode.EXIT_ROOM_RES:
          return protoRoot.ExitRoomResponse.decode(body);

        case SocketEventCode.USER_BET_RES:
          return protoRoot.StartGameResponse.decode(body);

        case SocketEventCode.USER_FULL_CASHOUT_RES:
          return protoRoot.CashOutResponse.decode(body);

        case SocketEventCode.USER_PART_CASHOUT_RES:
          return protoRoot.PartCashOutResponse.decode(body);

        case SocketEventCode.USER_BET_HISTORY_RES:
          return protoRoot.GetHistoryResponse.decode(body);

        case SocketEventCode.USER_UPDATE_AVATAR_RES:
          return protoRoot.ChangeAvatarResponse.decode(body);
        
        default:
          console.warn(`Unknown message type for decoding: ${messageId}`);
          return body;
      }
    } catch (error) {
      console.error(`Failed to decode message ${messageId}:`, error);
      return null;
    }
  }
}