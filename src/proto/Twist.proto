syntax = "proto3";

option java_package = "com.bat.protocol.minigame";
option java_outer_classname = "TwistProto";

//请求:进入
message EnterRoomRequest { //21901

}

//返回:进入
message EnterRoomResponse { //21902
  int32 errorCode = 1;

  repeated int32 steps = 2; //阶段列表 [水、土、火]
  double betAmount = 3; //下注金额
  bool canCashOut = 4; //是否可提现

  double minBetAmount = 5; // 最小下注金额
  double maxBetAmount = 6; // 最大下注金额
  double defaultBetAmount = 7; //默认下注金额
  double maxWinAmount = 8; // 最大中奖金额
  repeated double multiples_1 = 9; //水倍率列表
  repeated double multiples_2 = 10; //土倍率列表
  repeated double multiples_3 = 11; //火倍率列表

  string avatar = 12; //头像
  string nickname = 13; //昵称
  double userGold = 14; //玩家金额

  string clientSeed = 15; //客户端种子
  string serverSeed = 16; //服务器种子
}

//请求:退出
message ExitRoomRequest { //21903

}

//返回:退出
message ExitRoomResponse { //21904
  int32 errorCode = 1;

  double userGold = 3; //玩家金额
}

//请求:开始游戏
message StartGameRequest { //21905
  double betAmount = 1; //下注金额
}

//返回:开始游戏
message StartGameResponse { //21906
  int32 errorCode = 1;

  int32 element = 2; //元素
  repeated int32 steps = 3; //阶段列表 [水、土、火]
  double betAmount = 4; //下注金额
  double mul = 5; // 中奖倍率
  double winAmount = 6; //中奖金额
  bool canCashOut = 7; //是否可提现

  double userGold = 8; //玩家金额
}

//请求:提现
message CashOutRequest { //21907

}

//返回:提现
message CashOutResponse { //21908
  int32 errorCode = 1;

  repeated int32 steps = 2; //阶段列表 [水、土、火]
  double winAmount = 3; //中奖金额
  bool canCashOut = 4; //是否可提现

  double userGold = 5; //玩家金额
}

//请求:部分提现
message PartCashOutRequest { //21909

}

//返回:部分提现
message PartCashOutResponse { //21910
  int32 errorCode = 1;

  repeated int32 steps = 2; //阶段列表 [水、土、火]
  double betAmount = 3; //下注金额
  double winAmount = 4; //中奖金额
  bool canCashOut = 5; //是否可提现

  double userGold = 6; //玩家金额
}

// 请求:获取游戏历史记录
message GetHistoryRequest { //21911
  int32 page = 1; //页数
  int32 pageSize = 2;
}

//返回:获取游戏历史记录
message GetHistoryResponse { //21912
  int32 errorCode = 1;

  int32 page = 2; //页数
  int32 totalCount = 3; //总历史记录数
  repeated GameHistory histories = 4; //历史记录列表
}

// 历史记录
message GameHistory {
  string id = 1; //id
  int64 time = 2; //时间
  double betAmount = 3; //下注金额
  double multiple = 4; //倍数
  double winAmount = 5; //中奖金额
  string serverSeed = 6; //服务器种子
  string userSeed = 7; //用户种子
  string hashCode = 8; //哈希代码
  string hexCode = 9; //十六进制代码
  string decimalCode = 10; //十进制代码
}

//请求:修改昵称
message ChangeNicknameRequest { //21913
  string nickname = 1; //昵称
}

//返回:修改昵称
message ChangeNicknameResponse { //21914
  int32 errorCode = 1;

  string nickname = 2; //昵称
}

//请求:修改头像
message ChangeAvatarRequest { //21915
  string avatar = 1; //头像
}

//返回:修改头像
message ChangeAvatarResponse { //21916
  int32 errorCode = 1;

  string avatar = 3; //头像
}