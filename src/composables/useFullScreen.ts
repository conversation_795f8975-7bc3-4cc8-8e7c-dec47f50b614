import { ref, onMounted, onUnmounted } from 'vue';

export function useFullScreen() {
  const isFullScreen = ref(false); // 当前是否是全屏

  // 进入全屏
  const enterFullScreen = async (element: HTMLElement | null) => {
    if (!element) {
      console.warn("Element to enter fullscreen not found");
      return;
    }

    try {
      await element.requestFullscreen?.();
      isFullScreen.value = true;
    } catch (error) {
      console.error("Failed to enter fullscreen:", error);
    }
  };

  // 退出全屏
  const exitFullScreen = async () => {
    try {
      if (document.fullscreenElement) {
        await document.exitFullscreen?.();
        isFullScreen.value = false;
      }
    } catch (error) {
      console.error("Failed to exit fullscreen:", error);
    }
  };

  const toggleFullScreen = async (element: HTMLElement | null) => {
    if (isFullScreen.value) {
      await exitFullScreen();
    } else {
      await enterFullScreen(element);
    }
  };

  // 全屏状态变化回调
  const handleFullScreenChange = () => {
    isFullScreen.value = !!document.fullscreenElement;
  };

  // 监听全屏事件
  onMounted(() => {
    document.addEventListener('fullscreenchange', handleFullScreenChange);
  });

  onUnmounted(() => {
    document.removeEventListener('fullscreenchange', handleFullScreenChange);
  });

  return {
    isFullScreen,
    enterFullScreen,
    exitFullScreen,
    toggleFullScreen
  };
}