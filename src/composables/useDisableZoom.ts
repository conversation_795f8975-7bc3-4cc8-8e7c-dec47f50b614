import {onMounted, onUnmounted} from "vue";

export const useDisableZoom = () => {
  const iosGestureStartHandle = (e: any) => {
    e.preventDefault();
  }

  onMounted(() => {
    document.addEventListener('gesturestart', iosGestureStartHandle);
    document.addEventListener('gesturechange', iosGestureStartHandle);
  })

  onUnmounted(() => {
    document.removeEventListener('gesturestart', iosGestureStartHandle);
    document.removeEventListener('gesturechange', iosGestureStartHandle);
  })
}