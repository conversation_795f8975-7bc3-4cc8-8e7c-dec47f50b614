export const useTimer = ({ interval }: { interval?: boolean } = {}) => {
  let timer: any;

  const clean = () => {
    if (timer) {
      interval ? clearInterval(timer) : clearTimeout(timer);
      timer = undefined;
    }
  }

  const start = (cb: () => void, delay: number) => {
    clean();
    timer =  interval ? setInterval(cb, delay) : setTimeout(cb, delay);
  }

  return { start, clean }
}

export const useTimerFn = (cb: () => void, options: { delay: number; immediate?: boolean; interval?: boolean }) => {
  let timer: any;

  const isImmediate = options.immediate ?? true;
  const isInterval = options.interval ?? false;

  const clean = () => {
    if (timer) {
      isInterval ? clearInterval(timer) : clearTimeout(timer);
      timer = undefined;
    }
  }

  const start = () => {
    clean();
    timer = isInterval
      ? setInterval(cb, options.delay)
      : setTimeout(cb, options.delay);
  }

  if (isImmediate) {
    cb();
  }

  return { start, clean };
}