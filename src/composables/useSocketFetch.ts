import { ref } from "vue";
import { sleep } from "radash";
import emitter, { type GameEvent } from "@/utils/emitter.ts";
import { createManualPromise } from "@/utils/utils.ts";
import { useTimer } from "@/composables/useTimer.ts";
import { openMessage } from "@/components/ui/GMessage";
import i18n from "@/i18n";

const FETCH_MAP = new Map(); // 所有的请求map

const ROLLBACK_COUNT = 3; // 回退次数

const TIMEOUT = 5e3; // 超时时间

/**
 * 请求、响应数据
 * @param reqEvent 请求事件
 * @param resEvent 响应事件
 * @param data 请求数据
 * @param delay 最小响应时间
 */
export const useSocketFetch = (reqEvent: keyof GameEvent, resEvent: keyof GameEvent, data?: any, delay: number = 0) => {
    const { start: startTimeout, clean: cleanTimeout } = useTimer();
    const { promise, resolve, reject } = createManualPromise<DecodedMessage<any>>();

    const fetchId = ref<number>();
    const rollbacks = ref<number>(0);
    const startTime = performance.now();

    const clean = () => {
        // 清除超时定时器
        cleanTimeout();
        // 注销监听
        emitter.off(resEvent, handleResponse);
        // 删除当前请求记录
        FETCH_MAP.delete(fetchId.value);
    }

    const timeoutFn = () => {
        startTimeout(() => {
            rollbacks.value++;

            openMessage({ type: 'error', message: i18n.global.t(`errors.request_timeout`) });

            if (rollbacks.value >= ROLLBACK_COUNT) {
                clean();
                reject(`request timeout, messageId: ${fetchId.value}, reqEvent: ${ reqEvent }, timeout: ${ TIMEOUT * ROLLBACK_COUNT }`);
                emitter.emit('socket-reconnect');
            } else {
                timeoutFn();
            }
        }, TIMEOUT);
    }

    const handleResponse = async (res: any) => {
        const { header, data } = res;

        if (!(FETCH_MAP.has(header.requestId) && header.requestId === fetchId.value)) return;

        clean();

        const errorCode = header.errCode || data?.errorCode;
        if (!errorCode) {
            if (delay && delay > 0) {
                const waitTime = delay + startTime - performance.now();
                if (waitTime > 0) {
                    await sleep(waitTime);
                }
            }
            resolve(res);
        } else {
            reject(res);
        }
    };

    // 接收数据
    emitter.on(resEvent, handleResponse);

    // 发送请求
    emitter.emit(reqEvent, {
        data: data,
        callback: (requestId?: number) => {
            if (!requestId) {
                reject(`no requestId, reqEvent: ${ reqEvent }`);
                return;
            }
            fetchId.value = requestId;
            FETCH_MAP.set(requestId, promise);
        }
    });

    // 设置超时
    timeoutFn();

    return { fetchId, promise };
};