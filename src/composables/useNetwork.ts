/* this implementation is original ported from https://github.com/logaretm/vue-use-web by <PERSON><PERSON><PERSON><PERSON> */

import { onBeforeUnmount, type ShallowRef } from 'vue';
import { readonly, shallowRef } from 'vue'

export interface NetworkState {
  isSupported: Readonly<boolean>
  /**
   * If the user is currently connected.
   */
  isOnline: Readonly<ShallowRef<boolean>>
  /**
   * The time since the user was last connected.
   */
  offlineAt: Readonly<ShallowRef<number | undefined>>
  /**
   * At this time, if the user is offline and reconnects
   */
  onlineAt: Readonly<ShallowRef<number | undefined>>
}

/**
 * Reactive Network status.
 *
 * @see https://vueuse.org/useNetwork
 *
 * @__NO_SIDE_EFFECTS__
 */
export function useNetwork(): Readonly<NetworkState> {
  const navigator = window?.navigator
  const isSupported = !!navigator && 'connection' in navigator

  const isOnline = shallowRef(true)
  const offlineAt = shallowRef<number | undefined>(undefined)
  const onlineAt = shallowRef<number | undefined>(undefined)

  const connection = isSupported && (navigator as any).connection

  const handleOffline = () => {
    isOnline.value = false
    offlineAt.value = Date.now()
  }

  const handleOnline = () => {
    isOnline.value = true
    offlineAt.value = Date.now()
  }

  function updateNetworkInformation() {
    if (!navigator)
      return

    isOnline.value = navigator.onLine
    offlineAt.value = isOnline.value ? undefined : Date.now()
    onlineAt.value = isOnline.value ? Date.now() : undefined
  }

  const listenerOptions = { passive: true }

  if (window) {
    window.addEventListener('offline', handleOffline, listenerOptions);
    window.addEventListener('online', handleOnline, listenerOptions);
  }

  if (connection) connection.addEventListener('change', updateNetworkInformation, listenerOptions);

  updateNetworkInformation()

  onBeforeUnmount(() => {
    if (window) {
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', handleOnline);
    }

    if (connection) connection.removeEventListener('change', updateNetworkInformation);
  });

  return {
    isSupported,
    isOnline: readonly(isOnline),
    offlineAt: readonly(offlineAt),
    onlineAt: readonly(onlineAt),
  }
}
