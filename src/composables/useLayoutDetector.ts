import { onMounted, onUnmounted } from "vue";
import { debounce } from "radash";
import { storeToRefs } from "pinia";
import { useMainStore } from "@/stores";
import { VERTICAL_RATIO } from "@/constant";

export const useLayoutDetector = () => {
  const { setLayout } = useMainStore();
  const { layout } = storeToRefs(useMainStore());

  let resizeObserver: ResizeObserver | null = null;

  const detectLayout = () => {
    const aspectRatio = window.innerWidth / window.innerHeight;
    const newLayout = aspectRatio > VERTICAL_RATIO ? 'x' : 'y';
    
    if (layout.value !== newLayout) {
      setLayout(newLayout);
    }
  };

  const debouncedDetectLayout = debounce({ delay: 10 }, detectLayout);

  const setupResizeObserver = () => {
    resizeObserver = new ResizeObserver(() => {
      debouncedDetectLayout();
    });

    resizeObserver.observe(document.documentElement);
  };

  onMounted(() => {
    detectLayout();
    setupResizeObserver();
  });

  onUnmounted(() => {
    resizeObserver?.disconnect();
    resizeObserver = null;
  });

  return {
    layout,
    detectLayout
  };
};