import { watch, onUnmounted, isRef, unref, type Ref } from 'vue';


type ResizeHTMLElement = HTMLElement & { __resizeRAF__?: number; __resizeCallback__?: ResizeCallback[] }

// Singleton ResizeObserver instance
const resizeObserver = new ResizeObserver((entries) => {
  entries.forEach((entry) => {
    const target = entry.target as ResizeHTMLElement;
    // Use requestAnimationFrame to debounce resize callbacks
    if (target.__resizeRAF__) {
      cancelAnimationFrame(target.__resizeRAF__);
    }
    target.__resizeRAF__ = requestAnimationFrame(() => {
      target.__resizeCallback__?.forEach(cb => cb(entry));
    });
  });
});

// Type definition for the callback
type ResizeCallback = (entry: ResizeObserverEntry) => void;

// The useResize hook
export function useResize(
  target: Ref<HTMLElement | null> | HTMLElement,
  callback: ResizeCallback,
  options?: {
    isManual?: boolean,
    immediate?: boolean,
  }
) {
  // Store the element to observe
  const element = unref(target);

  // Observe function
  const observe = (el: ResizeHTMLElement) => {
    if (el) {
      // Store callback on the element to avoid creating new observer instances
      if (!el.__resizeCallback__) {
        el.__resizeCallback__ = []
      }
      el.__resizeCallback__.push(callback);
      resizeObserver.observe(el);

      // 如果设置了立马触发一次，这里手动构建ResizeObserverEntry并触发回调函数
      if (options?.immediate) {
        const rect = el.getBoundingClientRect()
        callback({
          target: el,
          contentRect: rect,
          borderBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }],
          contentBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }],
          devicePixelContentBoxSize: [{ blockSize: rect.height, inlineSize: rect.width }]
        } as ResizeObserverEntry)
      }
    }
  };

  // Unobserve function
  const unobserve = (el: ResizeHTMLElement | null) => {
    if (el) {
      resizeObserver.unobserve(el);

      const idx = el.__resizeCallback__?.findIndex(cb => cb === callback);

      if (idx) {
        el.__resizeCallback__?.splice(idx, 1);
      }

      if (!el.__resizeCallback__?.length) {
        delete el.__resizeCallback__;
        delete el.__resizeRAF__;
      }
    }
  };

  const handleBind = () => {
    // Handle initial observation and reactivity
    if (isRef(target)) {
      watch(
        target,
        (newEl, oldEl) => {
          // Cleanup previous element
          unobserve(oldEl as ResizeHTMLElement);
          // Observe new element
          if (newEl) {
            observe(newEl as ResizeHTMLElement);
          }
        },
        { immediate: true }
      );
    } else {
      observe(target);
    }
  }

  if (!options?.isManual) {
    handleBind();
  }

  // Cleanup on component unmount
  onUnmounted(() => {
    const el = unref(element);
    unobserve(el);
  });

  return {
    manual: handleBind,
  }
}