import { ref } from "vue";
import { GameLayout } from "@/constant/index";

export function useOrientation(callback?: (isPortrait: boolean) => void) {
  const { breakpointAspectRatio } = GameLayout;

  const isPortrait = ref(false);

  let raf: number | null = null;

  const cancelUpdate = () => {
    if (raf) {
      cancelAnimationFrame(raf);
      raf = null;
    }
  }

  const isPortraitMode = () => {
    return window.innerWidth / window.innerHeight <= breakpointAspectRatio;
  };

  const handleResize = () => {
    cancelUpdate();
    raf = requestAnimationFrame(() => {
      isPortrait.value = isPortraitMode();
      callback?.(isPortrait.value);
    });
  }

  handleResize();

  window.addEventListener('resize', handleResize);
  window.addEventListener('orientationchange', handleResize);

  return {
    isPortrait,
    clean: () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    }
  }
}