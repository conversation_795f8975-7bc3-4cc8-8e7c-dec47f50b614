import {onUnmounted, ref, watch} from 'vue';
import {useI18n} from "vue-i18n";
import {random, sleep} from "radash";
import {SocketEventCode} from '@/constant';
import {MessageService} from '@/proto/messageService';
import {useTimer} from '@/composables/useTimer';
import {useNetwork} from '@/composables/useNetwork';
import {glog} from '@/utils/utils';
import emitter from '@/utils/emitter';

import { openMessage } from "@/components/ui/GMessage/index.ts"

const HEARTBEAT_TIME = 4e4;

const RECONNECT_MAX_COUNT = 5;

const RECONNECT_DELAY = 200;

// 部分消息太频繁，不打印
const DISABLED_CONSOLE_CODES: number[] = [];

export function useWebSocket(url: string, callback: (ws: WebSocket) => void) {
  const { t } = useI18n();
  const { isOnline } = useNetwork();

  const { start: startHeartbeat, clean: cleanHeartbeat } = useTimer();
  const { start: startReconnect, clean: cleanReconnect } = useTimer();

  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)

  const handlers = new Map<SocketEventCode, MessageHandler>()

  // 是否允许重连 用于控制断开链接后是否自动重连 手动调用disconnect后设为false
  let isEnableReconnect = false;

  // 网络恢复后是否恢复连接 用于控制网络恢复后是否自动重连 手动调用disconnect后设为false
  let isRecoverReconnect = false;

  // 重连次数
  let reconnectCount = 0;

  // 初始化连接
  const connect = async () => {
    isEnableReconnect = true;
    glog('WebSocket start connect');

    socket.value = new WebSocket(url)

    socket.value.binaryType = 'arraybuffer';

    socket.value.onopen = () => {
      glog('WebSocket connected');
      isConnected.value = true;
      reconnectCount = 0;
      sendHeartbeat();
      callback(socket.value!);
    }

    socket.value.onmessage = (event) => {
      const buffer = new Uint8Array(event.data);
      const decodedMessage = MessageService.decode(buffer);

      if (!decodedMessage) {
        console.error('Failed to decode message');
        return;
      }

      const { header, data } = decodedMessage;

      if (!DISABLED_CONSOLE_CODES.includes(header.messageId)) glog(`WebSocket message received`, header, data);

      // 调用对应的处理器
      const handler = handlers.get(header.messageId as SocketEventCode);
      if (handler) {
        handler(decodedMessage);
      }

      const errorCode = header.errCode || data?.errorCode;
      // 服务器报错
      if (errorCode) {
        openMessage({ type: 'error', message: t(`error_code.${errorCode}`, t('error_code.-1')) });

        throw new Error(`Server Error, errCode:${errorCode}`);
      }
    }

    socket.value.onclose = (event) => {
      console.log('WebSocket disconnected')
      console.log("Code:", event.code);
      console.log("Reason:", event.reason);
      console.log("WasClean:", event.wasClean);

      isConnected.value = false;

      cleanHeartbeat();
      cleanReconnect();

      if (isEnableReconnect) {
        attemptReconnect();
      }
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket error:', error)
    }
  }

  // 重连逻辑
  const attemptReconnect = () => {
    if (reconnectCount < RECONNECT_MAX_COUNT) {

      reconnectCount++

      startReconnect(connect, RECONNECT_DELAY);

      glog(`Attempting to reconnect in ${RECONNECT_DELAY}ms...`)
    } else {
      emitter.emit('socket-reconnect-fail');

      glog('Max reconnection attempts reached');
    }
  }

  // 固定间隔发送心跳
  const sendHeartbeat = () => {
    send(SocketEventCode.HEARTBEAT, { sysTime: Date.now() });
    startHeartbeat(sendHeartbeat, HEARTBEAT_TIME);
  }

  // 发送消息 - 简化为只处理公共逻辑
  const send = (eventCode: SocketEventCode, data?: any) => {
    if (isConnected.value && socket.value) {
      const requestId = random(10e6, 10e7 - 1);
      const buffer = MessageService.encode(eventCode, requestId, data);

      socket.value.send(buffer);

      return requestId;
    } else {
      console.log('WebSocket is not connected', eventCode, data);
    }
  }

  // 订阅消息
  const subscribe = (eventCode: SocketEventCode, handler: MessageHandler) => {
    handlers.set(eventCode, handler)
  }

  // 取消订阅
  const unsubscribe = (eventCode: SocketEventCode) => {
    handlers.delete(eventCode)
  }

  // 手动关闭连接(不进行重连)
  const disconnect = (code?: number, reason?: string) => {
    // 客户端主动关闭连接
    console.log("client disconnect", code, reason);

    isEnableReconnect = false;
    isRecoverReconnect = false;
    reconnectCount = 0;

    if (socket.value) {
      socket.value.close(code, reason);
      socket.value = null;
    }
  }

  const reconnect = async () => {
    disconnect();
    await sleep(RECONNECT_DELAY);
    connect();
  }

  window.addEventListener('beforeunload', () => {
    console.log("======beforeunload close socket");
    disconnect(4000, "refresh_or_close");
  });

  // 自动清理
  onUnmounted(() => {
    console.log("======unmount close socket");
    disconnect(4000, "refresh_or_close");
  });
  
  // 断网处理
  watch(isOnline, (val: boolean) => {
    if (val) {
      openMessage({ message: t('network_success') });
      isRecoverReconnect && connect();
    } else {
      openMessage({ type: 'error', message: t('network_error') });
      // 如果socket存在（表明在连接状态），网络恢复后需要重连
      if (socket.value) {
        disconnect();
        isRecoverReconnect = true;
      }
    }
  });

  return {
    socket,
    isConnected,
    connect,
    reconnect,
    disconnect,
    send,
    subscribe,
    unsubscribe
  }
}
