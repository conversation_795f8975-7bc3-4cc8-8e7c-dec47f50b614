import { md5 } from "js-md5";
import { Assets, Spritesheet } from "pixi.js";
import { storeToRefs } from "pinia";
import { useMainStore } from "@/stores";
import manifest from "@/manifest.json";


export interface ISpritesData {
  alias: string;
  sheetData: any;
}

export const MATERIAL_MAP = {
  'chicken2-logo.png': 'gameLog',
  'loading.png': 'loadingLog'
}

const sprites_data: Record<string, ISpritesData> = {
  gameWheel: {
    alias: 'gameWheel.png',
    sheetData: JSON.parse(
      '{"frames":{"air":{"frame":{"x":1,"y":1,"w":369,"h":369},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":369,"h":369},"sourceSize":{"w":369,"h":369}},"bonusFire":{"frame":{"x":372,"y":1,"w":177,"h":201},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":177,"h":201},"sourceSize":{"w":177,"h":201}},"bonusText":{"frame":{"x":372,"y":204,"w":470,"h":130},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":470,"h":130},"sourceSize":{"w":470,"h":130}},"cashoutSumBg":{"frame":{"x":1,"y":372,"w":844,"h":114},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":844,"h":114},"sourceSize":{"w":844,"h":114}},"death":{"frame":{"x":1,"y":488,"w":369,"h":370},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":369,"h":370},"sourceSize":{"w":369,"h":370}},"earth":{"frame":{"x":372,"y":488,"w":369,"h":369},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":369,"h":369},"sourceSize":{"w":369,"h":369}},"earthTip":{"frame":{"x":551,"y":1,"w":44,"h":106},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":44,"h":106},"sourceSize":{"w":44,"h":106}},"fire":{"frame":{"x":844,"y":1,"w":369,"h":369},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":369,"h":369},"sourceSize":{"w":369,"h":369}},"fireTip":{"frame":{"x":597,"y":1,"w":44,"h":108},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":44,"h":108},"sourceSize":{"w":44,"h":108}},"mini_icons":{"frame":{"x":847,"y":372,"w":167,"h":452},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":167,"h":452},"sourceSize":{"w":167,"h":452}},"separator":{"frame":{"x":643,"y":1,"w":10,"h":69},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":10,"h":69},"sourceSize":{"w":10,"h":69}},"water":{"frame":{"x":743,"y":826,"w":369,"h":370},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":369,"h":370},"sourceSize":{"w":369,"h":370}},"waterTip":{"frame":{"x":655,"y":1,"w":44,"h":108},"rotated":false,"trimmed":false,"spriteSourceSize":{"x":0,"y":0,"w":44,"h":108},"sourceSize":{"w":44,"h":108}}},"meta":{"version":"1.0","image":"gameWheel.png","format":"RGBA8888","size":{"w":1214,"h":1197},"scale":"1"}}'
    ),
  },
}

export const loadSpritesResources = async () => {
  return Promise.allSettled(Object.keys(sprites_data).map(async (key) => {
    const data = sprites_data[key];
    const texture = await Assets.load(data.alias);
    const sheet = new Spritesheet(texture.source, data.sheetData);
    await sheet.parse();
    Assets.cache.set(key, sheet);
    return sheet;
  }));
}

export const loadGameFonts = async () => {
  return new Promise((resolve) => {
    const isFontsLoaded = () => document.fonts.check('16px Montserrat');

    const timer = setTimeout(() => resolve(true), 2000);
  
    const fontLoadHandler = () => {
      if (isFontsLoaded()) {
        if (timer) clearTimeout(timer);
        document.fonts.removeEventListener('loadingdone', fontLoadHandler);
        resolve(true);
      }
    }

    document.fonts.addEventListener('loadingdone', fontLoadHandler);

    fontLoadHandler();
  });
}


export const initAssets = async (): Promise<void> => {
  // Init PixiJS assets with this asset manifest
  await Assets.init({ manifest, basePath: "static" });
  await Assets.loadBundle(["default", "sounds", "avatars"]);

  // List all existing bundles names
  const allBundles = manifest.bundles.map((item) => item.name);
  // Start up background loading of all bundles
  Assets.backgroundLoadBundle(allBundles);

  // 加载精灵图
  await loadSpritesResources();

  // 加载游戏字体
  await loadGameFonts();
}

/**
 * 根据本地资源名称获取资源路径
 * @param name 资源名称，注意与MATERIAL_MAP对应（自动命名，需要带后缀，例如av-1.png、avatars/av-1.png）
 */
export const getAssetUrl = (name: string) => {
  const { isMaterial } = useMainStore();

  const texture = Assets.get(name);
  const localAssetUrl = texture?.source?._sourceOrigin || '';

  if (!isMaterial) return localAssetUrl;

  const remoteAssetUrl = Object.keys(MATERIAL_MAP).includes(name) ? getMaterialData(MATERIAL_MAP[name as keyof typeof MATERIAL_MAP]) : '';

  return remoteAssetUrl || localAssetUrl;
}

// 获取远端配置文件地址
export const getMaterialPath = () => {
  const domain = import.meta.env.VITE_COS_NAME;
  const materiatPath = import.meta.env.VITE_MATERIAL_PATH;
  const materiatName = import.meta.env.VITE_MATERIAL_NAME;
  const { dns, gid, mid } = useMainStore();
  const path = md5(`${gid}_${mid}`);
  const protocol = window.location.protocol;
  return `https://${domain}.${dns}${materiatPath}/${path}/${materiatName}`;
}

// 获取远端配置文件数据
export const getRemoteMaterialData = async () => {
  const { isMaterial } = storeToRefs(useMainStore());
  const { setMaterialData } = useMainStore();

  if (!isMaterial.value) return Promise.resolve(true);

  const materialUrl = getMaterialPath();
  
  try {
    const result = await fetch(materialUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Access-Control-Allow-Origin': '*',
      },
    });

    if (!result.ok) {
      throw new Error(`Failed to load material: ${result.statusText}`);
    }

    const data = await result.json();

    setMaterialData(data);
  } catch (error) {
    console.log(`Failed to load material: ${error}`);
  } finally {
    return Promise.resolve(true);
  }
}

// 获取远端配置文件中某个资源
export const getMaterialData = (key: string) => {
  const domain = import.meta.env.VITE_COS_NAME;
  const { dns, materialData } = useMainStore();
  const url = materialData[key];
  return url ? `https://${domain}.${dns}${url}` : '';
}
