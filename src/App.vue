<script setup lang="ts">
import {useI18n} from "vue-i18n";
import {useGameStore, useMainStore, useUserStore} from "@/stores";
import LobbyView from "@/views/LobbyView.vue";

const { setLang, setDns, setMid, setGid, setIsMaterial } = useMainStore();
const { setToken, setMerchantId, setCurrency, setCurrencySymbol } = useUserStore();
const { setGameId } = useGameStore();

const { locale } = useI18n();

const setupQueryParams = async () => {
  const search = window.location.search.replace('?', '');
  const params = search ? search.split('&') : [];

  const queryMap = params.reduce((curr: any, next: string) => {
    const [key, value] = next.split("=");
    curr[key] = window.decodeURIComponent(value);
    return curr;
  }, {});

  const dns = queryMap.dns ?? "";
  const mid = queryMap.mid ?? "";
  const gid = queryMap.gid ?? "";
  const material = queryMap.is_material ?? "";
  const lang = queryMap.lang ?? "en";
  const gameId = queryMap.gameId ?? "";
  const currency = queryMap.currency ?? "USD";
  const currencySymbol = queryMap.currency_symbol ?? "$";
  const merchantId = queryMap.merchantId ?? "1";
  const token = queryMap.token;

  setDns(dns as string);
  setMid(mid as string);
  setGid(gid as string);
  setIsMaterial(material === "true");
  setLang(lang as string);
  setGameId(gameId as string);
  setToken(token as string);
  setCurrency(currency as string);
  setCurrencySymbol(currencySymbol);
  setMerchantId(merchantId as string);

  locale.value = lang as string;
}

// 初始化通过url传入的参数
setupQueryParams();
</script>

<template>
  <LobbyView />
</template>

<style scoped></style>
