import { sound } from "@pixi/sound";
import { BGM, SFX } from "./audio";

let instance: AudioManager | null = null;

export function getAudio(): AudioManager {
  if (!instance) return createAudio();
  return instance;
}

export function createAudio(): AudioManager {
  if (instance) return instance;
  instance = new AudioManager();
  return instance;
}

export function destroyAudio(): void {
  if (!instance) return;
  instance.destroy();
  instance = null;
}

export class AudioManager {
  public bgm: BGM;
  public sfx: SFX;
  public getMasterVolume: () => number;
  public setMasterVolume: (volume: number) => void;

  constructor() {
    this.bgm = new BGM();
    this.sfx = new SFX();

    this.getMasterVolume = () => sound.volumeAll;

    this.setMasterVolume = (volume: number) => {
      sound.volumeAll = volume;
      
      this.bgm.setVolume(volume);
      this.sfx.setVolume(volume);

      if (!volume) {
        sound.muteAll();
      } else {
        sound.unmuteAll();
      }
    };

    document.addEventListener("visibilitychange", this.visibilityChange);
  }

  protected visibilityChange = () => {
    if (document.hidden) {
      sound.pauseAll();
    } else {
      sound.resumeAll();
    }
  };

  public pauseAll(): void {
    sound.pauseAll();
  }

  public resumeAll(): void {
    sound.resumeAll();
  }

  public destroy(): void {
    document.removeEventListener("visibilitychange", this.visibilityChange);
  }
}