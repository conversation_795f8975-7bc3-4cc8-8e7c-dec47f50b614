# useCanvasLayout

`useCanvasLayout` 是一个可组合函数，它会根据视口宽度和设计稿宽度动态计算字体大小。它还会设置一个 ResizeObserver，以便在视口大小发生变化时更新字体大小。

## 使用方法

```ts
import {useCanvasLayout} from "@/composables/useCanvasLayout.ts";

/** 设计稿375px，标准尺寸10px */
useCanvasLayout(375, 10);
```

## 参数

- `designDraftWidth`：设计稿宽度，默认为 375px
- `standardSize`：标准尺寸，默认为 10px
- `min`：最小缩放倍数，默认为 1
- `max`：最大缩放倍数，默认为 2

## 例子

如在一个宽度为`375px`的设计稿中，有一个按钮的宽度为`100px`。

我们使用`10px`作为标准尺寸，我们在css中应该设置按钮的宽度为`100px / 10px = 10rem`。

这样在页面宽度为375的时候，font-size为10px，按钮的宽度为10px * 10rem = 100px。

在页面宽度为750的时候，font-size为20px，按钮的宽度为20px * 10rem = 200px。
