import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { sleep } from "radash";
import { getAudio } from "@/AudioManager";

export const useGameSlotAnimStore = defineStore('gameSlotAnim', () => {
  const isTwistRolling = ref(false);
  const isBonusRolling = ref(false);
  const isBonusSlot = ref(false);
  
  // 是否在动画中
  const isRolling = computed(() => {
    return isTwistRolling.value || isBonusRolling.value || isBonusSlot.value;
  });

  // 显示转轮槽结果
  const showTwistItem = async ({ isAutoPlay }: { isAutoPlay: boolean }): Promise<void> => {
    isTwistRolling.value = true;
    
    getAudio().sfx.play('sounds/spin.mp3', { volume: 1 });

    await sleep(isAutoPlay ? 800 : 1500);

    getAudio().sfx.stop('sounds/spin.mp3');

    isTwistRolling.value = false;
  }

  // 显示奖金槽结果
  const showBonusItem = async (): Promise<void> => {
    isBonusSlot.value = true;

    await sleep(800);

    isBonusRolling.value = true;

    await sleep(1500);

    isBonusRolling.value = false;

    await sleep(1300);

    isBonusSlot.value = false;
  }

  return {
    isTwistRolling,
    isBonusRolling,
    isBonusSlot,
    isRolling,
    showTwistItem,
    showBonusItem,
  }
});