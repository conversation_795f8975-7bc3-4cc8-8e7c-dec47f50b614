import { computed, ref } from "vue";
import { defineStore } from 'pinia';
import { SYSTEM_ERROR_CODE } from "@/constant";
import { languages } from "@/i18n/language";

export const useMainStore = defineStore('main', () => {
  const dns = ref('');
  const mid = ref('');
  const gid = ref('');
  const isMaterial = ref(false);
  const materialData = ref<Record<string, any>>({});

  const setDns = (newDns: string) => {
    dns.value = newDns;
  }
  const setMid = (newMid: string) => {
    mid.value = newMid;
  }
  const setGid = (newGid: string) => {
    gid.value = newGid;
  }
  const setIsMaterial = (status: boolean) => {
    isMaterial.value = status;
  }
  const setMaterialData = (data: Record<string, any>) => {
    materialData.value = data;
  }
  const getSocketUrl = () => {
    return `${import.meta.env.VITE_SERVICE_ORIGIN}${dns.value ? '.' : ''}${dns.value}${import.meta.env.VITE_SOCKET_URL}`;
  }
  

  const systemError = ref<SYSTEM_ERROR_CODE>(SYSTEM_ERROR_CODE.NONE);
  const isSystemError = computed(() => {
    return systemError.value !== SYSTEM_ERROR_CODE.NONE;
  })
  const setSystemError = (code: SYSTEM_ERROR_CODE) => {
    systemError.value = code;
  }

  const soundEnable = ref(false);
  const musicEnable = ref(false);
  const spaceEnable = ref(true);

  const modalVisible = ref(false);
  const setModalVisible = (visible: boolean) => {
    modalVisible.value = visible;
  }

  const mainVolume = ref(1);
  const setMainVolume = (newVolume: number) => {
    mainVolume.value = newVolume;
  }

  const soundVolume = ref(1);
  const setSoundVolume = (newVolume: number) => {
    soundVolume.value = newVolume;
  }

  const bmgVolume = ref(1);
  const setBmgVolume = (newVolume: number) => {
    bmgVolume.value = newVolume;
  }

  const layout = ref<'x' | 'y'>('x');
  const isVertical = computed(() => layout.value === 'y');
  const isHorizontal = computed(() => layout.value === 'x');
  const setLayout = (newLayout: 'x' | 'y') => {
    layout.value = newLayout;
  }

  // 投注面板的缩放比例
  const betPanelScale = ref(1);
  const setBetPanelScale = (scale: number) => {
    betPanelScale.value = scale;
  }

  const langs = ref(languages);
  const lang = ref('en');
  const restrictLang = () => {
    // 做一下判断，如果设置的语言不再支持的语言列表中，回退为第一个
    if (langs.value.find(item => item.value === lang.value)) {
      return;
    }
    lang.value = langs.value[0].value;
  };
  const setLangs = (newLangs: string[]) => {
    if (!newLangs || !newLangs.length) {
      newLangs = [languages[0].value];
    }
    langs.value = languages.filter(item => newLangs.includes(item.value));
    restrictLang();
  }
  const setLang = (newLang: string) => {
    lang.value = newLang;
    restrictLang();
  }

  return {
    dns, setDns, gid, setGid, mid, setMid, isMaterial, setIsMaterial, materialData, setMaterialData, getSocketUrl,
    systemError, isSystemError, setSystemError,
    soundEnable, musicEnable, spaceEnable,
    modalVisible, setModalVisible,
    mainVolume, setMainVolume,
    bmgVolume, setBmgVolume,
    soundVolume, setSoundVolume,
    layout, isVertical, isHorizontal, setLayout,
    betPanelScale, setBetPanelScale,
    langs, setLangs, lang, setLang,
  };
},{
  persist:{
    key:'main',
    pick:['soundEnable', 'musicEnable', 'spaceEnable'],
  }
})