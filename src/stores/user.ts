import { ref } from "vue";
import { defineStore } from "pinia";
import { random } from "radash";

export const useUserStore = defineStore('user', () => {
  const token = ref<string>();
  const setToken = (newToken: string) => {
    token.value = newToken;
  }

  const userName = ref('');
  const setUserName = (name: string) => {
    userName.value = name;
  }

  const nickname = ref('Aqua Different Bird');
  const setNickname = (name: string) => {
    nickname.value = name;
  }

  // 用户头像
  const avatar = ref(random(1, 12).toString());
  const setAvatar = (newAvatar: string) => {
    avatar.value = newAvatar;
  }

  const merchantId = ref<string>();
  const setMerchantId = (newMerchantId: string) => {
    merchantId.value = newMerchantId;
  }

  const userId = ref<string>();
  const setUserId = (newUserId: string) => {
    userId.value = newUserId;
  }

  const currency = ref<string>('USD');
  const setCurrency = (newCurrency: string) => {
    currency.value = newCurrency;
  }

  // 'PKR' ? 'Rs.' : '$';
  const currencySymbol = ref<string>('');
  const setCurrencySymbol = (newCurrencySymbol: string) => {
    currencySymbol.value = newCurrencySymbol;
  }

  // 用户余额
  const balance = ref(0);
  const setBalance = (newBalance: number) => {
    balance.value = newBalance;
  }

  const clientSeed = ref<string>();
  const setClientSeed = (seed: string) => {
    clientSeed.value = seed;
  }

  const serverSeed = ref<string>();
  const setServerSeed = (seed: string) => {
    serverSeed.value = seed;
  }

  return {
    token, setToken,
    userName, setUserName,
    nickname, setNickname,
    avatar, setAvatar,
    merchantId, setMerchantId,
    userId, setUserId,
    currency, setCurrency,
    currencySymbol, setCurrencySymbol,
    balance, setBalance,
    clientSeed, setClientSeed, serverSeed, setServerSeed,
  }
},{
  persist:{
    key:'user',
    pick:['token']
  }
});