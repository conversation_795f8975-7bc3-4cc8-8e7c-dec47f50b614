import { reactive, readonly, watch } from "vue";
import { defineStore } from "pinia";
import { useMainStore } from "./main";
import { useGameStore } from "./game";
import { useUserStore } from "./user";
import emitter from "@/utils/emitter";

export const defaultSetting = readonly({
  rounds: 5,
  cashDecreasesBy: 0,
  coeffIncreasesBy: 0
});

export const defaultState = readonly({
  processing: false,
  paused: false,
  currentRound: 0,
  startBalance: 0,
  totalCoeff: 0
});

export const useGameAutoPlayStore = defineStore('gameAutoPlay', () => {

  const settings = reactive({ ...defaultSetting });

  const state = reactive({ ...defaultState });

  const stop = () => {
    Object.assign(state, {
      processing: false,
      paused: false,
      currentRound: 0,
      startBalance: 0,
      totalCoeff: 0,
    });
  }

  const handleBet = () => {
    if (state.processing && !state.paused) {
      state.currentRound++
      if (state.currentRound < settings.rounds) {
        const totalCoeff = useGameStore().gameData.coeff;
        const currentBalance = useUserStore().balance;

        // 更新总系数
        state.totalCoeff = totalCoeff;
  
        // 检查停止条件
        if (
          settings.cashDecreasesBy > 0 &&
          state.startBalance >= currentBalance + settings.cashDecreasesBy
        ) {
          stop();
          return;
        }
  
        if (
          settings.coeffIncreasesBy > 0 &&
          state.totalCoeff >= settings.coeffIncreasesBy
        ) {
          stop();
          return;
        }
      } else {
        stop();
      }
    }

    if (state.currentRound >= settings.rounds && state.paused) {
      stop();
      return;
    }

    return state.processing && !state.paused;
  }

  const start = (options = { rounds: Infinity, cashDecreasesBy: 0, coeffIncreasesBy: 0 }) => {
    Object.assign(settings, options);
    Object.assign(state, {
      startBalance: useUserStore().balance,
      paused: false,
      processing: true,
    });
  }

  const pause = () => {
    if (state.currentRound === settings.rounds) {
      stop();
      return;
    }

    state.paused = true;
  }

  const resume = () => {
    state.paused = false;
  }

  // 监听游戏自动开始
  watch(() => state.processing && !state.paused, (bool: boolean) => {
    if (bool) {
      emitter.emit('handle-game-play');
    }
  });

  // 监听设备大小切换后停止自动投注
  watch(() => useMainStore().isVertical, () => {
    if (state.processing) {
      stop();
    }
  });

  // 监听断线后停止自动投注
  watch(() => useGameStore().isSocketOnline, (bool: boolean) => {
    if (!bool && state.processing) {
      stop();
    }
  });

  return {
    state,
    settings,
    handleBet,
    start,
    stop,
    pause,
    resume,
  }
});