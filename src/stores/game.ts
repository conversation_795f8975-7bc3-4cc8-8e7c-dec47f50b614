import Big from "big.js";
import { sleep } from "radash";
import { computed, ref } from "vue";
import { defineStore, storeToRefs } from "pinia";
import { defaultGameData, TwistCoeffs, TwistElement, type CircleElement, type TCircleElement } from "@/constant/game";
import { clamp } from "@/utils/utils";
import protoRoot from "@/proto/index";
import { getAudio } from "@/AudioManager";
import { useUserStore } from "./user";
import { useGameSlotAnimStore } from "./game-slot-anim";
import { useGameAutoPlayStore } from "./game-autoplay";

export const useGameStore = defineStore("game", () => {
    const isInitLoaded = ref(false);
    const setIsInitLoaded = (loaded: boolean) => {
        isInitLoaded.value = loaded;
    }

    const isSocketOnline = ref(false);
    const setIsSocketOnline = (isOnline: boolean) => {
        isSocketOnline.value = isOnline;
    }

    // 是否初始化进入房间
    const isJoinRoom = ref(false);
    const setIsJoinRoom = (val: boolean) => {
        isJoinRoom.value = val;
    };

    // 服务器时间
    const serverTime = ref<number>(0);
    const setServerTime = (time: number) => {
        serverTime.value = time;
    }

    // 游戏id
    const gameId = ref<string>("");
    const setGameId = (id: string) => {
        gameId.value = id;
    }

    // 期数
    const gameRoundId = ref<string>("000");
    const setGameRoundId = (id: string) => {
        gameRoundId.value = id;
    }

    // 最小投注金额
    const gameMinBet = ref(0.01);
    const setGameMinBet = (min: number) => {
        gameMinBet.value = min;
    };

    // 最大投注金额
    const gameMaxBet = ref(200);
    const setGameMaxBet = (max: number) => {
        gameMaxBet.value = max;
    };

    // 一局最大获奖金额
    const gameMaxWin = ref(20000);
    const setGameMaxWin = (max: number) => {
        gameMaxWin.value = max;
    };

    const twistCoeffs = ref<TCircleElement<number[]>>(TwistCoeffs);

    const gameData = ref<GameData>({ ...defaultGameData });
    const circleSteps = computed<TCircleElement<number>>(() => {
        return {
            [TwistElement.FIRE]: gameData.value.progressFire || 0,
            [TwistElement.EARTH]: gameData.value.progressEarth || 0,
            [TwistElement.WATER]: gameData.value.progressWater || 0
        }
    });

    // progress coeff isFinished
    const updateTwistStep = (steps: number[]) => {
        // 更新转盘进度
        gameData.value.progressWater = steps?.[0] ?? 0;
        gameData.value.progressEarth = steps?.[1] ?? 0;
        gameData.value.progressFire = steps?.[2] ?? 0;

        // 更新是否都为0
        gameData.value.isFinished = !steps?.some(step => step > 0);

        // 更新当前总倍率
        const getCircleCoeff = (element: CircleElement, step: number) => {
            const coeffs = twistCoeffs.value[element] || [];
            if (!coeffs || !coeffs.length || step <= 0) return 0;
            return Math.max(coeffs[step - 1], 0);
        }
        const currWaterCoeff = getCircleCoeff(TwistElement.WATER, steps?.[0]);
        const currEarthCoeff = getCircleCoeff(TwistElement.EARTH, steps?.[1]);
        const currFireCoeff = getCircleCoeff(TwistElement.FIRE, steps?.[2]);
        gameData.value.coeff = new Big(currWaterCoeff).plus(currEarthCoeff).plus(currFireCoeff).toNumber();
    }
 
    const resetGameData = () => {
        gameData.value = { ...defaultGameData }
    }

    // 是否正在等待请求响应
    const isFetchPending = ref(false);

    // 初始化游戏信息
    const initGameInfo = (data: protoRoot.EnterRoomResponse) => {
        // 更新投注配置信息
        setGameMinBet(data.minBetAmount || 0.01);
        setGameMaxBet(data.maxBetAmount || 200);
        setGameMaxWin(data.maxWinAmount || 20000);

        // 更新倍率
        twistCoeffs.value = {
            [TwistElement.WATER]: data?.multiples_1 ?? TwistCoeffs[TwistElement.WATER],
            [TwistElement.EARTH]: data?.multiples_2 ?? TwistCoeffs[TwistElement.EARTH],
            [TwistElement.FIRE]: data?.multiples_3 ?? TwistCoeffs[TwistElement.FIRE],
        }

        // 更新当前游戏进度相关信息
        updateTwistStep(data?.steps);

        // 更新当前下注金额
        gameData.value.betAmount = gameData.value.isFinished ? data.defaultBetAmount : data.betAmount;
        // 更新cashout相关数据
        gameData.value.isCashout = false;
        gameData.value.isCanCashOut = data.canCashOut ?? false;
    };

    // 投注后更新游戏信息
    const updateBetInfo = async (data: protoRoot.StartGameResponse, hasBonus: boolean) => {
        const { balance } = storeToRefs(useUserStore());
        const { showTwistItem } = useGameSlotAnimStore();
        const { state } = useGameAutoPlayStore();

        // 更新中奖元素
        gameData.value.outcome = data.element;

        // 更新提现相关状态
        gameData.value.isCashout = hasBonus ? true : false;
        gameData.value.isCanCashOut = data.canCashOut ?? false;

        // 更新total win
        if (gameData.value.isFinished) {
            gameData.value.totalWinAmount = 0;
        }
        
        // 更新余额
        if (!hasBonus) {
            balance.value = data.userGold;
        }

        // 更新Slot动画
        await showTwistItem({ isAutoPlay: state.processing });

        // 更新进度相关信息
        updateTwistStep(data.steps);
    }

    // 投注后更新bonus信息
    const updateBonusInfo = async (data: protoRoot.StartGameResponse) => {
        const { balance } = storeToRefs(useUserStore());
        const { showBonusItem } = useGameSlotAnimStore();

        await sleep(500);

        const currStep = circleSteps.value[data.element as CircleElement];

        const setCircleStep = (element: CircleElement, step: number) => {
            const maxStep = twistCoeffs.value[element].length;
            switch (element) {
                case TwistElement.FIRE:
                    gameData.value.progressFire = clamp(step, 0, maxStep);
                    break;
                case TwistElement.EARTH:
                    gameData.value.progressEarth = clamp(step, 0, maxStep);
                    break;
                case TwistElement.WATER:
                    gameData.value.progressWater = clamp(step, 0, maxStep);
                    break;
            }
        }

        // 更新 环形 进度
        if (data.element === TwistElement.FIRE) {
            setCircleStep(data.element, 0);
        } else {
            setCircleStep(data.element, Math.max(currStep - 1, 0));
        }

        // 更新 bonus倍率
        gameData.value.bonusCoeff = data.mul;

        // 更新余额、最近一次获利金额
        setTimeout(() => {
            balance.value = data.userGold;
            gameData.value.lastWinAmount = data.winAmount;
            gameData.value.totalWinAmount = new Big(gameData.value.totalWinAmount).plus(data.winAmount).toNumber();
        }, 1500);

        // 音效播放 & bonus slot动画
        if (data.element == TwistElement.FIRE) {
            getAudio().sfx.play('sounds/bonus.mp3');
            await showBonusItem();
        } else {
            getAudio().sfx.play('sounds/lastLevelCashout.mp3');
        }
    }

    // full cashout后更新游戏信息
    const updateFullCashout = (data: protoRoot.CashOutResponse) => {
        const { balance } = storeToRefs(useUserStore());
        updateTwistStep([0, 0, 0]);
        gameData.value.isCashout = true;
        gameData.value.isCanCashOut = false;
        gameData.value.lastWinAmount = data.winAmount;
        gameData.value.totalWinAmount = new Big(gameData.value.totalWinAmount).plus(data.winAmount).toNumber();
        balance.value = data.userGold;
    }

    // part cashout后更新游戏信息
    const updatePartCashout = (data: protoRoot.PartCashOutResponse) => {
        const { balance } = storeToRefs(useUserStore());
        updateTwistStep(data.steps);
        gameData.value.isCashout = true;
        gameData.value.isCanCashOut = false;
        setTimeout(() => {
            balance.value = data.userGold;
            gameData.value.lastWinAmount = data.winAmount;
            gameData.value.totalWinAmount = new Big(gameData.value.totalWinAmount).plus(data.winAmount).toNumber();
        }, 1500);
    }

    return {
        isInitLoaded, setIsInitLoaded,
        isSocketOnline, setIsSocketOnline,
        isJoinRoom, setIsJoinRoom,
        gameId, setGameId,
        gameRoundId, setGameRoundId,
        gameMinBet, setGameMinBet,
        gameMaxBet, setGameMaxBet,
        gameMaxWin, setGameMaxWin,
        initGameInfo, updateBetInfo, updateBonusInfo, updateFullCashout, updatePartCashout,
        gameData, circleSteps, twistCoeffs, resetGameData,
        isFetchPending,
    };
});