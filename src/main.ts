import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

import { gsap } from "gsap";
import { PixiPlugin } from "gsap/PixiPlugin";
import CustomEase from "gsap/CustomEase";
import * as PIXI from "pixi.js";

import Vue3Odometer from "vue3-odometer"
import "odometer/themes/odometer-theme-default.css"

import i18n from "@/i18n/index";

import "@/assets/style/style.css"

import App from "./App.vue";

gsap.registerPlugin(CustomEase);
gsap.registerPlugin(PixiPlugin);
PixiPlugin.registerPIXI(PIXI);

const app = createApp(App);

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

app.component('Vue3Odometer', Vue3Odometer);

app.use(pinia);
app.use(i18n);
app.mount("#app");
