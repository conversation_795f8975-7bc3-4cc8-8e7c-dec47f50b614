import mitt, { type Emitter } from 'mitt';

const tickEmitter: Emitter<{ 'tick': () => void }> = mitt();

export const addTick = (callback: (delta: any) => void) => {
  tickEmitter.on('tick', callback);
}

export const removeTick = (callback: (delta: any) => void) => {
  tickEmitter.off('tick', callback);
}

export const emitTick = (delta: any) => {
  tickEmitter.emit('tick', delta);
}

export default tickEmitter;