import gsap from "gsap";
import { Container, Sprite, Text, TextStyle, Texture } from "pixi.js";
import { formatAmount } from "@/utils/utils";


export class PartCashout extends Container {
  private currency: string;
  private sum: Text;

  constructor(currency: string) {
    super({ scale: 0, y: 9 });

    this.currency = currency;

    this.addChild(new Sprite({
      texture: Texture.from('cashoutSumBg'),
      scale: 0.5,
      anchor: 0.5,
    }));

    this.sum = new Text({
      anchor: 0.5,
      y: -1,
      style: new TextStyle({
        fontFamily: ['Montserrat', 'sans-serif'],
        fontSize: 28,
        fill: '#ffffff',
        fontWeight: '800',
        padding: 14,
        letterSpacing: -0.3,
        dropShadow: {
          alpha: 0.8,
          angle: Math.PI / 2,
          blur: 16,
          distance: 1,
          color: '#000000',
        },
      }),
    });

    this.addChild(this.sum);
  }

  private startAnimation(from: { x: number, y: number }, to: { x: number, y: number }) {
    const midPoint = {
      x: Math.min((to.x + from.x) / 2, from.x + 70),
      y: from.y - 100,
    }

    gsap.killTweensOf(this);

    this.x = from.x;
    this.y = from.y;

    const tl = gsap.timeline();

    tl.to(this, { duration: 0.4, pixi: { scale: 1, alpha: 1 }, ease: 'power1.inOut' });
    tl.to(this, { duration: 0.7, pixi: { x: midPoint.x, y: midPoint.y }, ease: 'power2.in' });
    tl.to(this, { duration: 0.5, pixi: { x: to.x }, ease: 'sine.out' });
    tl.to(this, { duration: 0.5, pixi: { y: to.y }, ease: 'sine.in' }, '<');
    tl.to(this, { duration: 0.25, pixi: { scaleX: 0.2, scaleY: 0.2, alpha: 0.3 }, delay: 0.25 }, '<');
    tl.to(this, { duration: 0.1, pixi: { alpha: 0 } });
  }

  public update(amount: number, to: { x: number; y: number }) {
    this.sum.text = `CASHOUT ${formatAmount(amount, 2, true)} ${this.currency}`;
    this.startAnimation({ x: 0, y: 9 }, to);
  }
}