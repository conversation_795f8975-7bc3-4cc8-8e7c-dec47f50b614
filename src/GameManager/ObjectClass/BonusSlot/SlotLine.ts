import { gsap } from "gsap";
import CustomEase from "gsap/CustomEase";
import { shuffle, sleep } from "radash";
import { Container, type DestroyOptions } from "pixi.js";
import { TwistFireBonus, type ITwistSlotData } from "@/constant/game";
import { SlotSymbol } from "./SlotSymbol";

// 生成指定数量的随机元素（基于 ss）
const generateItems = (count: number) => {
  const result: ITwistSlotData[] = [];
  while (result.length < count) {
    result.push(...TwistFireBonus.map((name) => ({ name, posY: 0 })));
  }
  return shuffle(result).slice(0, count);
};

// 生成奖金项目（基于 sI）
const generateBonusItems = (count: number, winItem?: number) => {
  const items = generateItems(count);
  items[3] = { name: winItem || TwistFireBonus[0], posY: 0 };
  items.forEach((item, index) => {
    item.posY = (-3 + index) * 206;
  });
  return items;
};

export interface ISlotOptions {
  isRolling: boolean;
  winItem?: number;
}

export class SlotLine extends Container {
  private isRolling: boolean = false;
  private isWinAnimation: boolean = false;
  private winItem?: number;

  private offsetY: number = 0;
  private tween?: gsap.core.Tween;

  private items: ITwistSlotData[];

  constructor(options: ISlotOptions) {
    super()

    this.isRolling = options.isRolling;
    this.winItem = options.winItem;
    this.items = generateBonusItems(25, options.winItem);

    this.addChild(...this.items.map(item => new SlotSymbol(item)));
  }

  private generateNewBonusItems() {
    const newItems = generateItems(20);
    const startY = this.items[0]?.posY || 0;

    newItems.forEach((item, idx) => {
      item.posY = startY + 206 * (idx - newItems.length);
    });

    newItems[3] = {
      name: this.winItem ?? TwistFireBonus[0],
      posY: newItems[2].posY + 206,
    }
    
    this.items = [...newItems, ...this.items.slice(0, -20)];
  }

  private updateItems() {
    this.children.forEach((child, idx) => {
      const item = this.items[idx];
      const slot = child as SlotSymbol;
      slot.update({
        item,
        isWinItem: idx === 3 && this.isWinAnimation,
        isBlur: this.isRolling,
        isWinAnimation: this.isWinAnimation,
      });
    });
  }

  private startAnimate() {
    this.tween?.kill();

    const targetY = 4120 + this.offsetY;
  
    this.tween = gsap.to(this, {
      pixi: { y: targetY },
      ease: CustomEase.create('custom', 'M0,0 C0.199,0 0.251,0.597 0.387,0.803 0.529,1.029 0.822,1 1,1'),
      duration: 1.5,
      onComplete: () => {
        this.isWinAnimation = true;
        this.updateItems();
      }
    });

    this.offsetY = targetY;
  }

  private async startRolling() {
    this.isRolling = true;
    await sleep(1500);
    this.isRolling = false;
  }

  public update(options: ISlotOptions) {
    const { winItem, isRolling } = options;
    this.winItem = winItem;
    
    if (this.isRolling !== isRolling && isRolling) {
      this.isWinAnimation = false;
      this.startRolling();
      this.generateNewBonusItems();
      this.updateItems();
      this.startAnimate();
    }
  }

  override destroy(options?: DestroyOptions): void {
    this.tween?.kill();
    this.tween = undefined;
    super.destroy(options);
  }
}