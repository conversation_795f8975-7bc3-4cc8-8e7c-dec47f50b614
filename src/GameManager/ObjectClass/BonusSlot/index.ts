import { gsap } from "gsap";
import { Container, Graphics, Sprite, Texture, type DestroyOptions } from "pixi.js";
import { addTick, removeTick } from "@/GameManager/GlobalTick";
import { SlotLine } from './SlotLine';

export interface ISlotOptions {
  isRolling: boolean;
  isBonusSlot: boolean;
  winItem?: number;
}

export class BonusSlot extends Container {
  private slotLineWrapper: Container;
  private slotLineRef: SlotLine;
  private circleMask: Graphics;
  private centerRef: Sprite;
  private bonusFireRef: Sprite;
  private bonusTextRef: Sprite;

  private isRolling: boolean;
  private isBonusSlot: boolean;

  constructor(options: ISlotOptions) {
    super({ scale: 0.5, y: 7 });

    this.isRolling = options.isRolling;
    this.isBonusSlot = options.isBonusSlot;

    this.addChild(new Sprite({
      texture: Texture.from('bonusSlotBg.png'),
      anchor: 0.5,
    }));

    this.circleMask = new Graphics();
    this.addChild(this.circleMask);

    this.slotLineWrapper = new Container();
    this.slotLineWrapper.mask = this.circleMask;

    this.slotLineRef = new SlotLine({ isRolling: options.isRolling });
    this.slotLineWrapper.addChild(this.slotLineRef);
    this.addChild(this.slotLineWrapper);

    this.centerRef = new Sprite({ texture: Texture.from('bonusCenterRed.png'), anchor: 0.5 });
    this.bonusFireRef = new Sprite({ texture: Texture.from('bonusFire'), anchor: 0.5, y: -80 });
    this.bonusTextRef = new Sprite({ texture: Texture.from('bonusText'), anchor: 0.5, y: 80 });
    this.addChild(this.centerRef, this.bonusFireRef, this.bonusTextRef);

    this.reset();

    addTick(this.draw);
  }

  private draw = () => {
    this.circleMask
    .clear()
    .circle(0, 0, 277)
    .fill({ color: 0xffffff, alpha: 0.05 });
  }

  private reset() {
    this.alpha = 0;
    this.visible = false;
    this.centerRef.alpha = 1;
    this.bonusFireRef.alpha = 1;
    this.bonusTextRef.alpha = 1;
    this.bonusTextRef.scale.set(0.2);
    this.slotLineWrapper.alpha = 0;
  }

  public update(options: ISlotOptions) {
    const { isRolling, isBonusSlot, winItem } = options;

    if (isBonusSlot !== this.isBonusSlot) {
      this.isBonusSlot = isBonusSlot;
      if (isBonusSlot) {
        this.reset();
        gsap.to(this, { duration: 0.2, pixi: { alpha: 1, visible: true } });
        gsap.to(this.bonusTextRef, { duration: 0.2, pixi: { scale: 1 } });
        gsap.to(this.bonusFireRef, { duration: 0.2, pixi: { alpha: 0.9 }, yoyo: true, repeat: -1 });
        gsap.to(this.centerRef, { duration: 0.2, pixi: { alpha: 0.8 }, yoyo: true, repeat: -1 });
      } else {
        gsap.to(this, { duration: 0.2, pixi: { alpha: 0 }, onComplete: () => this.reset() });
      }
    }

    if (isRolling !== this.isRolling) {
      this.isRolling = isRolling;
      if (isRolling) {
        gsap.killTweensOf(this.bonusFireRef);
        gsap.to(this.bonusTextRef, { duration: 0.2, pixi: { alpha: 0 } });
        gsap.to(this.bonusFireRef, { duration: 0.2, pixi: { alpha: 0 } });
        gsap.to(this.slotLineWrapper, { duration: 0.2, pixi: { alpha: 1 } });
      } else {
        gsap.killTweensOf(this.centerRef);
        gsap.to(this.centerRef, { duration: 0.2, pixi: { alpha: 1 } });
      }

      this.slotLineRef.update({ isRolling, winItem });
    }
  }

  override destroy(options?: DestroyOptions): void {
    removeTick(this.draw);
    super.destroy(options);
  }
}