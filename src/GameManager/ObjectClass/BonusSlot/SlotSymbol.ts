import { gsap } from "gsap";
import { Sprite, Texture } from "pixi.js";
import type { ITwistSlotData } from "@/constant/game";

export interface iSlotSymbolOptions {
  item: ITwistSlotData;
  isBlur?: boolean;
  isWinAnimation?: boolean;
  isWinItem?: boolean;
}

export class SlotSymbol extends Sprite {
  constructor(item: ITwistSlotData) {
    super({
      anchor: 0.5,
      texture: Texture.WHITE,
      y: item.posY,
    });

    this.update({ item });
  }

  update(options: iSlotSymbolOptions) {
    const { item, isBlur, isWinAnimation, isWinItem } = options;

    this.position.y = item.posY;
    this.texture = Texture.from(isBlur ? `x${item.name}Blur.png` : `x${item.name}.png`);

    if (isWinItem && isWinAnimation) {
      gsap.to(this, { duration: 0.2, pixi: { scaleX: 1.2, scaleY: 1.2 } });
    }
    if (!isWinItem && isWinAnimation) {
      gsap.to(this, { duration: 0.2, pixi: { alpha: 0.2 } });
    }
  }
}