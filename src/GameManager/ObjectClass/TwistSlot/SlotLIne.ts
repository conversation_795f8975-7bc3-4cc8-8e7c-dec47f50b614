import { gsap } from "gsap";
import CustomEase from "gsap/CustomEase";
import { shuffle, sleep } from "radash";
import { Container, type DestroyOptions } from "pixi.js";
import { TwistElement } from "@/constant/game";
import { SlotSymbol } from "./SlotSymbol";

const twistElemts = [TwistElement.FIRE, TwistElement.EARTH, TwistElement.WATER, TwistElement.AIR, TwistElement.DEATH];

// 生成指定数量的随机元素（基于 ss）
const generateItems = (count: number) => {
  const result = [];
  while (result.length < count) {
    result.push(...twistElemts.map((el) => ({ name: el, posY: 0 })));
  }
  return shuffle(result).slice(0, count);
};

// 生成包含胜利项目的元素列表（基于 sg）
const generateItemsWithWinItem = (count: number, winItem?: TwistElement) => {
  const items = generateItems(count);

  items[3] = { name: winItem ?? TwistElement.FIRE, posY: 0 };

  items.forEach((item, index) => {
    item.posY = (-3 + index) * 396;
  });

  return items;
};

export interface ISlotOptions {
  isRolling: boolean;
  isAutoplay: boolean;
  winItem?: TwistElement;
}

export class SlotLine extends Container {
  private isAutoplay: boolean = false;
  private isRolling: boolean = false;
  private isWinAnimation: boolean = false;
  private winItem?: TwistElement;

  private offsetY: number = 0;
  private tween?: gsap.core.Tween;

  private items: any[] = [];

  constructor (options: ISlotOptions) {
    super();

    this.isAutoplay = options.isAutoplay;
    this.isRolling = options.isRolling;
    this.winItem = options.winItem;
    this.items = generateItemsWithWinItem(15, options.winItem);

    this.addChild(...this.items.map(item => new SlotSymbol(item)));
  }

  private generateNewItems() {
    const newItems = generateItems(10);
    const startY = this.items[0]?.posY || 0;

    newItems.forEach((item, idx) => {
      item.posY = startY + 396 * (idx - newItems.length);
    });

    newItems[3] = {
      name: this.winItem ?? twistElemts[0],
      posY: newItems[2].posY + 396
    };

    this.items = [ ...newItems, ...this.items.slice(0, -10) ];
  }

  private updateItems() {
    this.children.forEach((child, idx) => {
      const item = this.items[idx];
      const slot = child as SlotSymbol;
      slot.update({
        item,
        isWinItem: idx === 3 && this.isWinAnimation
      });
    });
  }

  private startAnimate() {
    this.tween?.kill();

    const targetY = 3960 + this.offsetY;

    this.tween = gsap.to(this, {
      pixi: { y: targetY },
      ease: CustomEase.create('custom', 'M0,0 C0.199,0 0.251,0.597 0.387,0.803 0.529,1.029 0.822,1 1,1'),
      duration: this.isAutoplay ? 0.8 : 1.5,
      onComplete: () => {
        this.isWinAnimation = true;
        this.updateItems();
      },
    });

    this.offsetY = targetY;
  }

  private async startRolling() {
    this.isRolling = true;
    await sleep(this.isAutoplay ? 800 : 1500);
    this.isRolling = false;
  }

  public update(options: ISlotOptions) {
    const { winItem, isRolling, isAutoplay } = options;
    this.winItem = winItem;
    this.isAutoplay = isAutoplay;
    if (this.isRolling !== isRolling && isRolling) {
      this.isWinAnimation = false;
      this.startRolling();
      this.generateNewItems();
      this.updateItems();
      this.startAnimate();
    }
  }

  override destroy(options?: DestroyOptions): void {
    this.tween?.kill();
    this.tween = undefined;
    super.destroy(options);
  }
}