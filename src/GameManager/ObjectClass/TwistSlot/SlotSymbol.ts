import { Spine } from '@esotericsoftware/spine-pixi-v8';
import { Container, type DestroyOptions } from 'pixi.js';
import { TwistElemantAnim, TwistSoundName, type ITwistSlotData } from '@/constant/game';
import { getAudio } from '@/AudioManager';

export interface iSlotSymbolOptions {
  item: ITwistSlotData;
  isWinItem?: boolean;
}

export class SlotSymbol extends Container {
  private spine: Spine;

  constructor(item: ITwistSlotData) {
    super();
    
    this.spine = Spine.from({
      skeleton: "spin-master.json",
      atlas: "spin-master.atlas",
      scale: 2,
    });

    this.addChild(this.spine);

    this.update({ item });
  }

  public update(options: iSlotSymbolOptions) {
    const { item, isWinItem } = options;
    this.position.y = item.posY;

    this.spine.state.clearListeners();

    if (isWinItem) {
      getAudio().sfx.play(`sounds/${TwistSoundName[item.name]}.mp3`);
      this.spine.state.setAnimation(0, TwistElemantAnim[item.name]);
      this.spine.state.addListener({
        complete: () => {
          this.spine.state.addAnimation(0, `${TwistElemantAnim[item.name]}-idle`, false, 4);
        }
      });
    } else {
      // 设置 Spine 动画，暂停在第一帧
      this.spine.state.setAnimation(0, TwistElemantAnim[item.name]).timeScale = 0;
    }
  }

  override destroy(options?: DestroyOptions): void {
    this.spine.state.clearListeners();
    this.spine.destroy({ children: true });
    super.destroy(options)
  }
}