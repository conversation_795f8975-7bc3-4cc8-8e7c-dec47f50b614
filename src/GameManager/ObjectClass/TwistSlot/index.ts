import { Container, Graphics, Sprite, Texture, type DestroyOptions } from "pixi.js";
import { TwistElement } from "@/constant/game";
import { addTick, removeTick } from "@/GameManager/GlobalTick";
import { SlotLine } from "./SlotLIne";

export interface ISlotOptions {
  isRolling: boolean;
  isAutoplay: boolean;
  winItem?: TwistElement;
}

export class TwistSlot extends Container {
  private slotLineWrapper: Container;
  private slotLineRef: SlotLine;
  private circleMask: Graphics;

  constructor (options: ISlotOptions) {
    super({ scale: 0.5, y: 7 });

    this.circleMask = new Graphics();
    this.addChild(this.circleMask);

    this.slotLineWrapper = new Container();
    this.slotLineWrapper.mask = this.circleMask;

    this.slotLineRef = new SlotLine(options);
    this.slotLineWrapper.addChild(this.slotLineRef);
    this.addChild(this.slotLineWrapper);

    this.addChild(new Sprite({
      texture: Texture.from('center.png'),
      scale: 1,
      anchor: 0.5,
    }));

    addTick(this.draw);
  }

  private draw = () => {
    this.circleMask
    .clear()
    .circle(0, 0, 277)
    .fill({ color: 0xffffff, alpha: 0.05 });
  }

  public update(options: ISlotOptions) {
    this.slotLineRef.update(options);
  }

  override destroy(options?: DestroyOptions): void {
    removeTick(this.draw);
    super.destroy(options);
  }
}