import gsap from "gsap";
import { Sprite, Texture } from "pixi.js";
import { SeparatorColors, SeparatorRadii, type CircleElement } from "@/constant/game";

const calculatePosition = (x: number, y: number, radius: number, index: number, total: number, offset: boolean) => {
  const angle = (2 * Math.PI / total) * index - Math.PI / 2 + (offset ? Math.PI / total : 0);
  return {
    x: x + radius * Math.cos(angle),
    y: y + radius * Math.sin(angle),
  };
};

export interface iSeparatorOptions {
  index: number;
  type: CircleElement;
  coeffs: number[];
  filled: boolean;
}


export class Separator extends Sprite {
  private defaultColor: string | number = 0x36434b;
  private filledColor: string | number;

  constructor (options: iSeparatorOptions) {
    const { index, type, coeffs, filled } = options;

    const totalItems = coeffs.length;

    super({
      texture: Texture.from('separator'),
      anchor: 0.5,
      scale: 0.5,
      position: calculatePosition(0, 7, SeparatorRadii[type], index + 1, totalItems, false),
      angle: (360 / totalItems) * (index + 1),
      visible: index !== totalItems - 1
    });

    this.filledColor = SeparatorColors[type];
    this.update(filled);
  }

  public update(filled: boolean) {
    gsap.delayedCall(filled ? 0.7 : 0, () => {
      this.tint = filled ? this.filledColor : this.defaultColor;
    });
  }
}
