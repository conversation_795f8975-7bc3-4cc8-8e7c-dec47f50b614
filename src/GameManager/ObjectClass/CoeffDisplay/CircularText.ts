import gsap from "gsap";
import { Container, Text, TextStyle, type DestroyOptions } from "pixi.js";

interface CircularTextOptions {
  text: string;
  radius: number;
  centerX?: number;
  centerY?: number;
  startAngleDeg?: number;
  style?: TextStyle;
}

export class CircularText extends Container {
  private text: string;
  private radius: number;
  private centerX: number;
  private centerY: number;
  private startAngleDeg: number;
  private style: TextStyle;

  private tween?: gsap.core.Tween;

  constructor({
    text,
    radius,
    centerX = 0,
    centerY = 0,
    startAngleDeg = 0,
    style = new TextStyle(),
  }: CircularTextOptions) {
    super();

    this.text = text;
    this.radius = radius;
    this.centerX = centerX;
    this.centerY = centerY;
    this.startAngleDeg = startAngleDeg;
    this.style = style;

    this.draw();
  }

  private draw() {
    this.removeChildren();

    const textStyle = new TextStyle(this.style);
    const letterSpacing = this.style?.letterSpacing || 0;
    const chars = [];
    let totalWidth = 0;

    // 计算每个字符的宽度和角度
    for (let i = 0; i < this.text.length; i++) {
      const char = this.text[i];
      const textObj = new Text({ text: char, style: textStyle });
      const width = textObj.getBounds().width;
      const angle = (width + (i < this.text.length - 1 ? letterSpacing : 0)) / this.radius;
      totalWidth += width + (i < this.text.length - 1 ? letterSpacing : 0);
      chars.push({ char, width, angle });
    }

    const totalAngle = totalWidth / this.radius;
    const startAngle = this.startAngleDeg * (Math.PI / 180);
    const totalAngleRad = totalAngle / 2;
    // 判断是否需要反转
    const isReversed =
      startAngle > (90 * Math.PI) / 180 &&
      startAngle < (260 * Math.PI) / 180 &&
      startAngle < Math.PI * 2;

    const charsToRender = isReversed ? [...chars].reverse() : chars;

    const positions = [];
    let currentAngle = startAngle - totalAngleRad;

    for (let i = 0; i < charsToRender.length; i++) {
      const { char, angle } = charsToRender[i];
      const angleMid = currentAngle + angle / 2;
      const rotation = angleMid + Math.PI / 2 + (isReversed ? Math.PI : 0);
      const x = this.centerX + this.radius * Math.cos(angleMid);
      const y = this.centerY + this.radius * Math.sin(angleMid);
      // 反转时字符顺序也要反转
      const displayChar = isReversed ? chars[chars.length - 1 - i].char : char;
      positions.push({ char: displayChar, x, y, rotation });
      currentAngle += angle;
    }

    // 渲染每个字符
    for (let i = 0; i < positions.length; i++) {
      const { char, x, y, rotation } = positions[i];
      this.addChild(new Text({
        text: char,
        style: textStyle,
        anchor: 0.5,
        x,
        y,
        rotation
      }));
    }
  }

  public setColor(color: string | number) {
    this.children.forEach(child => {
      this.tween = gsap.to(child, {
        pixi: { tint: color },
        duration: 0.25,
      });
    });
  }

  override destroy(options?: DestroyOptions): void {
    this.tween?.kill();
    super.destroy(options);
  }
}