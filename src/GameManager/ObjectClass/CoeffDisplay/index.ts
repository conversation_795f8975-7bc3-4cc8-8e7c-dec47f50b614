import { Container } from "pixi.js";
import type { CircleElement } from "@/constant/game";
import { CoeffText } from "./CoeffText";
import { Separator } from "./Separator";

export class CoeffDisplay extends Container {
  private coeffs: number[] = [];
  private coeffList: { coeffTextRef: CoeffText; separatorRef: Separator }[] = [];

  public get last() {
    return this.coeffs.length - 1
  }

  constructor(type: CircleElement, coeffs: number[], progress: number) {
    super()

    this.coeffs = coeffs;

    this.coeffList = this.coeffs.map((coeff, index) => {
      const itemContainer = new Container({ label: `${type}_${index}` });

      const coeffTextRef = new CoeffText({
        type,
        index,
        coeff,
        coeffs,
        filled: index <= progress - 1,
        isLast: index === this.last,
        bonusHighlight: progress === this.last
      });

      const separatorRef = new Separator({
        type,
        index,
        coeffs,
        filled: index <= progress - 1,
      });

      itemContainer.addChild(coeffTextRef, separatorRef);

      this.addChild(itemContainer);

      return { coeffTextRef, separatorRef };
    });
  }

  public update(progress: number) {
    this.coeffList.forEach(({ coeffTextRef, separatorRef }, index) => {
      coeffTextRef.update(index <= progress - 1, progress === this.last);
      separatorRef.update(index <= progress - 1)
    })
  }
}