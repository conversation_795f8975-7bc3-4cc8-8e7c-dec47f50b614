import gsap from "gsap";
import { Container, TextStyle, type DestroyOptions } from "pixi.js";
import { BonusColors, FilledColors, TextRadii, type CircleElement } from "@/constant/game";
import { CircularText } from "./CircularText";

export interface ICoefficientTextOptions {
  type: CircleElement;
  index: number;
  coeff: number;
  coeffs: number[];
  filled: boolean;
  isLast: boolean;
  bonusHighlight: boolean;
}

export class CoeffText extends Container {
  private type: CircleElement;
  private isLast: boolean;

  private tween?: gsap.core.Tween;

  private circularTextRef: CircularText;

  constructor(options: ICoefficientTextOptions) {
    const { type, index, coeff, coeffs, filled, isLast, bonusHighlight } = options;
    super({
      angle: -90,
      x: 2.5,
      y: 7
    });

    const anglePerItem = 360 / coeffs.length;
    const coeffText = isLast ? coeff === -1 ? 'BONUS' : `+${coeff}X` : `${coeff}X`;
    
    this.type = type;
    this.isLast = isLast;

    this.circularTextRef = new CircularText({
      text: coeffText,
      radius: TextRadii[type],
      startAngleDeg: 0.5 * anglePerItem + anglePerItem * index,
      style: new TextStyle({
        fontFamily: ['Montserrat', 'sans-serif'],
        fontSize: 34,
        fill: 0xffffff,
        fontWeight: '900',
        letterSpacing: 0.2,
      })
    })

    this.addChild(this.circularTextRef);

    this.update(filled, bonusHighlight);
  }

  public update(filled: boolean, bonusHighlight: boolean) {
    const bonusColor = BonusColors[this.type];
    const filledColor = FilledColors[this.type];
    const color = this.isLast && bonusHighlight ? bonusColor : filled ? filledColor : 0x596f7c;
    
    gsap.killTweensOf(this);
    
    this.alpha = 1;

    if (bonusHighlight && this.isLast) {
      this.tween = gsap.to(this, {
        pixi: { alpha: 0.75 },
        duration: 0.5,
        yoyo: true,
        repeat: -1,
        ease: 'sine.inOut'
      });
    }

    this.circularTextRef.setColor(color);
  }

  override destroy(options?: DestroyOptions): void {
    this.tween?.kill();
    super.destroy(options);
  }
}