import { CanvasTextMetrics, Text, TextStyle, type TextOptions } from "pixi.js"

export interface BaseTextOptions extends TextOptions {
  scale?: number;
  windowScale?: number;
  fixBlurScale?: number;
  baseFontSize?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface CalculateAdaptiveFontSizeOptions {
  baseScale: number;
  windowScale: number;
  fixBlurScale: number;
  baseFontSize: number;
  maxWidth?: number;
  maxHeight?: number;
  text: string;
  style: Partial<TextStyle>;
}

const BaseTextStyle = new TextStyle({
  align: 'center',
  fontFamily: ['Game Inter', 'sans-serif'],
  fontSize: 24,
  fontWeight: '700',
  fill: '#ffffff',
  letterSpacing: 0.5,
});

function calculateAdaptiveFontSize(options: CalculateAdaptiveFontSizeOptions) {
  const { baseScale, windowScale, fixBlurScale, baseFontSize, maxWidth, maxHeight, text, style } = options;
  console.log("====")

  // 计算初始字体大小
  let fontSize = Math.floor(windowScale * baseFontSize * fixBlurScale);
  
  // 如果没有最大宽高限制，直接返回计算的字体大小
  if (!maxWidth && !maxHeight) {
    return fontSize;
  }
  
  // 计算缩放比例
  const scaleRatio = baseScale / windowScale / fixBlurScale;
  
  // 创建临时文本样式用于测量
  const measureStyle = new TextStyle(style);
  measureStyle.fontSize = fontSize;
  
  // 测量文本尺寸
  let textMetrics = CanvasTextMetrics.measureText(text, measureStyle);
  
  // 如果文本尺寸超过最大限制，逐步减小字体大小
  while (
    (maxWidth && textMetrics.width * scaleRatio > maxWidth || 
     maxHeight && textMetrics.height * scaleRatio > maxHeight) && 
    fontSize > 0
  ) {
    fontSize -= 1;
    measureStyle.fontSize = fontSize;
    textMetrics = CanvasTextMetrics.measureText(text, measureStyle);
  }
  
  return fontSize;
}

export class ScaleText extends Text {
  private baseFontSize: number;
  private baseScale: number;
  private windowScale: number;
  private fixBlurScale: number;
  private maxWidth?: number;
  private maxHeight?: number;

  constructor(options: BaseTextOptions) {
    const textStyle = new TextStyle(options.style || BaseTextStyle);

    const baseFontSize = options.baseFontSize;
    const baseScale = options.scale || 1;
    const windowScale = options.windowScale || 1;
    const fixBlurScale = options.fixBlurScale || 1.5;

    super({
      style: textStyle,
      ...options,
    });

    this.scale.set(baseScale / windowScale / fixBlurScale)
    this.baseFontSize = (typeof baseFontSize === 'number' ? baseFontSize : this.style?.fontSize) || 24;
    this.baseScale = baseScale;
    this.windowScale = windowScale;
    this.fixBlurScale = fixBlurScale;
    this.maxWidth = options.maxWidth;
    this.maxHeight = options.maxHeight;

    this.updateStyle();
  }

  public setText(text: string): void {
    this.text = text;
    this.updateStyle();
  }

  public updateStyle(): void {
    const { text, style, baseFontSize, baseScale, windowScale, fixBlurScale, maxWidth, maxHeight } = this;

    this.style.fontSize = calculateAdaptiveFontSize({
      baseScale,
      windowScale,
      fixBlurScale,
      text,
      style,
      baseFontSize,
      maxWidth,
      maxHeight,
    });
  }
}