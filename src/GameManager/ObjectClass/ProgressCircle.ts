import { gsap } from "gsap";
import { Container, Graphics, Sprite, Texture, type DestroyOptions } from "pixi.js";
import { Colors, SeparatorRadii, TwistElementName, type CircleElement } from "@/constant/game";
import { clamp } from "@/utils/utils";

export class ProgressCircle extends Container {
  private type: CircleElement;
  private tween: gsap.core.Tween | null = null;
  private coeffs: number[];
  private progress: number = 0;
  private radius: number;
  private currentCount: number = 0;

  private maskRef   : Graphics;
  private sectionRef: Sprite;
  private tipRef    : Sprite;
 
  constructor(type: CircleElement, coeffs: number[], count: number = 0) {
    super({ y: 7, sortableChildren: true });

    this.type = type;
    this.coeffs = coeffs;
    this.radius = SeparatorRadii[type];

    this.maskRef = new Graphics({ x: this.radius / 2, y: this.radius / 2 });
    this.addChild(this.maskRef);
    this.mask = this.maskRef;

    this.sectionRef = new Sprite({
      label: `${TwistElementName[type]} Progress`,
      texture: Texture.from(`${TwistElementName[type]}Section.png`),
      anchor: 0.5,
    });
    this.addChild(this.sectionRef);

    this.tipRef = new Sprite({
      label: `${TwistElementName[type]} Tip`,
      texture: Texture.from(`${TwistElementName[type]}Tip`),
      anchor: { x: -0.4, y: 0.493 },
      scale: 0.49,
      // anchor: { x: -0.57, y: 0.49 },
      // scale: 0.5,
    });
    this.addChild(this.tipRef);

    this.update(count);
  }

  private animateTo(targetProgress: number): void {
    this.tween?.kill();

    this.visible = true;

    const initial = this.progress;

    const tweenState = { value: 0 };

    this.tween = gsap.to(tweenState, {
      value: 1,
      duration: 0.5,
      onUpdate: () => {
        this.progress = initial + tweenState.value * (targetProgress - initial);
        this.updateTip();
        this.updateMask();
      },
      onComplete: () => {
        this.progress = targetProgress;
        if (this.progress <= 0) {
          this.visible = false
        } else {
          this.updateTip();
          this.updateMask();
        }
      },
      ease: "none",
    });
  }

  private updateMask(): void {
    if (!this.maskRef) return;

    this.maskRef.clear();

    if (this.progress <= 0) return;

    this.maskRef.setStrokeStyle({
      width: 60,
      cap: 'round',
      color: 0xFFFFFF
    });

    this.maskRef.arc(
      -this.radius / 2,
      -this.radius / 2,
      this.radius,
      -Math.PI / 2,
      2 * Math.PI * this.progress - Math.PI / 2,
    );
    
    this.maskRef.stroke();
  }

  private updateTip(): void {
    if (!this.tipRef) return;
    const { x, y, rotation, tint } = this.calculateTipPosition(this.progress);
    this.tipRef.position.set(x, y);
    this.tipRef.rotation = rotation;
    this.tipRef.tint = tint;
  }

  private calculateTipPosition(progress: number): { x: number; y: number; rotation: number; tint: number } {
    const angle = 2 * Math.PI * progress - Math.PI / 2;
    const x = this.radius * Math.cos(angle);
    const y = this.radius * Math.sin(angle);
    const palette = Colors[this.type] || [];
    const tintHex = palette[Math.min(this.currentCount, Math.max(palette.length - 1, 0))] || "#ffffff";
    const tint = Number(tintHex.replace("#", "0x"));
    return { x, y, rotation: angle + Math.PI / 2, tint };
  }

  public update(count: number): void {
    const total = this.coeffs.length;
    const clamped = clamp(count, 0, total);
    const target = total === 0 ? 0 : clamp(clamped / total, 0, 1);

    // 防止偶尔会出现闪动问题
    if (clamped === this.currentCount && clamped === 0) {
      return;
    }

    this.currentCount = clamped;
    this.animateTo(target);
  }

  public override destroy(options?: DestroyOptions): void {
    this.tween?.kill();
    this.tween = null;
    this.maskRef.destroy({ children: true });
    super.destroy(options);
  }
}