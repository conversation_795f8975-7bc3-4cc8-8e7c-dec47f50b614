import { Application, CullerPlugin, extensions, ResizePlugin } from 'pixi.js';

extensions.add(CullerPlugin);
extensions.remove(ResizePlugin);

let instance: Application | null = null;

export function getEngine(): Application {
  if (!instance) return createEngine();
  return instance;
}

export function createEngine() {
  if (instance) return instance;
  instance = new Application();
  return instance;
}

export function destroyEngine() {
  if (!instance) return;
  instance.destroy(true, true);
  instance = null;
}

export function getResolution(): number {
  let resolution = Math.max(window.devicePixelRatio, 2);

  if (resolution % 1 !== 0) {
    resolution = 2;
  }

  return resolution;
}
