import { watch, type <PERSON><PERSON><PERSON><PERSON> } from "vue";
import type { Application, DestroyOptions } from "pixi.js";
import { Container, Sprite, Texture } from "pixi.js";
import { useGameAutoPlayStore, useGameSlotAnimStore, useGameStore, useMainStore, useUserStore } from "@/stores";
import { useTween } from "@/composables/useTween";
import { GameLayout } from "@/constant/index";
import { TwistElement, type CircleElement } from "@/constant/game";
import emitter from "@/utils/emitter";

import { bet, fullCashout, partCashout } from "./request";

import { ProgressCircle } from "./ObjectClass/ProgressCircle";
import { CoeffDisplay } from "./ObjectClass/CoeffDisplay";
import { TwistSlot } from "./ObjectClass/TwistSlot";
import { BonusSlot } from "./ObjectClass/BonusSlot";
import { PartCashout } from "./ObjectClass/PartCashout";
import { getAudio } from "@/AudioManager";

export class MainScreen extends Container {
  /** pixi 元素对象 */
  private app: Application;
  private fireRef: ProgressCircle;
  private earthRef: ProgressCircle;
  private waterRef: ProgressCircle;
  private fireCoeffRef: CoeffDisplay;
  private earthCoeffRef: CoeffDisplay;
  private waterCoeffRef: CoeffDisplay;
  private twistSlotRef: TwistSlot;
  private bonusSlotRef: BonusSlot;
  private partCashoutRef: PartCashout;

  private tween = useTween();
  private balancePosition: { x: number; y: number } = { x: 0, y: 0 };

  /** watcher */
  private watchProgressHandle: WatchHandle;
  private watchSlotHandle: WatchHandle;

  constructor(app: Application) {
    const { gameData, twistCoeffs } = useGameStore();
    const { currencySymbol } = useUserStore();

    super({ label: 'main-screen' });

    this.app = app;

    /** 背景 */
    this.addChild(new Sprite({ texture: Texture.from('wheel.webp'), anchor: 0.5, scale: 0.5 }));

    /** 进度条 */
    this.fireRef = new ProgressCircle(TwistElement.FIRE, twistCoeffs[TwistElement.FIRE], gameData.progressFire);
    this.earthRef = new ProgressCircle(TwistElement.EARTH, twistCoeffs[TwistElement.EARTH], gameData.progressEarth);
    this.waterRef = new ProgressCircle(TwistElement.WATER, twistCoeffs[TwistElement.WATER], gameData.progressWater);
    this.addChild(this.fireRef, this.earthRef, this.waterRef);

    /** slot */
    this.twistSlotRef = new TwistSlot({ isRolling: false, isAutoplay: false });
    this.bonusSlotRef = new BonusSlot({ isRolling: false, isBonusSlot: false });
    this.addChild(this.twistSlotRef, this.bonusSlotRef);

    /** slot icon */
    this.addChild(new Sprite({ texture: Texture.from('mini_icons'), anchor: 0.5, scale: 0.5, y: -236 }));

    /** 环形 倍率 */
    this.fireCoeffRef = new CoeffDisplay(TwistElement.FIRE, twistCoeffs[TwistElement.FIRE], gameData.progressFire);
    this.earthCoeffRef = new CoeffDisplay(TwistElement.EARTH, twistCoeffs[TwistElement.EARTH], gameData.progressEarth);
    this.waterCoeffRef = new CoeffDisplay(TwistElement.WATER, twistCoeffs[TwistElement.WATER], gameData.progressWater);
    this.addChild(this.fireCoeffRef, this.earthCoeffRef, this.waterCoeffRef);

    /** part cashout 元素(金额飞行) */
    this.partCashoutRef = new PartCashout(currencySymbol);
    this.addChild(this.partCashoutRef);

    this.resize();
    this.app.renderer.on('resize', () => this.resize());

    this.watchProgressHandle = watch(
      [
        () => gameData.progressFire,
        () => gameData.progressEarth,
        () => gameData.progressWater,
      ],
      () => this.updateProgress(),
      { immediate: true }
    );

    this.watchSlotHandle = watch(
      [
        () => useGameSlotAnimStore().isTwistRolling,
        () => useGameSlotAnimStore().isBonusRolling,
        () => useGameSlotAnimStore().isBonusSlot
      ],
      () => this.updateSlot(),
      { immediate: true }
    );

    emitter.on('handle-game-play', this.handleBet);
    emitter.on('handle-game-full-cashout', this.handleFullCashout);
    emitter.on('handle-game-part-cashout', this.handlePartCashout);
    document.addEventListener('keydown', this.handleKeyDown);
  }

  private updateProgress() {
    const { gameData } = useGameStore();
    this.fireRef.update(gameData.progressFire);
    this.earthRef.update(gameData.progressEarth);
    this.waterRef.update(gameData.progressWater);
    this.fireCoeffRef.update(gameData.progressFire);
    this.earthCoeffRef.update(gameData.progressEarth);
    this.waterCoeffRef.update(gameData.progressWater);
  }

  private updateSlot() {
    const { gameData } = useGameStore();
    const { state } = useGameAutoPlayStore();
    const { isTwistRolling, isBonusRolling, isBonusSlot } = useGameSlotAnimStore();
    this.twistSlotRef.update({
      winItem: gameData.outcome,
      isRolling: isTwistRolling,
      isAutoplay: state.processing,
    });
    this.bonusSlotRef.update({
      winItem: gameData.bonusCoeff,
      isRolling: isBonusRolling,
      isBonusSlot: isBonusSlot,
    });
  }

  private handleKeyDown = (event: KeyboardEvent) => {
    const { spaceEnable } = useMainStore();
    const { isSocketOnline, isFetchPending } = useGameStore();
    const { state } = useGameAutoPlayStore();

    if (event.code !== 'Space') return;
    if (!spaceEnable || !isSocketOnline) return;
    if (isFetchPending || state.processing) return;
    
    this.handleBet();
    event.preventDefault();
  }

  private handleBet = async () => {
    const { ok, error, data } = await bet();

    if (!ok || !data) {
      error && console.log(error);
      return;
    }

    const { twistCoeffs, circleSteps, updateBetInfo, updateBonusInfo } = useGameStore();
    const { handleBet } = useGameAutoPlayStore();

    const currCoeffs = twistCoeffs[data.element as CircleElement];
    const currStep = circleSteps[data.element as CircleElement] || 0;
    // 这里进度还没有更新，所以要 + 1(最新进度)
    const hasBonus = currCoeffs && currCoeffs.length === currStep + 1;

    await updateBetInfo(data, hasBonus);

    if (hasBonus) {
      await updateBonusInfo(data);
      this.partCashoutRef.update(data.winAmount, this.balancePosition);
    }

    // 自动投注
    handleBet() && setTimeout(() => this.handleBet(), 100);
  }

  private handleFullCashout = async () => {
    const { ok, error, data } = await fullCashout();

    if (!ok || !data) {
      error && console.log(error);
      return;
    }

    const { updateFullCashout } = useGameStore();

    updateFullCashout(data);

    getAudio().sfx.play('sounds/cashout.mp3');

    emitter.emit('play-award-animation', data.winAmount);
  }

  private handlePartCashout = async () => {
    const { ok, error, data } = await partCashout();

    if (!ok || !data) {
      error && console.log(error);
      return;
    }

    const { updatePartCashout } = useGameStore();

    updatePartCashout(data);

    getAudio().sfx.play('sounds/partCashout.mp3');

    this.partCashoutRef.update(data.winAmount, this.balancePosition);
  }

  public resize() {
    const { width, height } = this.app.renderer;
    const { portraitConfig, landscapeConfig } = GameLayout;
    const isVertical = useMainStore().isVertical;

    const sceneConfig = isVertical ? portraitConfig : landscapeConfig;

    const scaleRatio = (width / height) > (sceneConfig.width / sceneConfig.height)
                        ? height / sceneConfig.height
                        : width / sceneConfig.width;

    const centerX = width / 2;
    const centerY = (height / 2) - sceneConfig.centerYOffset * scaleRatio;

    // 获取余额元素的位置信息
    const balanceElement = document.getElementById("balance");
    const balanceRect = balanceElement?.getBoundingClientRect();

    this.balancePosition = {
      x: balanceRect ? ((balanceRect.x - centerX) / scaleRatio) + sceneConfig.balanceOffset.x : 0,
      y: balanceRect ? ((balanceRect.y - 48 - centerY) / scaleRatio) + sceneConfig.balanceOffset.y : 0
    };

    this.scale.set(scaleRatio);
    this.x = centerX;
    this.y = centerY;
  }

  public override destroy(options?: DestroyOptions): void {
    emitter.off('handle-game-play', this.handleBet);
    emitter.off('handle-game-full-cashout', this.handleFullCashout);
    emitter.off('handle-game-part-cashout', this.handlePartCashout);
    document.removeEventListener('keydown', this.handleKeyDown);
    this?.watchProgressHandle();
    this?.watchSlotHandle();
    this.tween.clean();
    super.destroy(options);
  }
}
