import { storeToRefs } from "pinia";
import { useSocketFetch } from "@/composables/useSocketFetch";
import { useGameAutoPlayStore, useGameSlotAnimStore, useGameStore, useUserStore } from "@/stores";
import { openMessage } from "@/components/ui/GMessage";
import emitter from "@/utils/emitter";
import protoRoot from '@/proto/index';
import i18n from '@/i18n'


export const bet = async (): Promise<{ ok: boolean; error?: any; data?: protoRoot.StartGameResponse }> => {
  const { gameData, gameMaxBet, gameMinBet, isFetchPending } = storeToRefs(useGameStore());
  const { isRolling } = storeToRefs(useGameSlotAnimStore());
  const { balance } = storeToRefs(useUserStore());

  if (isRolling.value || isFetchPending.value) {
    return Promise.resolve({ ok: false });
  }

  if (gameMinBet.value > gameData.value.betAmount) {
    openMessage({ message: i18n.global.t('errors.Limiting the minimum rate of {{x}}', { x: gameMinBet.value }), type: 'error' });
    emitter.emit('handle-bet-amount-error');
    return Promise.resolve({ ok: false });
  }

  if (gameMaxBet.value < gameData.value.betAmount) {
    openMessage({ message: i18n.global.t('errors.The rate exceeds the limit of {{x}}', { x: gameMaxBet.value }), type: 'error' });
    emitter.emit('handle-bet-amount-error');
    return Promise.resolve({ ok: false });
  }

  if (balance.value < gameData.value.betAmount) {
    openMessage({ message: i18n.global.t('balance_not_enough_tis'), type: 'error' });
    emitter.emit('handle-bet-amount-error');
    return Promise.resolve({ ok: false });
  }

  isFetchPending.value = true;

  try {
    const { promise } = useSocketFetch('user-bet-req', 'user-bet-res', { betAmount: gameData.value.betAmount });

    const { data } = await promise;

    isFetchPending.value = false;

    return Promise.resolve({ ok: true, data });
  } catch (error) {
    isFetchPending.value = false;

    return Promise.resolve({ ok: false, error });
  }
}

export const fullCashout = async (): Promise<{ ok: boolean; error?: any; data?: protoRoot.CashOutResponse }> => {
  const { gameData, isFetchPending } = storeToRefs(useGameStore());
  const { isRolling } = storeToRefs(useGameSlotAnimStore());
  const { state } = useGameAutoPlayStore();

  if (state.processing && !state.paused) {
    return Promise.resolve({ ok: false });
  }

  if (isRolling.value || isFetchPending.value) {
    return Promise.resolve({ ok: false });
  }

  if (gameData.value.isFinished) {
    return Promise.resolve({ ok: false });
  }

  isFetchPending.value = true;

  try {
    const { promise } = useSocketFetch('user-full-cashout-req', 'user-full-cashout-res');

    const { data } = await promise;

    isFetchPending.value = false;

    return Promise.resolve({ ok: true, data });
  } catch (error) {
    isFetchPending.value = false;

    return Promise.resolve({ ok: false, error });
  }
}

export const partCashout = async (): Promise<{ ok: boolean; error?: any; data?: protoRoot.PartCashOutResponse }> => {
  const { gameData, circleSteps, isFetchPending } = storeToRefs(useGameStore());
  const { isRolling } = storeToRefs(useGameSlotAnimStore());
  const { state } = useGameAutoPlayStore();

  if (state.processing && !state.paused) {
    return Promise.resolve({ ok: false });
  }

  if (isRolling.value || isFetchPending.value) {
    return Promise.resolve({ ok: false });
  }

  if (gameData.value.isFinished) {
    return Promise.resolve({ ok: false });
  }

  if (!Object.values(circleSteps.value).some(step => step >= 2)) {
    return Promise.resolve({ ok: false });
  }

  isFetchPending.value = true;

  try {
    const { promise } = useSocketFetch('user-part-cashout-req', 'user-part-cashout-res');

    const { data } = await promise;

    isFetchPending.value = false;

    return Promise.resolve({ ok: true, data });
  } catch (error) {
    isFetchPending.value = false;

    return Promise.resolve({ ok: false, error });
  }
}