<script setup lang="ts">
import { sleep } from "radash";
import type { Hand<PERSON> } from "mitt";
import { nextTick, onUnmounted, ref, watch, watchEffect } from "vue";
import { storeToRefs } from "pinia";
import { useI18n } from "vue-i18n";
import { useGameStore, useMainStore, useUserStore } from "@/stores";
import emitter, { type GameEvent } from "@/utils/emitter.ts";
import { SocketEventCode, SYSTEM_ERROR_CODE } from "@/constant";
import { useDisableZoom } from "@/composables/useDisableZoom.ts";
import { useOrientation } from "@/composables/useOrientation";
import { useTimerFn } from "@/composables/useTimer";
import { openMessage } from "@/components/ui/GMessage/index.ts";
import { useWebSocket } from "@/composables/useWebSocket.ts";
import protoRoot from "@/proto/index";

import InitScene from "@/components/InitScene/index.vue";
import LayoutHeader from "@/components/LayoutHeader/index.vue";
import LayoutBody from "@/components/LayoutBody/index.vue";
import GameRender from "@/components/GameRender/index.vue";
import BetBar from "@/components/BetBar/index.vue";
import LastWin from "@/components/LastWin/index.vue";
import GameNotification from "@/components/GameNotification.vue";
import AwardBox from "@/components/AwardBox.vue";


const { isSystemError } = storeToRefs(useMainStore());
const { setLayout, setSystemError, getSocketUrl } = useMainStore();
const { token } = storeToRefs(useUserStore());
const { setUserName, setNickname, setCurrency, setAvatar, setBalance, setClientSeed, setServerSeed } = useUserStore();
const { setIsSocketOnline, setIsJoinRoom, initGameInfo } = useGameStore();
const { isInitLoaded, isSocketOnline } = storeToRefs(useGameStore());

useDisableZoom();

const { t } = useI18n();

const { clean: cleanLayout } = useOrientation((isPortrait: boolean) => {
  setLayout(isPortrait ? 'y' : 'x');
});

const { start: resetSocketOnlineState, clean: cleanSocketOnlineState } = useTimerFn(() => {
  // 如果socket是连接状态并且没有系统维护或网络错误 触发掉线
  if (isSocketOnline.value && !isSystemError.value) {
    console.log('socket-no-response');
    emitter.emit('socket-no-response');
    openMessage({ type: 'error', message: t('network_error') });
  }
}, { delay: 1000 * 60 * 3 });


function setupSocket() {
  if (!token.value) {
    setSystemError(SYSTEM_ERROR_CODE.MAINTAIN);
    // openMessage({ type: 'error', message: t('the_system_is_under_maintenance') });
    return;
  }
  const { connect, subscribe, send, isConnected, disconnect, reconnect } = useWebSocket(
      getSocketUrl(),
      () => {
        setIsJoinRoom(false);
        emitter.emit('login-token-req', { token: token.value } as protoRoot.LoginTokenRequest)
      }
  );

  watchEffect(() => setIsSocketOnline(isConnected.value));

  // 订阅socket事件 通过emitter分发出去
  subscribe(SocketEventCode.HEARTBEAT, data => emitter.emit('heartbeat', data));

  subscribe(SocketEventCode.LOGIN_RES, data => emitter.emit('login-res', data));
  subscribe(SocketEventCode.PUSH_CONFLICT, data => emitter.emit('push-conflict', data));
  subscribe(SocketEventCode.PUSH_KICK_OUT, data => emitter.emit('push-kick-out', data));
  subscribe(SocketEventCode.ENTER_ROOM_RES, data => emitter.emit('enter-room-res', data));
  subscribe(SocketEventCode.EXIT_ROOM_RES, data => emitter.emit('exit-room-res', data));
  subscribe(SocketEventCode.USER_BET_RES, data => emitter.emit('user-bet-res', data));
  subscribe(SocketEventCode.USER_FULL_CASHOUT_RES, data => emitter.emit('user-full-cashout-res', data));
  subscribe(SocketEventCode.USER_PART_CASHOUT_RES, data => emitter.emit('user-part-cashout-res', data));
  subscribe(SocketEventCode.USER_UPDATE_AVATAR_RES, data => emitter.emit('user-update-avatar-res', data));
  subscribe(SocketEventCode.USER_BET_HISTORY_RES, data => emitter.emit('user-bet-history-res', data));

  const handleHeartbeat = ({ data }: DecodedMessage<protoRoot.HeartbeatResponse>) => {
    resetSocketOnlineState();
  };

  // 登录请求
  const handleLoginReq = (data: protoRoot.LoginRequest) => send(SocketEventCode.LOGIN_REQ, data);
  // token 登录请求
  const handleLoginTokenReq = (data: protoRoot.LoginTokenRequest) => send(SocketEventCode.LOGIN_TOKEN_REQ, data);
  // 登录响应
  const handleLoginRes = ({ header, data }: DecodedMessage<protoRoot.LoginResponse>) => {
    if (header.errCode || data?.errorCode) return;

    setUserName(data?.userName ?? '');
    setNickname(data?.nickName ?? '');
    setBalance(data?.gold ?? 0);
    data?.currency && setCurrency(data?.currency);
    emitter.emit('enter-room-req');
  };

  // 推送 被挤下线
  const handleConflict = () => {
    setSystemError(SYSTEM_ERROR_CODE.LOGOUT);
    // openMessage({ type: 'error', message: t('logout_tips') });
    disconnect();
  };

  // 推送 被踢
  const handleKickOut = () => {
    setSystemError(SYSTEM_ERROR_CODE.KICK_OUT);
    // openMessage({ type: 'error', message: t('kick_out_tips') });
    disconnect();
  };

  // 进入房间请求
  const handleEnterRoomReq = (data: protoRoot.EnterRoomRequest) => {
    setIsJoinRoom(false);
    send(SocketEventCode.ENTER_ROOM_REQ, data);
  };
  // 进入房间响应
  const handleEnterRoomRes = ({ header, data }: DecodedMessage<protoRoot.EnterRoomResponse>) => {
    if (header.errCode || data?.errorCode) return;
    setAvatar(data.avatar ? data.avatar + '' : '');
    setBalance(data.userGold || 0);
    setClientSeed(data.clientSeed);
    setServerSeed(data.serverSeed);
    initGameInfo(data);
    setIsJoinRoom(true);
  };

  // 退出房间请求
  const handleExitRoomReq = (data: protoRoot.ExitRoomRequest) => send(SocketEventCode.EXIT_ROOM_REQ, data);
  // 退出房间响应
  const handleExitRoomRes = ({ data }: DecodedMessage<protoRoot.ExitRoomResponse>) => {};

  // 请求 开始游戏、go
  const handleUserGoReq = (data: EmitterCallbackData<protoRoot.StartGameRequest, number | undefined>) => {
    const requestId = send(SocketEventCode.USER_BET_REQ, data.data);
    data.callback?.(requestId);
  }
  // 请求 full cashout
  const handleUserFullCashoutReq = (data: EmitterCallbackData<protoRoot.CashOutRequest, number | undefined>) => {
    const requestId = send(SocketEventCode.USER_FULL_CASHOUT_REQ, data.data);
    data.callback?.(requestId);
  }

  // 请求 part cashout
  const handleUserPartCashoutReq = (data: EmitterCallbackData<protoRoot.PartCashOutRequest, number | undefined>) => {
    const requestId = send(SocketEventCode.USER_PART_CASHOUT_REQ, data.data);
    data.callback?.(requestId);
  }

  // 请求 玩家投注历史记录
  const handleUserBetHistoryReq = (data: EmitterCallbackData<protoRoot.GetHistoryRequest, number | undefined>) => {
    const requestId = send(SocketEventCode.USER_BET_HISTORY_REQ, data.data);
    data.callback?.(requestId);
  };

  // 请求 更新头像
  const handleUpdateAvatarReq = (data: EmitterCallbackData<protoRoot.ChangeAvatarRequest, number | undefined>) => {
    const requestId = send(SocketEventCode.USER_UPDATE_AVATAR_REQ, data.data);
    data.callback?.(requestId);
  }

  // socket假死
  const handleSocketNoResponse = () => {
    disconnect();
  };

  // socket reconnect
  const handleSocketReconnect = () => {
    reconnect();
  }

  // 网络错误
  const handleNetworkError = () => {
    if (isSystemError.value) return;
    openMessage({ type: 'error', message: t('network_error') });
  };

  const socketEventsHandlerMap = {
    'heartbeat': handleHeartbeat,

    'login-req': handleLoginReq,
    'login-token-req': handleLoginTokenReq,
    'login-res': handleLoginRes,

    'push-conflict': handleConflict,

    'push-kick-out': handleKickOut,

    'enter-room-req': handleEnterRoomReq,
    'enter-room-res': handleEnterRoomRes,

    'exit-room-req': handleExitRoomReq,
    'exit-room-res': handleExitRoomRes,

    'user-bet-req': handleUserGoReq,
    'user-full-cashout-req': handleUserFullCashoutReq,
    'user-part-cashout-req': handleUserPartCashoutReq,
    'user-bet-history-req': handleUserBetHistoryReq,
    'user-update-avatar-req': handleUpdateAvatarReq,

    'socket-no-response': handleSocketNoResponse,
    'socket-reconnect': handleSocketReconnect,
    'socket-reconnect-fail': handleNetworkError
  } as const;
  const events = Object.keys(socketEventsHandlerMap) as Array<keyof typeof socketEventsHandlerMap>;

  events.forEach((key) => emitter.on(key, socketEventsHandlerMap[key] as Handler<GameEvent[typeof key]>));

  connect();

  onUnmounted(() => {
    events.forEach(key => emitter.off(key, socketEventsHandlerMap[key] as Handler<GameEvent[typeof key]>));
  });
}

setupSocket();

onUnmounted(() => {
  cleanLayout();
  cleanSocketOnlineState();
});

// 页面初始化完成
const isInitComplete = ref(false);
const isShowLoading = ref(true);
watch(isInitLoaded, async (loaded: boolean) => {
  if (loaded) {
    isInitComplete.value = true;
    await sleep(100);
    isShowLoading.value = false;
  }
}, { once: true });
</script>

<template>
  <InitScene v-if="isShowLoading" />
  <div v-if="isInitComplete" id="view-box" class="flex flex-col text-white font-semibold select-none">
    <LayoutHeader />
    <LayoutBody>
      <div class="flex size-full items-center flex-col justify-end overflow-hidden">
        <GameRender />
        <BetBar />
        <LastWin class="x:hidden" />
      </div>
    </LayoutBody>
    <GameNotification />
    <AwardBox />
  </div>
</template>

<style scoped>
#view-box {
  width: 100%;
  height: 100%;
  position: relative;
  overscroll-behavior: none;
  overflow: hidden;
}
</style>