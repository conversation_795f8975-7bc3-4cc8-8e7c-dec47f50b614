// =============================================================================
// Socket Communication Types
// =============================================================================

/**
 * Socket response data structure
 */
declare interface SocketResData {
  /** Unique message identifier */
  messageId: string;
  /** Response data payload */
  data: string;
  /** API endpoint path */
  path: string;
}

/**
 * Represents a decoded message with a header and associated data.
 *
 * @template T The type of the data contained in the decoded message.
 * @property {MessageHeader} header The header of the message.
 * @property {T} data The decoded data carried by the message.
 */
declare interface DecodedMessage<T> {
  header: MessageHeader;
  data: T;
}

/**
 * Generic emitter callback data structure
 */
declare interface EmitterCallbackData<T, K> {
  /** Optional data payload */
  data?: T;
  /** Optional callback function */
  callback?: (data: K) => void;
}

/**
 * Message handler function type
 */
declare type MessageHandler = (data: T) => void;

// =============================================================================
// Global Types
// =============================================================================

declare type ScreenOrientationType = 'portrait-primary' | 'portrait-secondary' | 'landscape-primary' | 'landscape-secondary';


// =============================================================================
// Game Types
// =============================================================================

declare interface GameData {
  /** 中奖元素 */
  outcome: number;
  /** 投注金额 */
  betAmount: number;
  /** 一个周期内总赢钱金额 */
  totalWinAmount: number;
  /** 最近一次赢钱金额 */
  lastWinAmount: number;
  /** 当前总倍率 */
  coeff: number;
  /** bonus 倍率 */
  bonusCoeff: number;
  /** 游戏进度 */
  progressFire: number;
  progressEarth: number;
  progressWater: number;
  /** 当前周期是否结束(progress全为0) */
  isFinished: boolean;
  /** 是否已经提现过 */
  isCashout: boolean;
  /** 服务端判断是否能提现 */
  isCanCashOut: boolean;
}