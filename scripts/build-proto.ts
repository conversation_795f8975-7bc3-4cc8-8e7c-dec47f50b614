#!/usr/bin/env node

import { execSync } from 'child_process';
import { readdirSync, statSync } from 'fs';
import { join, resolve } from 'path';

/**
 * 构建 Protocol Buffers 脚本
 * 自动查找 src/proto 目录下的所有 .proto 文件
 * 生成对应的 .js 和 .d.ts 文件
 */

const PROTO_DIR = resolve(process.cwd(), 'src/proto');
const OUTPUT_JS = join(PROTO_DIR, 'index.js');
const OUTPUT_DTS = join(PROTO_DIR, 'index.d.ts');

/**
 * 递归查找所有 .proto 文件
 */
function findProtoFiles(dir: string): string[] {
  const files: string[] = [];
  
  try {
    const items = readdirSync(dir);
    
    for (const item of items) {
      const fullPath = join(dir, item);
      const stat = statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 递归查找子目录
        files.push(...findProtoFiles(fullPath));
      } else if (item.endsWith('.proto')) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  
  return files;
}

/**
 * 执行命令并处理错误
 */
function execCommand(command: string, description: string): void {
  try {
    console.log(`🔄 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed successfully`);
  } catch (error) {
    console.error(`❌ Error during ${description}:`, error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
function main(): void {
  console.log('🚀 Starting Protocol Buffers build process...\n');
  
  // 查找所有 .proto 文件
  console.log('📁 Searching for .proto files...');
  const protoFiles = findProtoFiles(PROTO_DIR);
  
  if (protoFiles.length === 0) {
    console.log('⚠️  No .proto files found in src/proto directory');
    return;
  }
  
  console.log(`📄 Found ${protoFiles.length} .proto file(s):`);
  protoFiles.forEach(file => {
    console.log(`   - ${file.replace(process.cwd(), '.')}`);
  });
  console.log();
  
  // 构建 pbjs 命令
  const pbjsCommand = `pbjs -t static-module -w es6 -o "${OUTPUT_JS}" ${protoFiles.map(f => `"${f}"`).join(' ')}`;
  
  // 构建 pbts 命令
  const pbtsCommand = `pbts -o "${OUTPUT_DTS}" "${OUTPUT_JS}"`;
  
  // 执行 pbjs 命令
  execCommand(pbjsCommand, 'Generating JavaScript files from .proto files');
  
  // 执行 pbts 命令
  execCommand(pbtsCommand, 'Generating TypeScript definition files');
  
  console.log('\n🎉 Protocol Buffers build completed successfully!');
  console.log(`📦 Generated files:`);
  console.log(`   - ${OUTPUT_JS.replace(process.cwd(), '.')}`);
  console.log(`   - ${OUTPUT_DTS.replace(process.cwd(), '.')}`);
}

// 检查是否直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { main as buildProto };
