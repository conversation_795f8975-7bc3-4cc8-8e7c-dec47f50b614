# Build Proto Script

这个脚本用于自动构建 Protocol Buffers 文件。

## 功能

- 自动查找 `src/proto` 目录下的所有 `.proto` 文件（包括子目录）
- 使用 `pbjs` 将所有 `.proto` 文件编译为单个 JavaScript 文件
- 使用 `pbts` 生成对应的 TypeScript 定义文件

## 使用方法

### 方法一：使用 npm 脚本
```bash
pnpm run build-proto
```

### 方法二：直接运行
```bash
npx tsx scripts/build-proto.ts
```

## 输出文件

- `src/proto/twist.js` - 编译后的 JavaScript 文件
- `src/proto/twist.d.ts` - TypeScript 定义文件

## 依赖

- `protobufjs-cli` - 用于编译 .proto 文件
- `tsx` - 用于运行 TypeScript 脚本

## 注意事项

- 脚本会自动递归查找所有子目录中的 `.proto` 文件
- 如果找不到任何 `.proto` 文件，脚本会显示警告并退出
- 如果编译过程中出现错误，脚本会显示错误信息并退出
