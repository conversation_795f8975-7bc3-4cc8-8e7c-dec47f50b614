# build stage
FROM node:20-alpine as build-stage

# Install git and other build dependencies
RUN apk add --no-cache git openssh-client

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install --prefer-offline

# Copy git configuration and submodule files
COPY .gitmodules* ./
COPY .git* ./

# Copy source code
COPY . .

# Configure git for Docker environment
RUN git config --global --add safe.directory /app

# Initialize and update submodules with error handling
RUN if [ -f .gitmodules ] && [ -d .git ]; then \
      echo "Initializing submodules..." && \
      git submodule init && \
      git submodule update --recursive --init --force; \
    elif [ -f .gitmodules ]; then \
      echo "Warning: .gitmodules found but no .git directory. Submodules may not be available."; \
    else \
      echo "No submodules configuration found."; \
    fi

# Verify submodule content
RUN if [ -d src/i18n ]; then \
      echo "i18n submodule content:" && \
      ls -la src/i18n/; \
    else \
      echo "Warning: src/i18n directory not found"; \
    fi

# Build project
RUN npm run build-only:test

# production stage
FROM nginx:alpine as production-stage

# Copy built files from build stage
COPY --from=build-stage /app/dist /usr/share/nginx/html

# Copy nginx configuration if exists
COPY nginx.conf* /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
