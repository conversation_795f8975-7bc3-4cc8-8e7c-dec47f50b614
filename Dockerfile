# build stage
FROM node:20-alpine as build-stage

WORKDIR /app

# Install git (required for submodules)
RUN apk add --no-cache git

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install --prefer-offline

# Copy source code (including .gitmodules)
COPY . .

# Configure git for Docker environment
RUN git config --global --add safe.directory /app

# Initialize and update submodules with branch switching
ARG SUBMODULES_BRANCH=develop

RUN if [ -f .gitmodules ]; then \
      echo "Initializing submodules..." && \
      git submodule init && \
      git submodule update --recursive --init && \
      echo "Switching submodule to branch: ${SUBMODULES_BRANCH}" && \
      git submodule foreach "git checkout ${SUBMODULES_BRANCH} || git checkout -b ${SUBMODULES_BRANCH} origin/${SUBMODULES_<PERSON>ANCH} && git pull origin ${SUBMODULES_BRANCH}" && \
      echo "submodule is now on branch ${SUBMODULES_BRANCH}"; \
    else \
      echo "No .gitmodules found, skipping submodule update"; \
    fi

# Build project
RUN npm run build-only:test

# production stage
FROM node:20-alpine as production-stage

WORKDIR /app

# Copy built files from build stage
COPY --from=build-stage /app/dist /app

# Install serve globally
RUN npm install -g serve

# Expose port 3000
EXPOSE 3000

# Start static file server
CMD ["serve", "-s", "/app", "-l", "3000"]
