# syntax=docker/dockerfile:1.4
# build stage
FROM node:20-alpine as build-stage

# Install git
RUN apk add --no-cache git

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --prefer-offline

# Mount git context and update submodules
RUN --mount=source=.git,target=.git,type=bind \
    --mount=source=.gitmodules,target=.gitmodules,type=bind \
    git submodule update --init --recursive

# Copy source code
COPY . .

# Build project
RUN npm run build-only:test

# production stage  
FROM nginx:alpine as production-stage

COPY --from=build-stage /app/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
