# 克隆主项目
git clone project.git
cd project

# 初始化并更新子模块
git submodule init
git submodule update

# 或者一次性克隆包含子模块的项目
git clone --recursive project.git

# 更新子模块到最新版本
git submodule update --remote src/i18n/language

# 提交子模块引用的更新
git add src/i18n/language
git commit -m "Update i18n submodule to latest"


# 在主项目根目录切换子模块分支（所有子模块）
git submodule foreach 'git checkout develop'

# 在主项目根目录切换指定子模块（指定子模块）
git -C src/i18n/language checkout develop

# 配置子模块跟踪特定分支
git config -f .gitmodules submodule.src/i18n/language.branch develop

